import{_ as R,a as I,d as U,r as f,e as i,c as w,f as e,h as t,i as d,j as z,k as B,t as S,l as s,m as y,o as h,p as K,n as N,q as T,v as M,x as $,y as j,E as A}from"./index-BJsoK47l.js";const D=c=>(M("data-v-98f7f576"),c=c(),$(),c),F={class:"register"},H={class:"title"},L={class:"register-code"},G=["src"],J={key:0},O={key:1},Q={style:{float:"right"}},W=D(()=>d("div",{class:"el-register-footer"},[d("span",null,"Copyright © 2018-2025 ruoyi.vip All Rights Reserved.")],-1)),X={__name:"register",setup(c){const x="若依管理系统",b=I(),{proxy:k}=U(),o=f({username:"",password:"",confirmPassword:"",code:"",uuid:""}),C={username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:(l,r,a)=>{o.value.password!==r?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},V=f(""),p=f(!1),m=f(!0);function g(){k.$refs.registerRef.validate(l=>{l&&(p.value=!0,j(o.value).then(r=>{const a=o.value.username;A.alert("<font color='red'>恭喜你，您的账号 "+a+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then(()=>{b.push("/login")}).catch(()=>{})}).catch(()=>{p.value=!1,m&&v()}))})}function v(){T().then(l=>{m.value=l.captchaEnabled===void 0?!0:l.captchaEnabled,m.value&&(V.value="data:image/gif;base64,"+l.img,o.value.uuid=l.uuid)})}return v(),(l,r)=>{const a=i("svg-icon"),_=i("el-input"),u=i("el-form-item"),q=i("el-button"),E=i("router-link"),P=i("el-form");return h(),w("div",F,[e(P,{ref:"registerRef",model:s(o),rules:C,class:"register-form"},{default:t(()=>[d("h3",H,S(s(x)),1),e(u,{prop:"username"},{default:t(()=>[e(_,{modelValue:s(o).username,"onUpdate:modelValue":r[0]||(r[0]=n=>s(o).username=n),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:t(()=>[e(a,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"password"},{default:t(()=>[e(_,{modelValue:s(o).password,"onUpdate:modelValue":r[1]||(r[1]=n=>s(o).password=n),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"confirmPassword"},{default:t(()=>[e(_,{modelValue:s(o).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=n=>s(o).confirmPassword=n),type:"password",size:"large","auto-complete":"off",placeholder:"确认密码",onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),s(m)?(h(),z(u,{key:0,prop:"code"},{default:t(()=>[e(_,{size:"large",modelValue:s(o).code,"onUpdate:modelValue":r[3]||(r[3]=n=>s(o).code=n),"auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),d("div",L,[d("img",{src:s(V),onClick:v,class:"register-code-img"},null,8,G)])]),_:1})):B("",!0),e(u,{style:{width:"100%"}},{default:t(()=>[e(q,{loading:s(p),size:"large",type:"primary",style:{width:"100%"},onClick:K(g,["prevent"])},{default:t(()=>[s(p)?(h(),w("span",O,"注 册 中...")):(h(),w("span",J,"注 册"))]),_:1},8,["loading"]),d("div",Q,[e(E,{class:"link-type",to:"/login"},{default:t(()=>[N("使用已有账户登录")]),_:1})])]),_:1})]),_:1},8,["model"]),W])}}},ee=R(X,[["__scopeId","data-v-98f7f576"]]);export{ee as default};
