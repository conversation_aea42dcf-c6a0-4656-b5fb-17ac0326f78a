import request from '@/utils/request'

// 查询简历主列表
export function listResume(query) {
  return request({
    url: '/resume/list',
    method: 'post',
    data: {
      title: query.title || '',
      name: query.name || ''
    }
  })
}

// 查询简历主详细
export function getResume(resumeId) {
  return request({
    url: '/system/resume/' + resumeId,
    method: 'get'
  })
}

// 新增简历主
export function addResume(data) {
  return request({
    url: '/system/resume',
    method: 'post',
    data: data
  })
}

// 修改简历主
export function updateResume(data) {
  return request({
    url: '/system/resume',
    method: 'put',
    data: data
  })
}

// 删除简历主
export function delResume(resumeId) {
  return request({
    url: '/system/resume/' + resumeId,
    method: 'delete'
  })
}

// 审核简历
export function auditResume(resumeId, data) {
  return request({
    url: `/resume/audit/${resumeId}`,
    method: 'put',
    data: data
  })
}

// 简历详情 // /resume/detail/{resumeId}
export function resumeDetail(resumeId) {
  return request({
    url: `/resume/detail/${resumeId}`,
    method: 'get'
  })
}
