<template>
  <div class="app-container">
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ examInfo.examName }} - 学生答题详情</h1>
        <p class="page-subtitle">
          <span class="student-info">学生：{{ studentInfo.studentName }}</span>
          <span class="student-info">学号：{{ studentInfo.studentId }}</span>
          <span class="student-info">班级：{{ examInfo.className }}</span>
        </p>
      </div>
      <div class="header-actions">
        <el-button @click="handleBack">
          <el-icon><Back /></el-icon>返回
        </el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>导出详情
        </el-button>
      </div>
    </div>

    <!-- 答题概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>总分</span>
              <el-icon><Trophy /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ studentInfo.totalScore }}</div>
          <div class="card-footer">
            <span>满分：{{ examInfo.totalScore }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>正确率</span>
              <el-icon><DataLine /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ studentInfo.accuracy }}%</div>
          <div class="card-footer">
            <el-progress 
              :percentage="studentInfo.accuracy" 
              :color="getAccuracyColor(studentInfo.accuracy)"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>答题用时</span>
              <el-icon><Timer /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ formatDuration(studentInfo.duration) }}</div>
          <div class="card-footer">
            <span>考试时长：{{ formatDuration(examInfo.duration) }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>排名</span>
              <el-icon><Medal /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ studentInfo.rank }}</div>
          <div class="card-footer">
            <span>共{{ examInfo.totalStudents }}人</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 答题详情 -->
    <el-card class="detail-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>答题详情</span>
          <div class="header-actions">
            <el-radio-group v-model="questionType" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="correct">正确</el-radio-button>
              <el-radio-button label="wrong">错误</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <div class="question-list">
        <div v-for="(question, index) in filteredQuestions" :key="index" class="question-item">
          <div class="question-header">
            <div class="question-title">
              <span class="question-index">第{{ index + 1 }}题</span>
              <span class="question-type">{{ getQuestionTypeText(question.type) }}</span>
              <span class="question-score">
                <span :class="{ 'text-success': question.isCorrect, 'text-danger': !question.isCorrect }">
                  {{ question.score }}/{{ question.totalScore }}分
                </span>
              </span>
            </div>
            <div class="question-status">
              <el-tag :type="question.isCorrect ? 'success' : 'danger'" size="small">
                {{ question.isCorrect ? '正确' : '错误' }}
              </el-tag>
            </div>
          </div>
          
          <div class="question-content">
            <div class="question-text">{{ question.content }}</div>
            
            <div v-if="question.type === 'choice'" class="options-list">
              <div 
                v-for="(option, optIndex) in question.options" 
                :key="optIndex" 
                class="option-item"
                :class="{
                  'option-correct': option.isCorrect,
                  'option-selected': option.isSelected,
                  'option-wrong': option.isSelected && !option.isCorrect
                }"
              >
                <span class="option-label">{{ option.label }}</span>
                <span class="option-text">{{ option.text }}</span>
                <span v-if="option.isCorrect" class="option-icon">
                  <el-icon><Check /></el-icon>
                </span>
                <span v-if="option.isSelected && !option.isCorrect" class="option-icon">
                  <el-icon><Close /></el-icon>
                </span>
              </div>
            </div>
            
            <div v-else-if="question.type === 'judge'" class="judge-options">
              <div 
                class="option-item"
                :class="{
                  'option-correct': question.answer === 'true',
                  'option-selected': question.studentAnswer === 'true',
                  'option-wrong': question.studentAnswer === 'true' && question.answer !== 'true'
                }"
              >
                <span class="option-label">A</span>
                <span class="option-text">正确</span>
                <span v-if="question.answer === 'true'" class="option-icon">
                  <el-icon><Check /></el-icon>
                </span>
                <span v-if="question.studentAnswer === 'true' && question.answer !== 'true'" class="option-icon">
                  <el-icon><Close /></el-icon>
                </span>
              </div>
              <div 
                class="option-item"
                :class="{
                  'option-correct': question.answer === 'false',
                  'option-selected': question.studentAnswer === 'false',
                  'option-wrong': question.studentAnswer === 'false' && question.answer !== 'false'
                }"
              >
                <span class="option-label">B</span>
                <span class="option-text">错误</span>
                <span v-if="question.answer === 'false'" class="option-icon">
                  <el-icon><Check /></el-icon>
                </span>
                <span v-if="question.studentAnswer === 'false' && question.answer !== 'false'" class="option-icon">
                  <el-icon><Close /></el-icon>
                </span>
              </div>
            </div>
            
            <div v-else-if="question.type === 'fill'" class="fill-answer">
              <div class="student-answer">
                <div class="answer-label">学生答案：</div>
                <div class="answer-content">{{ question.studentAnswer || '未作答' }}</div>
              </div>
              <div class="correct-answer">
                <div class="answer-label">正确答案：</div>
                <div class="answer-content">{{ question.answer }}</div>
              </div>
            </div>
            
            <div v-if="question.explanation" class="question-explanation">
              <div class="explanation-title">解析：</div>
              <div class="explanation-content">{{ question.explanation }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Download, Trophy, DataLine, Timer, Medal, Check, Close } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 考试信息
const examInfo = ref({
  examId: '',
  examName: '',
  className: '',
  totalScore: 100,
  duration: 7200, // 2小时
  totalStudents: 10
})

// 学生信息
const studentInfo = ref({
  studentId: '',
  studentName: '',
  totalScore: 0,
  accuracy: 0,
  duration: 0,
  rank: 0
})

// 问题类型筛选
const questionType = ref('all')

// 问题列表
const questions = ref([])

// 根据筛选条件过滤问题
const filteredQuestions = computed(() => {
  if (questionType.value === 'all') {
    return questions.value
  } else if (questionType.value === 'correct') {
    return questions.value.filter(q => q.isCorrect)
  } else {
    return questions.value.filter(q => !q.isCorrect)
  }
})

// 获取问题类型文本
function getQuestionTypeText(type) {
  const typeMap = {
    'choice': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'fill': '填空题',
    'essay': '简答题'
  }
  return typeMap[type] || type
}

// 获取正确率对应的颜色
function getAccuracyColor(accuracy) {
  if (accuracy >= 80) {
    return '#67c23a'
  } else if (accuracy >= 60) {
    return '#e6a23c'
  } else {
    return '#f56c6c'
  }
}

// 格式化答题用时
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 返回按钮操作
function handleBack() {
  router.push({
    path: '/aqsystem/exams/student-list',
    query: {
      examId: examInfo.value.examId,
      examName: examInfo.value.examName,
      className: examInfo.value.className
    }
  })
}

// 导出详情
function handleExport() {
  ElMessage.success('导出成功')
}

// 模拟问题数据
const mockQuestions = [
  {
    id: 1,
    type: 'choice',
    content: '以下哪个不是JavaScript的数据类型？',
    options: [
      { label: 'A', text: 'String', isCorrect: false, isSelected: false },
      { label: 'B', text: 'Boolean', isCorrect: false, isSelected: false },
      { label: 'C', text: 'Integer', isCorrect: true, isSelected: true },
      { label: 'D', text: 'Object', isCorrect: false, isSelected: false }
    ],
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'JavaScript的数据类型包括：String、Number、Boolean、Object、Undefined、Null、Symbol和BigInt。Integer不是JavaScript的数据类型，JavaScript中的数字类型是Number。'
  },
  {
    id: 2,
    type: 'choice',
    content: '在Vue 3中，以下哪个不是组合式API的函数？',
    options: [
      { label: 'A', text: 'ref', isCorrect: false, isSelected: true },
      { label: 'B', text: 'reactive', isCorrect: false, isSelected: false },
      { label: 'C', text: 'computed', isCorrect: false, isSelected: false },
      { label: 'D', text: 'watchEffect', isCorrect: true, isSelected: false }
    ],
    score: 0,
    totalScore: 5,
    isCorrect: false,
    explanation: 'Vue 3的组合式API包括ref、reactive、computed、watch、watchEffect等函数。watchEffect是组合式API的函数，用于自动追踪依赖并在依赖变化时重新执行。'
  },
  {
    id: 3,
    type: 'judge',
    content: 'Vue 3中的响应式系统是基于Proxy实现的。',
    answer: 'true',
    studentAnswer: 'true',
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'Vue 3的响应式系统是基于JavaScript的Proxy API实现的，这比Vue 2中基于Object.defineProperty的实现更加强大和灵活。'
  },
  {
    id: 4,
    type: 'judge',
    content: '在Vue 3中，setup函数是组件选项API的一部分。',
    answer: 'false',
    studentAnswer: 'true',
    score: 0,
    totalScore: 5,
    isCorrect: false,
    explanation: 'setup函数是Vue 3组合式API的一部分，而不是选项API的一部分。选项API包括data、methods、computed等选项，而组合式API则是基于函数的API。'
  },
  {
    id: 5,
    type: 'fill',
    content: '在Vue 3中，用于创建响应式引用的函数是______。',
    answer: 'ref',
    studentAnswer: 'ref',
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'ref是Vue 3中用于创建响应式引用的函数，它可以包装任何类型的值，使其成为响应式的。'
  },
  {
    id: 6,
    type: 'fill',
    content: '在Vue 3中，用于创建响应式对象的函数是______。',
    answer: 'reactive',
    studentAnswer: 'reactive',
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'reactive是Vue 3中用于创建响应式对象的函数，它接收一个普通对象作为参数，返回该对象的响应式代理。'
  },
  {
    id: 7,
    type: 'choice',
    content: '以下哪个不是Vue 3的生命周期钩子？',
    options: [
      { label: 'A', text: 'onMounted', isCorrect: false, isSelected: false },
      { label: 'B', text: 'onUpdated', isCorrect: false, isSelected: false },
      { label: 'C', text: 'onBeforeMount', isCorrect: false, isSelected: false },
      { label: 'D', text: 'onCreated', isCorrect: true, isSelected: true }
    ],
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: '在Vue 3的组合式API中，生命周期钩子包括onBeforeMount、onMounted、onBeforeUpdate、onUpdated、onBeforeUnmount、onUnmounted等，但没有onCreated。在选项API中，created是生命周期钩子，但在组合式API中，setup函数本身就相当于created和beforeCreate。'
  },
  {
    id: 8,
    type: 'choice',
    content: '在Vue 3中，以下哪个不是内置组件？',
    options: [
      { label: 'A', text: 'Transition', isCorrect: false, isSelected: false },
      { label: 'B', text: 'Suspense', isCorrect: false, isSelected: false },
      { label: 'C', text: 'Teleport', isCorrect: false, isSelected: false },
      { label: 'D', text: 'RouterView', isCorrect: true, isSelected: true }
    ],
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'Vue 3的内置组件包括Transition、TransitionGroup、KeepAlive、Teleport、Suspense等，而RouterView是Vue Router提供的组件，不是Vue的内置组件。'
  },
  {
    id: 9,
    type: 'judge',
    content: 'Vue 3支持多根节点组件（Fragment）。',
    answer: 'true',
    studentAnswer: 'true',
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'Vue 3支持多根节点组件，也称为Fragment，这允许组件有多个根节点，而不需要额外的包装元素。'
  },
  {
    id: 10,
    type: 'fill',
    content: '在Vue 3中，用于创建计算属性的函数是______。',
    answer: 'computed',
    studentAnswer: 'computed',
    score: 5,
    totalScore: 5,
    isCorrect: true,
    explanation: 'computed是Vue 3中用于创建计算属性的函数，它接收一个getter函数作为参数，返回一个只读的响应式引用。'
  }
]
console.log('mockQuestions isExtensible:', Object.isExtensible(mockQuestions));
mockQuestions.forEach((q, i) => console.log(`mockQuestions[${i}] isExtensible:`, Object.isExtensible(q)));

onMounted(() => {
  // 从路由参数中获取考试和学生信息
  if (route.query.examId && route.query.studentId) {
    // 设置考试信息
    examInfo.value = {
      examId: route.query.examId,
      examName: route.query.examName || '期中考试',
      className: route.query.className || '计算机科学2101',
      totalScore: 100,
      duration: 7200,
      totalStudents: 10
    }
    
    // 设置学生信息
    studentInfo.value = {
      studentId: route.query.studentId,
      studentName: route.query.studentName || '张三',
      totalScore: 85,
      accuracy: 85,
      duration: 3600,
      rank: 3
    }
    
    // 设置问题列表
    questions.value = JSON.parse(JSON.stringify(mockQuestions))
  } else {
    ElMessage.warning('未获取到考试或学生信息')
  }
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  color: #606266;
  margin: 4px 0 0;
  display: flex;
  gap: 16px;
}

.student-info {
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  border-radius: 8px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin: 16px 0;
}

.card-footer {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.detail-card {
  border-radius: 8px;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.question-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.question-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-index {
  font-weight: 600;
  color: #303133;
}

.question-type {
  color: #606266;
  font-size: 14px;
}

.question-score {
  font-weight: 500;
}

.question-content {
  padding: 0 8px;
}

.question-text {
  font-size: 16px;
  color: #303133;
  margin-bottom: 16px;
  line-height: 1.6;
}

.options-list, .judge-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
}

.option-label {
  font-weight: 600;
  margin-right: 12px;
  color: #606266;
}

.option-text {
  flex: 1;
  color: #303133;
}

.option-icon {
  margin-left: 8px;
  color: #67c23a;
}

.option-correct {
  background-color: #f0f9eb;
  border-color: #c2e7b0;
}

.option-selected {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

.option-wrong {
  background-color: #fef0f0;
  border-color: #fbc4c4;
}

.fill-answer {
  margin-bottom: 16px;
}

.student-answer, .correct-answer {
  display: flex;
  margin-bottom: 8px;
}

.answer-label {
  width: 80px;
  font-weight: 500;
  color: #606266;
}

.answer-content {
  flex: 1;
  color: #303133;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.question-explanation {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.explanation-title {
  font-weight: 500;
  color: #409eff;
  margin-bottom: 8px;
}

.explanation-content {
  color: #606266;
  line-height: 1.6;
}

.text-success {
  color: #67c23a;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c;
  font-weight: 500;
}

@media (max-width: 768px) {
  .page-subtitle {
    flex-direction: column;
    gap: 4px;
  }
  
  .overview-cards .el-col {
    margin-bottom: 16px;
  }
}
</style> 