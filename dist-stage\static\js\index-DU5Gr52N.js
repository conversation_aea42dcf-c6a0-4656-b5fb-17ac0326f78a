import{Z as w,B as pe,d as me,r as p,C as ge,a5 as fe,F as ve,e as c,G as J,c as O,o as g,J as b,f as t,a4 as Y,l as a,h as n,m as _e,n as v,j as I,D as Z,t as ye,H as W,I as X,i as he}from"./index-BJsoK47l.js";function be(r){return w({url:"/system/topic/list",method:"post",data:{keyword:r.title||"",pageNum:r.pageNum,pageSize:r.pageSize}})}function Ie(r){return w({url:`/system/topic/selectById/${r}`,method:"get"})}function De(){return w({url:"/system/supervisor/list",method:"post",data:{keyword:"",pageNum:1,pageSize:100}})}function we(){return w({url:"/system/cate/list",method:"post",data:{keyword:"",pageNum:1,pageSize:100}})}function Se(r){return w({url:"/system/topic/add",method:"post",data:r})}function Ve(r){return w({url:"/system/topic/update",method:"put",data:{topicId:r.topicId,title:r.title,description:r.description,supervisorId:r.supervisorId,categoryId:r.categoryId,tag:r.tag,publishDate:r.publishDate}})}function Te(r){return w({url:`/system/topic/delete/${r}`,method:"delete"})}const ke={class:"app-container"},Ce={class:"dialog-footer"},$e=pe({name:"Topic"}),qe=Object.assign($e,{setup(r){const{proxy:m}=me(),$=p([]),h=p(!1),_=p(!0),k=p(!0),N=p([]),L=p(!0),M=p(!0),q=p(0),U=p(""),x=p([]),E=p([]),S=p(!1),ee=ge({form:{},queryParams:{pageNum:1,pageSize:10,title:null,tag:null,status:null},rules:{title:[{required:!0,message:"题目不能为空",trigger:"blur"}],description:[{required:!0,message:"描述不能为空",trigger:"blur"}],supervisorId:[{required:!0,message:"导师不能为空",trigger:"change"}],categoryId:[{required:!0,message:"类别不能为空",trigger:"change"}],tag:[{required:!0,message:"标签不能为空",trigger:"blur"}],publishDate:[{required:!0,message:"发布时间不能为空",trigger:"change"}],deadline:[{required:!0,message:"截止时间不能为空",trigger:"change"}],quota:[{required:!0,message:"名额不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:y,form:i,rules:te}=fe(ee);function D(){_.value=!0,be(y.value).then(e=>{if(!e.data||!e.data.topics){console.error("课题列表数据格式不正确:",e),_.value=!1;return}const l=e.data.topics;console.log("原始课题列表数据:",l),q.value=e.data.total;const d=[],u=new Set;l.forEach(s=>{u.has(s.topicId)||(u.add(s.topicId),d.push(s))}),$.value=d,console.log("处理后的课题列表(直接使用后端categoryName): ",$.value),_.value=!1}).catch(e=>{console.error("获取课题列表失败:",e),_.value=!1})}function Q(){return Promise.all([De().then(e=>{x.value=e.data.topics}),we().then(e=>{E.value=e.data.topics})])}ve(()=>{Q()});function le(){h.value=!1,F()}function F(){i.value={topicId:null,title:null,description:null,supervisorId:null,categoryId:null,tag:null,publishDate:null,deadline:null,quota:1,status:1,createTime:null,editTime:null,isDelete:0},m.resetForm("topicRef")}function R(){y.value.pageNum=1,D()}function oe(){m.resetForm("queryRef"),R()}function ae(e){N.value=e.map(l=>l.topicId),L.value=e.length!=1,M.value=!e.length}function ne(){F(),h.value=!0,U.value="添加选题信息"}function j(e){F();const l=e.topicId||N.value;_.value=!0,Ie(l).then(d=>{x.value.length===0||E.value.length===0?Q().then(()=>{H(d.data)}).catch(()=>{m.$modal.msgError("获取导师或类目列表失败")}).finally(()=>{_.value=!1}):(H(d.data),_.value=!1)}).catch(()=>{m.$modal.msgError("获取课题信息失败"),_.value=!1})}function H(e){console.log("Setting form data. Topic Category ID from backend:",e.categoryId,"Type:",typeof e.categoryId),i.value={topicId:e.topicId,title:e.title,description:e.description,supervisorId:e.supervisorId,categoryId:e.categoryId!==null?Number(e.categoryId):null,tag:e.tag,publishDate:e.publishDate,deadline:e.deadline,quota:e.quota,status:e.status,createTime:e.createTime,editTime:e.editTime,isDelete:e.isDelete},h.value=!0,U.value="修改选题信息",console.log("Form data set.",i.value)}function se(){S.value||m.$refs.topicRef.validate(e=>{if(e){S.value=!0;const l=new Date().toISOString().slice(0,19).replace("T"," "),d=u=>{if(!u)return null;const s=new Date(u),C=s.getFullYear(),V=("0"+(s.getMonth()+1)).slice(-2),z=("0"+s.getDate()).slice(-2),P=("0"+s.getHours()).slice(-2),f=("0"+s.getMinutes()).slice(-2),B=("0"+s.getSeconds()).slice(-2);return`${C}-${V}-${z} ${P}:${f}:${B}`};if(i.value.topicId!=null){const u={...i.value,publishDate:d(i.value.publishDate),deadline:d(i.value.deadline)};Ve(u).then(s=>{m.$modal.msgSuccess("修改成功"),h.value=!1,D()}).catch(s=>{m.$modal.msgError(s.message||"修改失败")}).finally(()=>{S.value=!1})}else{const u={...i.value,createTime:l,editTime:l,publishDate:d(i.value.publishDate),deadline:d(i.value.deadline),isDelete:0};Se(u).then(s=>{m.$modal.msgSuccess("新增成功"),h.value=!1,D()}).catch(s=>{m.$modal.msgError(s.message||"新增失败")}).finally(()=>{S.value=!1})}}})}function K(e){const l=e.topicId||N.value;m.$modal.confirm('是否确认删除选题信息编号为"'+l+'"的数据项？').then(function(){return Te(l)}).then(()=>{D(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}return D(),(e,l)=>{const d=c("el-input"),u=c("el-form-item"),s=c("el-button"),C=c("el-form"),V=c("el-col"),z=c("right-toolbar"),P=c("el-row"),f=c("el-table-column"),B=c("el-tag"),ie=c("el-table"),re=c("pagination"),A=c("el-option"),G=c("el-select"),ue=c("el-dialog"),T=J("hasPermi"),de=J("loading");return g(),O("div",ke,[b(t(C,{model:a(y),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[t(u,{label:"关键字",prop:"title"},{default:n(()=>[t(d,{modelValue:a(y).title,"onUpdate:modelValue":l[0]||(l[0]=o=>a(y).title=o),placeholder:"请输入关键字/导师/标签",clearable:"",onKeyup:_e(R,["enter"])},null,8,["modelValue"])]),_:1}),t(u,null,{default:n(()=>[t(s,{type:"primary",icon:"Search",onClick:R},{default:n(()=>[v("搜索")]),_:1}),t(s,{icon:"Refresh",onClick:oe},{default:n(()=>[v("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Y,a(k)]]),t(P,{gutter:10,class:"mb8"},{default:n(()=>[t(V,{span:1.5},{default:n(()=>[b((g(),I(s,{type:"primary",plain:"",icon:"Plus",onClick:ne},{default:n(()=>[v("新增")]),_:1})),[[T,["system:topic:add"]]])]),_:1}),t(V,{span:1.5},{default:n(()=>[b((g(),I(s,{type:"success",plain:"",icon:"Edit",disabled:a(L),onClick:j},{default:n(()=>[v("修改")]),_:1},8,["disabled"])),[[T,["system:topic:edit"]]])]),_:1}),t(V,{span:1.5},{default:n(()=>[b((g(),I(s,{type:"danger",plain:"",icon:"Delete",disabled:a(M),onClick:K},{default:n(()=>[v("删除")]),_:1},8,["disabled"])),[[T,["system:topic:remove"]]])]),_:1}),t(z,{showSearch:a(k),"onUpdate:showSearch":l[1]||(l[1]=o=>Z(k)?k.value=o:null),onQueryTable:D},null,8,["showSearch"])]),_:1}),b((g(),I(ie,{data:a($),onSelectionChange:ae},{default:n(()=>[t(f,{type:"selection",width:"55",align:"center"}),t(f,{label:"",align:"center",prop:"topicId",width:"50"}),t(f,{label:"题目",align:"center",prop:"title",width:"260"}),t(f,{label:"导师",align:"center",prop:"supervisorName"}),t(f,{label:"类别",align:"center",prop:"categoryName"}),t(f,{label:"标签",align:"center",prop:"tag"}),t(f,{label:"课题状态",align:"center",prop:"status"},{default:n(o=>[t(B,{type:o.row.status==="1"?"success":"info"},{default:n(()=>[v(ye(o.row.status==="1"?"可选":"已满"),1)]),_:2},1032,["type"])]),_:1}),t(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(o=>[b((g(),I(s,{link:"",type:"primary",icon:"Edit",onClick:ce=>j(o.row)},{default:n(()=>[v("修改")]),_:2},1032,["onClick"])),[[T,["system:topic:edit"]]]),b((g(),I(s,{link:"",type:"primary",icon:"Delete",onClick:ce=>K(o.row)},{default:n(()=>[v("删除")]),_:2},1032,["onClick"])),[[T,["system:topic:remove"]]])]),_:1})]),_:1},8,["data"])),[[de,a(_)]]),b(t(re,{total:a(q),page:a(y).pageNum,"onUpdate:page":l[2]||(l[2]=o=>a(y).pageNum=o),limit:a(y).pageSize,"onUpdate:limit":l[3]||(l[3]=o=>a(y).pageSize=o),onPagination:D},null,8,["total","page","limit"]),[[Y,a(q)>0]]),t(ue,{title:a(U),modelValue:a(h),"onUpdate:modelValue":l[9]||(l[9]=o=>Z(h)?h.value=o:null),width:"500px","append-to-body":""},{footer:n(()=>[he("div",Ce,[t(s,{type:"primary",onClick:se,loading:a(S)},{default:n(()=>[v("确 定")]),_:1},8,["loading"]),t(s,{onClick:le},{default:n(()=>[v("取 消")]),_:1})])]),default:n(()=>[t(C,{ref:"topicRef",model:a(i),rules:a(te),"label-width":"80px"},{default:n(()=>[t(u,{label:"题目",prop:"title"},{default:n(()=>[t(d,{modelValue:a(i).title,"onUpdate:modelValue":l[4]||(l[4]=o=>a(i).title=o),placeholder:"请输入题目"},null,8,["modelValue"])]),_:1}),t(u,{label:"描述",prop:"description"},{default:n(()=>[t(d,{modelValue:a(i).description,"onUpdate:modelValue":l[5]||(l[5]=o=>a(i).description=o),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),t(u,{label:"导师",prop:"supervisorId"},{default:n(()=>[t(G,{modelValue:a(i).supervisorId,"onUpdate:modelValue":l[6]||(l[6]=o=>a(i).supervisorId=o),placeholder:"请选择导师"},{default:n(()=>[(g(!0),O(W,null,X(a(x),o=>(g(),I(A,{key:o.supervisorId,label:o.supervisorName,value:o.supervisorId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"类别",prop:"categoryId"},{default:n(()=>[t(G,{modelValue:a(i).categoryId,"onUpdate:modelValue":l[7]||(l[7]=o=>a(i).categoryId=o),placeholder:"请选择类别"},{default:n(()=>[(g(!0),O(W,null,X(a(E),o=>(g(),I(A,{key:o.categoryId,label:o.categoryName,value:o.categoryId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"标签",prop:"tag"},{default:n(()=>[t(d,{modelValue:a(i).tag,"onUpdate:modelValue":l[8]||(l[8]=o=>a(i).tag=o),placeholder:"请输入标签"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{qe as default};
