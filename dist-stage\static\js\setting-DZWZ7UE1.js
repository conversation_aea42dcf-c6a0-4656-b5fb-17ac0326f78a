import{_ as Y,B as Z,a as ee,C as te,r as _,F as ae,e as r,G as le,c as k,o as m,i as v,f as o,h as n,n as c,H as z,I as B,j as C,m as oe,J as M,t as L,a4 as ne,K as s,v as se,x as re,E as ie}from"./index-BJsoK47l.js";import{b as ce,a as ue,j as pe,k as de}from"./questionnaire-D6WvR6WF.js";const me=g=>(se("data-v-639729d3"),g=g(),re(),g),fe={class:"app-container"},_e={class:"page-header"},ge=me(()=>v("h1",{class:"page-title"},"题库管理",-1)),be={class:"header-actions"},ye={class:"form-row"},ve=Z({name:"QuestionBank"}),he=Object.assign(ve,{setup(g){const I=ee(),a=te({pageNum:1,pageSize:10,type:void 0,labelId:void 0,content:void 0}),E=_([{value:"单选题",label:"单选题"},{value:"多选题",label:"多选题"},{value:"判断题",label:"判断题"},{value:"填空题",label:"填空题"},{value:"简答题",label:"简答题"}]),h=_([]),w=_(!1),f=_(0),b=_([]),u=async()=>{w.value=!0;try{const e=await ce({pageNum:a.pageNum,pageSize:a.pageSize,type:a.type,labelId:a.labelId,content:a.content});e.code===200?(console.log("获取题目列表响应:",e),e.data?(b.value=e.data.list||[],f.value=e.data.total||0):(b.value=e.rows||[],f.value=e.total||0),console.log("分页数据:",{currentPage:a.pageNum,pageSize:a.pageSize,total:f.value,list:b.value})):s.error(e.msg||"获取题目列表失败")}catch(e){console.error("获取题目列表失败:",e),s.error("获取题目列表失败")}finally{w.value=!1}},x=()=>{a.pageNum=1,u()},T=()=>{a.pageNum=1,a.type=void 0,a.labelId=void 0,a.content=void 0,x()},D=e=>{a.pageSize=e,a.pageNum=1,u()},Q=e=>{a.pageNum=e,u()},U=e=>e.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||e.type==="application/vnd.ms-excel"?e.size/1024/1024<2?!0:(s.error("文件大小不能超过 2MB!"),!1):(s.error("只能上传Excel文件!"),!1),F=async e=>{const t=e.file;if(t){const i=new FormData;i.append("file",t);try{const p=await pe(i);p.code===0?(s.success("导入成功"),u()):s.error(p.msg||"导入失败")}catch(p){console.error("导入失败:",p),s.error("导入失败，请重试")}}},j=()=>{I.push("/aqsystem/questionnaires/questionForm1")},P=e=>{I.push({path:"/aqsystem/questionnaires/questionForm1",query:{id:e.questionsId}})},K=e=>{ie.confirm("确认要删除该题目吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=await de(e.questionsId);t.code===200?(s.success("删除成功"),u()):s.error(t.msg||"删除失败")}catch(t){console.error("删除失败:",t),s.error("删除失败")}})},O=e=>({单选题:"primary",多选题:"success",判断题:"warning",填空题:"info",简答题:"danger"})[e]||"info",A=e=>({收集问卷:"primary",个性化问卷:"success"})[e]||"info",R=async()=>{try{const e=await ue();e.code===200?(console.log("问卷类型列表响应:",e),e.rows?h.value=e.rows.map(t=>({value:t.labelId,label:t.labelName})):e.data?h.value=e.data.map(t=>({value:t.id,label:t.name})):s.warning("问卷类型列表数据格式不正确")):s.error(e.msg||"获取问卷类型列表失败")}catch(e){console.error("获取问卷类型列表失败:",e),s.error("获取问卷类型列表失败")}};return ae(()=>{u(),R()}),(e,t)=>{const i=r("el-button"),p=r("el-upload"),N=r("el-option"),S=r("el-select"),y=r("el-form-item"),$=r("el-input"),G=r("el-form"),q=r("el-card"),d=r("el-table-column"),V=r("el-tag"),H=r("el-table"),J=r("pagination"),W=le("loading");return m(),k("div",fe,[v("div",_e,[ge,v("div",be,[o(p,{class:"upload-btn",action:null,"http-request":F,"show-file-list":!1,"before-upload":U,accept:".xlsx,.xls"},{default:n(()=>[o(i,{type:"primary",icon:"Upload"},{default:n(()=>[c("导入题目")]),_:1})]),_:1}),o(i,{type:"success",icon:"Plus",onClick:j},{default:n(()=>[c("添加题目")]),_:1})])]),o(q,{class:"filter-container",shadow:"hover"},{default:n(()=>[o(G,{inline:!0,model:a,class:"search-form"},{default:n(()=>[v("div",ye,[o(y,{label:"题目类型"},{default:n(()=>[o(S,{modelValue:a.type,"onUpdate:modelValue":t[0]||(t[0]=l=>a.type=l),placeholder:"所有类型",clearable:"",class:"filter-select"},{default:n(()=>[(m(!0),k(z,null,B(E.value,l=>(m(),C(N,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(y,{label:"问卷类型"},{default:n(()=>[o(S,{modelValue:a.labelId,"onUpdate:modelValue":t[1]||(t[1]=l=>a.labelId=l),placeholder:"所有类型",clearable:"",class:"filter-select"},{default:n(()=>[(m(!0),k(z,null,B(h.value,l=>(m(),C(N,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(y,{label:"题目内容"},{default:n(()=>[o($,{modelValue:a.content,"onUpdate:modelValue":t[2]||(t[2]=l=>a.content=l),placeholder:"搜索题目内容...",clearable:"","prefix-icon":"Search",onKeyup:oe(x,["enter"])},null,8,["modelValue"])]),_:1}),o(y,null,{default:n(()=>[o(i,{type:"primary",icon:"Search",onClick:x},{default:n(()=>[c("搜索")]),_:1}),o(i,{icon:"Refresh",onClick:T},{default:n(()=>[c("重置")]),_:1})]),_:1})])]),_:1},8,["model"])]),_:1}),o(q,{class:"table-container",shadow:"hover"},{default:n(()=>[M((m(),C(H,{data:b.value,"row-key":"questionsId",border:"",style:{width:"100%"}},{default:n(()=>[o(d,{type:"index",label:"序号",width:"60",align:"center"}),o(d,{prop:"content",label:"题目内容","min-width":"200","show-overflow-tooltip":""}),o(d,{prop:"type",label:"题目类型",width:"120",align:"center"},{default:n(l=>[o(V,{type:O(l.row.type),effect:"light"},{default:n(()=>[c(L(l.row.type),1)]),_:2},1032,["type"])]),_:1}),o(d,{prop:"labelName",label:"问卷类型",width:"120",align:"center"},{default:n(l=>[o(V,{type:A(l.row.labelName),effect:"light"},{default:n(()=>[c(L(l.row.labelName),1)]),_:2},1032,["type"])]),_:1}),o(d,{prop:"createdAt",label:"创建时间",width:"160",align:"center"}),o(d,{label:"操作",width:"200",align:"center"},{default:n(l=>[o(i,{type:"primary",link:"",icon:"Edit",onClick:X=>P(l.row)},{default:n(()=>[c("编辑")]),_:2},1032,["onClick"]),o(i,{type:"danger",link:"",icon:"Delete",onClick:X=>K(l.row)},{default:n(()=>[c("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,w.value]]),M(o(J,{total:f.value,page:a.pageNum,"onUpdate:page":t[3]||(t[3]=l=>a.pageNum=l),limit:a.pageSize,"onUpdate:limit":t[4]||(t[4]=l=>a.pageSize=l),onPagination:u,onSizeChange:D,onCurrentChange:Q},null,8,["total","page","limit"]),[[ne,f.value>0]])]),_:1})])}}}),ke=Y(he,[["__scopeId","data-v-639729d3"]]);export{ke as default};
