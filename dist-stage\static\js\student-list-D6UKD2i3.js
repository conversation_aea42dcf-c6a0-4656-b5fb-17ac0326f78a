import{_ as J,u as Y,a as W,r as h,C as X,F as Z,K as V,e as r,G as ee,c as E,i as u,f as e,t as p,h as t,n as v,l as g,O as te,P as ae,Q as se,Y as le,R as ne,m as T,o as x,H as oe,I as ue,J as de,j as U,k as re,M as ce,v as ie,x as me}from"./index-BJsoK47l.js";import{u as q,w as _e}from"./xlsx-DrgRuPKf.js";import{h as pe,i as fe}from"./zuoye-v_1cayzW.js";const y=b=>(ie("data-v-917bbb80"),b=b(),me(),b),he={class:"app-container"},ve={class:"page-header"},ge={class:"page-title"},be={class:"header-actions"},we={class:"stat-header"},Ne=y(()=>u("span",null,"总人数",-1)),Se={class:"stat-value"},xe={class:"stat-header"},ye=y(()=>u("span",null,"已答题",-1)),Ce={class:"stat-value"},Ie={class:"stat-header"},ke=y(()=>u("span",null,"及格人数",-1)),Ve={class:"stat-value"},qe={class:"stat-header"},Be=y(()=>u("span",null,"平均分",-1)),De={class:"stat-value"},Ee={__name:"student-list",setup(b){const w=Y(),z=W(),c=h({examId:"",examName:"",className:"",passingScore:60,totalScore:0}),f=h({totalStudents:0,submittedCount:0,passedCount:0,averageScore:0}),C=h(!1),I=h(0),m=h([]),o=X({pageNum:1,pageSize:10,studentName:void 0,studentId:void 0,status:void 0,orderByColumn:void 0,isAsc:void 0}),K=[{value:"未开始",label:"未开始"},{value:"进行中",label:"进行中"},{value:"已完成",label:"已完成"}];function L(s){switch(s){case"未开始":return"info";case"进行中":return"warning";case"已完成":return"success";default:return"info"}}function Te(s){return s}async function B(s){C.value=!0;try{if(w.query.examId){const a=(await pe(s)).data;c.value={examId:a.examId,examName:a.examName,className:a.className,passingScore:60,totalScore:a.totalScore},f.value={totalStudents:a.totalCount,submittedCount:a.submittedCount,passedCount:a.passCount,averageScore:a.averageScore},m.value=a.students}else if(w.query.assignmentId){const a=(await fe(s)).data;c.value={examId:a.assignmentId,examName:a.name,className:a.className,passingScore:60,totalScore:a.totalScore},f.value={totalStudents:a.totalCount,submittedCount:a.submittedCount,passedCount:a.passCount,averageScore:a.averageScore},m.value=a.students.map(i=>({...i,studentNo:i.studentNo||i.studentId}))}I.value=m.value.length}catch(l){console.error("获取数据失败:",l),V.error("获取数据失败")}finally{C.value=!1}}Z(()=>{const s=w.query.examId||w.query.assignmentId;s?B(s):V.warning("未获取到考试/作业信息")});function k(){if(!m.value.length)return;let s=[...m.value];o.studentName&&(s=s.filter(l=>l.studentName.includes(o.studentName))),o.studentNo&&(s=s.filter(l=>l.studentNo.includes(o.studentNo))),o.status&&(s=s.filter(l=>l.status===o.status)),m.value=s}function P(){o.studentName=void 0,o.studentId=void 0,o.status=void 0,B(c.value.examId)}function R(){const s=m.value.map(d=>({序号:d.index,姓名:d.studentName,手机号:d.studentNo,答题状态:d.status,得分:d.score||"-",提交时间:d.submitTime||"-",答题用时:d.duration||"-"})),l=q.book_new(),a=q.json_to_sheet(s);a["!cols"]=[{wch:8},{wch:10},{wch:15},{wch:10},{wch:8},{wch:20},{wch:15}],q.book_append_sheet(l,a,"学生答题情况");const i=`${c.value.className}-学生答题情况.xlsx`;_e(l,i),V.success("导出成功")}function A(s){z.push({path:"/aqsystem/exams/student-detail",query:{examId:c.value.examId,examName:c.value.examName,className:c.value.className,studentId:s.studentNo,studentName:s.studentName}})}return(s,l)=>{const a=r("el-icon"),i=r("el-button"),d=r("el-card"),N=r("el-col"),F=r("el-row"),D=r("el-input"),S=r("el-form-item"),M=r("el-option"),Q=r("el-select"),j=r("el-form"),_=r("el-table-column"),O=r("el-tag"),$=r("el-table"),G=r("pagination"),H=ee("loading");return x(),E("div",he,[u("div",ve,[u("div",null,[u("h1",ge,p(c.value.className),1)]),u("div",be,[e(i,{type:"primary",onClick:R},{default:t(()=>[e(a,null,{default:t(()=>[e(g(te))]),_:1}),v("导出数据 ")]),_:1})])]),e(F,{gutter:20,class:"stat-cards"},{default:t(()=>[e(N,{span:6},{default:t(()=>[e(d,{shadow:"hover",class:"stat-card"},{header:t(()=>[u("div",we,[Ne,e(a,null,{default:t(()=>[e(g(ae))]),_:1})])]),default:t(()=>[u("div",Se,p(f.value.totalStudents),1)]),_:1})]),_:1}),e(N,{span:6},{default:t(()=>[e(d,{shadow:"hover",class:"stat-card"},{header:t(()=>[u("div",xe,[ye,e(a,null,{default:t(()=>[e(g(se))]),_:1})])]),default:t(()=>[u("div",Ce,p(f.value.submittedCount),1)]),_:1})]),_:1}),e(N,{span:6},{default:t(()=>[e(d,{shadow:"hover",class:"stat-card"},{header:t(()=>[u("div",Ie,[ke,e(a,null,{default:t(()=>[e(g(le))]),_:1})])]),default:t(()=>[u("div",Ve,p(f.value.passedCount),1)]),_:1})]),_:1}),e(N,{span:6},{default:t(()=>[e(d,{shadow:"hover",class:"stat-card"},{header:t(()=>[u("div",qe,[Be,e(a,null,{default:t(()=>[e(g(ne))]),_:1})])]),default:t(()=>[u("div",De,p(f.value.averageScore),1)]),_:1})]),_:1})]),_:1}),e(d,{class:"filter-container",shadow:"hover"},{default:t(()=>[e(j,{inline:!0,model:o,class:"search-form"},{default:t(()=>[e(S,{label:"姓名"},{default:t(()=>[e(D,{modelValue:o.studentName,"onUpdate:modelValue":l[0]||(l[0]=n=>o.studentName=n),placeholder:"请输入姓名",clearable:"","prefix-icon":"Search",onKeyup:T(k,["enter"])},null,8,["modelValue"])]),_:1}),e(S,{label:"学号"},{default:t(()=>[e(D,{modelValue:o.studentNo,"onUpdate:modelValue":l[1]||(l[1]=n=>o.studentNo=n),placeholder:"请输入学号",clearable:"","prefix-icon":"Search",onKeyup:T(k,["enter"])},null,8,["modelValue"])]),_:1}),e(S,{label:"答题状态"},{default:t(()=>[e(Q,{modelValue:o.status,"onUpdate:modelValue":l[2]||(l[2]=n=>o.status=n),placeholder:"请选择状态",clearable:"",class:"filter-select"},{default:t(()=>[(x(),E(oe,null,ue(K,n=>e(M,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(S,{class:"search-buttons"},{default:t(()=>[e(i,{type:"primary",icon:"Search",onClick:k},{default:t(()=>[v("搜索")]),_:1}),e(i,{icon:"Refresh",onClick:P},{default:t(()=>[v("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(d,{class:"box-card",shadow:"hover"},{default:t(()=>[de((x(),U($,{data:m.value,border:"",style:{width:"100%"},onSortChange:s.handleSortChange},{default:t(()=>[e(_,{type:"index",label:"序号",width:"80",align:"center"}),e(_,{prop:"studentName",label:"姓名","min-width":"100",align:"center"}),e(_,{prop:"studentNo",label:"学号","min-width":"120",align:"center"}),e(_,{prop:"status",label:"答题状态",width:"100",align:"center"},{default:t(n=>[e(O,{type:L(n.row.status)},{default:t(()=>[v(p(n.row.status),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"score",label:"得分",width:"100",align:"center",sortable:""},{default:t(n=>[u("span",{class:ce({"text-success":n.row.score>=c.value.passingScore,"text-danger":n.row.score<c.value.passingScore})},p(n.row.score||"-"),3)]),_:1}),e(_,{prop:"submitTime",label:"提交时间","min-width":"160",align:"center"}),e(_,{prop:"duration",label:"答题用时",width:"120",align:"center",sortable:""}),e(_,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(n=>[e(i,{type:"primary",link:"",icon:"View",onClick:Ue=>A(n.row)},{default:t(()=>[v("查看详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","onSortChange"])),[[H,C.value]]),I.value>0?(x(),U(G,{key:0,total:I.value,page:o.pageNum,"onUpdate:page":l[3]||(l[3]=n=>o.pageNum=n),limit:o.pageSize,"onUpdate:limit":l[4]||(l[4]=n=>o.pageSize=n),onPagination:s.getList},null,8,["total","page","limit","onPagination"])):re("",!0)]),_:1})])}}},Pe=J(Ee,[["__scopeId","data-v-917bbb80"]]);export{Pe as default};
