import request from '@/utils/request'

// 查询问卷管理列表
export function listQuestionnaires(query) {
  return request({
    url: '/aqsystem/questionnaires/list',
    method: 'get',
    params: query
  })
}

// 查询问卷管理详细
export function getQuestionnaires(questionnaireId) {
  return request({
    url: '/aqsystem/questionnaires/' + questionnaireId,
    method: 'get'
  })
}

// 新增问卷管理
export function addQuestionnaires(data) {
  return request({
    url: '/aqsystem/questionnaires',
    method: 'post',
    data: data
  })
}

// 修改问卷管理
export function updateQuestionnaires(data) {
  return request({
    url: '/aqsystem/questionnaires',
    method: 'put',
    data: data
  })
}

// 删除问卷管理
export function delQuestionnaires(questionnaireId) {
  return request({
    url: '/aqsystem/questionnaires/' + questionnaireId,
    method: 'delete'
  })
}
