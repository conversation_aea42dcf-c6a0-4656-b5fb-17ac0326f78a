<template>
  <div class="resume-template template-3">
    <div class="resume-container">
      <div>
        <!-- 简历头部 -->
        <div class="header-content">
          <div class="header-text">
            <div class="resume-title">
              {{ resume && resume.modules && resume.modules.basic ? (resume.modules.basic.name || '姓名') : '姓名' }}
            </div>
            <div class="basic-info-section">
              <div class="basic-info-grid">
                <div class="info-item">
                  <span class="info-label">姓名：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.name || '' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">年龄：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.age || '' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">电话：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.phone || '' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">邮箱：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.email || '' }}</span>
                </div>
              </div>
              <div class="basic-info-grid">
                <div class="info-item">
                  <span class="info-label">性别：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.gender || '' }}</span>
                </div>
                <div v-if="resume?.modules?.basic?.hometown" class="info-item" style="margin-left: 20px">
                  <span class="info-label">籍贯：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.hometown }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">求职意向：</span>
                  <span class="info-value">{{ resume?.modules?.basic?.jobObjective || '' }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="photo-container" v-if="resume.modules.basic?.avatar">
            <img class="photo" :src="resume.modules.basic.avatar" alt="头像" />
          </div>
        </div>
      </div>
    </div>

    <!-- 教育经历 -->
    <div v-if="hasEducation" class="resume-section">
      <div class="section-header">
        <h2>教育经历</h2>
      </div>
      <div class="education-content">
        <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
          <div class="edu-header">
            <div class="edu-date">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</div>
            <div class="edu-school">{{ edu.school }}</div>
            <div>{{ edu.degree }}，{{ edu.major }}</div>
          </div>
          <div class="edu-info">
            <div v-if="edu.courses" class="edu-courses">
              <span class="courses-label">主修课程：</span>
              <MdEditor :modelValue="edu.courses" previewOnly />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作经验 -->
    <div v-if="hasWork" class="resume-section">
      <div class="section-header">
        <h2>工作经验</h2>
      </div>
      <div class="work-content">
        <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
          <div class="work-header">
            <span class="work-time">{{ formatDate(work.time[0]) }} - {{ formatDate(work.time[1]) || '至今' }}</span>
            <div class="work-company">{{ work.company }}</div>
            <div class="work-position">{{ work.position }}</div>
          </div>
          <div class="work-description" v-html="formatContent(work.description)"></div>
        </div>
      </div>
    </div>

    <!-- 项目经验 -->
    <div v-if="hasProjects" class="resume-section">
      <div class="section-header">
        <h2>项目经验</h2>
      </div>
      <div class="work-content">
        <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
          <div class="work-header">
            <div class="project-date">{{ project.time[0] }} - {{ project.time[1] || '至今'}}</div>
            <div class="project-title">{{ project.name }}</div>
            <div class="project-role">{{ project.role }}</div>
          </div>
          <div class="project-description" v-html="formatContent(project.description)"></div>
        </div>
      </div>
    </div>

    <!-- 练手项目 -->
    <div v-if="hasPractices" class="resume-section">
      <div class="section-header">
        <h2>练手项目</h2>
      </div>
      <div class="work-content">
        <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
          <div class="work-header">
            <!-- <div class="project-date">{{ practice.time[0] }} - {{ practice.time[1] || '至今' }}</div> -->
            <div class="project-time">{{ formatDate(practice.time[0]) }} - {{ formatDate(practice.time[1]) || '至今' }}</div>
            <div class="project-title">{{ practice.name }}</div>
            <div class="project-role">{{ practice.role }}</div>
          </div>
          <div class="project-url">项目地址：{{ practice.url }}</div>
          <div class="project-description" v-html="formatContent(practice.description)"></div>
        </div>
      </div>
    </div>

    <!-- 技能特长 -->
    <div v-if="hasSkills" class="resume-section">
      <div class="section-header">
        <h2>技能特长</h2>
      </div>
      <div class="skills-content">
        <div class="skills-description" v-if="hasSkillsWithDescriptions">
          <div v-for="(skill, index) in resume.modules.skills" :key="'desc-'+index" class="skill-description-item"
               v-show="skill.description">
            <div class="skill-description-body" v-html="formatContent(skill.description)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 证书奖项 -->
    <div v-if="hasCertificates" class="resume-section">
      <div class="section-header">
        <h2>荣誉证书</h2>
      </div>
      <div class="certificate-content">
        <MdEditor :modelValue="typeof resume.modules.certificates === 'string' ? 
          resume.modules.certificates : 
          (typeof resume.modules.certificates === 'object' ? resume.modules.certificates.certificateName : '')"
          previewOnly />
      </div>
    </div>

    <!-- 校园经历 -->
    <div v-if="hasCampus" class="resume-section">
      <div class="section-header">
        <h2>校园经历</h2>
      </div>
      <div class="campus-content" v-html="formattedCampus"></div>
    </div>

    <!-- 兴趣爱好 -->
    <div v-if="hasInterests" class="resume-section">
      <div class="section-header">
        <h2>兴趣爱好</h2>
      </div>
      <div class="interests-content" v-html="formattedInterests"></div>
    </div>

    <!-- 个人评价 -->
    <div v-if="hasEvaluation" class="resume-section">
      <div class="section-header">
        <h2>个人评价</h2>
      </div>
      <div class="evaluation-content" v-html="formattedEvaluation"></div>
    </div>
  </div>
</template>

<script setup>
import {computed, toRefs, watch} from 'vue';
import MdEditor from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 简单的Markdown转HTML处理
  return content
      // 处理标题
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
      // 处理加粗和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理列表
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
      // 处理段落
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';

  try {
    const dateObj = new Date(date);

    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      // 如果不是有效的日期对象，尝试作为字符串直接处理，提取 YYYY-MM
      const match = date.match(/^(\d{4}[-\.]\d{2})/);
      if (match && match[1]) {
          return match[1]; // Return YYYY-MM from string
      }
      return date; // Return original string if parsing and regex fail
    }

    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');

    return `${year}-${month}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return date; // Return original string in case of error
  }
};

// u4f7fu7528toRefsu589eu5f3au54cdu5e94u5f0f
const {resume} = toRefs(props);

// u6dfbu52a0u76d1u542cu6765u8c03u8bd5u7ec4u4ef6u6e32u67d3
watch(() => resume.value, (newVal, oldVal) => {
  console.log('Template3 - resume prop changed');
}, {deep: true});

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasSkillsWithDescriptions = computed(() => {
  if (!props.resume.modules || !props.resume.modules.skills) return false;
  return props.resume.modules.skills.some(skill => skill.description && skill.description.trim() !== '');
});
const hasCertificates = computed(() => {
  if (!props.resume.modules || !props.resume.modules.certificates) return false;
  const cert = props.resume.modules.certificates;
  return typeof cert === 'string' ? cert.trim() !== '' : 
         (typeof cert === 'object' && cert.certificateName && cert.certificateName.trim() !== '');
});
const hasCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return false;
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? campus.trim() !== '' : 
         (typeof campus === 'object' && campus.description && campus.description.trim() !== '');
});
const hasInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return false;
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? interests.trim() !== '' : 
         (typeof interests === 'object' && interests.description && interests.description.trim() !== '');
});
const hasEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return false;
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? evaluation.trim() !== '' : 
         (typeof evaluation === 'object' && evaluation.description && evaluation.description.trim() !== '');
});
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化工作描述为列表项
const getWorkItems = (description) => {
  if (!description) return [];
  return description.split('\n').filter(item => item.trim() !== '');
};

// 格式化项目描述为列表项
const getProjectItems = (description) => {
  if (!description) return [];
  return description.split('\n').filter(item => item.trim() !== '');
};

// 格式化证书奖项为列表项
const getCertificateItems = (certificates) => {
  if (!certificates || typeof certificates === 'object') return [];
  return certificates.split('\n').filter(item => item.trim() !== '');
};

// 根据技能等级获取文本描述
const getSkillLevelText = (level) => {
  switch (level) {
    case 1:
      return '了解';
    case 2:
      return '掌握';
    case 3:
      return '熟悉';
    case 4:
      return '精通';
    case 5:
      return '专家';
    default:
      return '了解';
  }
};

// 获取最高学历
const getHighestDegree = () => {
  if (!props.resume || !props.resume.modules || !props.resume.modules.education || !props.resume.modules.education.length) {
    return '未填写';
  }

  const degreeOrder = {
    '博士': 5,
    '硕士': 4,
    '本科': 3,
    '大专': 2,
    '高中': 1
  };

  let highestDegree = '未填写';
  let highestValue = 0;

  props.resume.modules.education.forEach(edu => {
    if (edu.degree && degreeOrder[edu.degree] > highestValue) {
      highestValue = degreeOrder[edu.degree];
      highestDegree = edu.degree;
    }
  });

  return highestDegree;
};

// 获取带有文本级别的技能列表
const getSkillsWithLabels = computed(() => {
  if (!props.resume.modules || !props.resume.modules.skills || !props.resume.modules.skills.length) return [];
  return props.resume.modules.skills.map(skill => ({
    ...skill,
    levelText: getSkillLevelText(skill.level)
  }));
});

// 格式化内容
const formattedCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return '';
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? formatContent(campus) : 
         (typeof campus === 'object' && campus.description ? formatContent(campus.description) : '');
});

const formattedInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return '';
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? formatContent(interests) : 
         (typeof interests === 'object' && interests.description ? formatContent(interests.description) : '');
});

const formattedEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return '';
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? formatContent(evaluation) : 
         (typeof evaluation === 'object' && evaluation.description ? formatContent(evaluation.description) : '');
});

// 添加计算较亮的颜色变体函数
const getLighterColor = (hexColor) => {
  if (!hexColor) return '#2ecc71'; // 默认备用颜色

  // 将十六进制颜色转换为RGB
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);

  // 亮化颜色（简单方法：将RGB值调高）
  const lighterR = Math.min(255, r + 20);
  const lighterG = Math.min(255, g + 40);
  const lighterB = Math.min(255, b + 20);

  // 转回十六进制格式
  return '#' +
      lighterR.toString(16).padStart(2, '0') +
      lighterG.toString(16).padStart(2, '0') +
      lighterB.toString(16).padStart(2, '0');
};
</script>

<style scoped>
.resume-template {
  width: 100%;
  background: white;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
  padding: 15px;
}

/* Added container for padding */
.resume-container {
  padding: 0 15px;
}

/* New class for the header content container */
.header-content {
  display: flex;
  justify-content: space-between; /* Space out the text and photo */
  align-items: center; /* Vertically align items */
  margin-bottom: 5px;
  padding-bottom: 5px;
}

.header-text {
  flex-grow: 1; /* Allow text to take available space */
  margin-right: 20px; /* Add space between text and photo */
}


.resume-title {
  font-size: 24px;
  font-weight: bold;
  color: #000;
}

.job-objective {
  font-size: 16px;
  color: #000000;
  margin-top: 5px;
  font-weight: bold; /* 添加字体加粗 */
}

.basic-info-section {
  display: flex;
  align-items: center;
  gap: 15px; /* 信息区域与分隔符的间距 */
}

.basic-info-grid {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  flex-wrap: wrap; /* 允许换行 */
  line-height: 1.8;
}

/* 单个信息项 */
.info-item {
  display: inline-flex; /* 内联弹性容器 */
  margin: 0 10px 5px 0; /* 项之间的间距 */
  white-space: nowrap; /* 防止内部内容换行 */
}

.info-label {
  font-weight: bold;
  margin-right: 5px;
}

.info-value {
  color: #333;
}


.photo-container {
  width: 80px;
  height: 100px;
  margin-left: 5px;
  overflow: hidden;
  border: 1px solid #ddd;
  flex-shrink: 0;
}

.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.resume-section {
  margin-bottom: 10px;
}

.section-header {
  margin-bottom: 5px;
  background-color: #ffffff;
  padding: 4px 10px;
  display: flex;
  align-items: center;
}

.section-header h2 {
  font-size: 15px;
  color: #FF0000;
  margin: 0;
  font-weight: bold;
  flex-grow: 1;
  border-bottom: 1px solid #FF0000; /* 底部边框作为分割线 */
  padding-bottom: 3px; /* 文字与线的间距 */
}

/* Style for the arrow */
.section-header::after {
  color: v-bind('resume.themeColor || "#3498db"');
  font-size: 16px;
  margin-left: 5px;
}

.evaluation-content,
.certificate-content,
.campus-content,
.interests-content,
.work-content,
.project-content,
.skills-content,
.education-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 10px;
}

.work-item,
.education-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.project-header,
.work-header,
.edu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

.edu-header > div:nth-child(1) {
  flex-basis: 30%; /* 第一个元素占30% */
  text-align: left;
}

.edu-header > div:nth-child(2) {
  flex-basis: 40%; /* 第二个元素占40% */
  text-align: center;
}

.edu-header > div:nth-child(3) {
  flex-basis: 30%; /* 第三个元素占30% */
  text-align: right;
}

.work-header > div:nth-child(1) {
  flex-basis: 30%; /* 第一个元素占30% */
  text-align: left;
}

.work-header > div:nth-child(2) {
  flex-basis: 40%; /* 第二个元素占40% */
  text-align: center;
}

.work-header > div:nth-child(3) {
  flex-basis: 30%; /* 第三个元素占30% */
  text-align: right;
}


.edu-school {
  font-weight: bold;
  color: #333;
  font-size: 17px;
}

.project-date,
.work-time,
.edu-time {
  color: #000000;
  margin-bottom: 3px;
  font-weight: bold;
}

.edu-info {
  color: #666;
  font-size: 1em;
}

.work-company {
  font-weight: bold;
  margin-bottom: 3px;
  font-size: 17px;
}

.work-position {
  color: #333;
  margin-bottom: 8px;
}

.skills-description,
.project-description,
.work-description {
  line-height: 1.4;
  text-align: justify;
}

.work-description li {
  margin-bottom: 3px;
}

.projects-section {
  margin-bottom: 10px;
}

.project-item {
  margin-bottom: 10px;
}


.project-time {
  color: #000000;
  margin-bottom: 3px;
  font-weight: bold;
}

.project-name {
  font-weight: bold;
  margin-bottom: 3px;
}

.project-role {
  color: #333;
  margin-bottom: 8px;
}

.project-url {
  font-weight: normal;
}


.project-description li {
  margin-bottom: 3px;
}


.skill-description-item {
  margin-bottom: 1px;
  display: flex;
}

.skill-description-body {
  font-size: 12px;
  color: #000000;
  line-height: 1.4;
  text-align: justify;
  white-space: pre-line;
}


.language-skill-bars {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.language-skill {
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-name {
  width: 60px;
  font-weight: bold;
}

.language-bar-container {
  flex: 1;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.language-bar {
  height: 100%;
  width: 80%;
  background-color: v-bind('resume.themeColor || "#3498db"');
  border-radius: 4px;
}

.language-level-text {
  width: 30px;
  text-align: right;
  color: #666;
  font-size: 0.8em;
}

.list-content {
  padding-left: 20px;
  margin: 0;
}

.list-content li {
  margin-bottom: 3px;
  line-height: 1.4;
}

.text-content {
  line-height: 1.3;
  text-align: justify;
}

.text-content p {
  margin: 0 0 3px 0;
}

/* Removed .section-title - using .section-header h2 instead */

.header-info {
  border-bottom: 2px solid v-bind('resume.themeColor || "#3498db"');
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.skill-level-fill {
  height: 100%;
  background-color: v-bind('resume.themeColor || "#3498db"');
}

/* MdPreview Styles - Adjusted for list items */
:deep(.md-preview) {
  background: none;
  padding: 0;
}

:deep(.md-preview-html) {
  padding: 0;
}

:deep(.md-preview-html p) {
  margin: 0 0 3px 0;
}

:deep(.md-preview-html ul) {
  margin: 0;
  padding-left: 15px;
}

:deep(.md-preview-html li) {
  margin-bottom: 3px;
  line-height: 1.4;
}

:deep(.md-preview-html strong) {
  font-weight: bold;
}

:deep(.md-preview-html em) {
  font-style: italic;
}

:deep(.md-preview-html h1) {
  font-size: 1.4em;
  margin: 0.4em 0;
}

:deep(.md-preview-html h2) {
  font-size: 1.2em;
  margin: 0.4em 0;
}

:deep(.md-preview-html h3) {
  font-size: 1em;
  margin: 0.4em 0;
}
</style> 