<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:project:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:project:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:project:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:project:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mergedProjectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="30" align="center" />
      <!-- <el-table-column label="项目ID" width="80" align="center" prop="proId" /> -->
      <el-table-column label="项目名称" align="center" prop="name" width="140" />
      <el-table-column label="职位类别" width="160" align="center" prop="catName" />
      <el-table-column label="项目内容" align="center" width="300">
        <template #default="scope">
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 400px; white-space: pre-wrap;">
                <h4>项目内容详情</h4>
                <div v-if="scope.row.contents && scope.row.contents.length > 0">
                  <div v-for="(item, index) in scope.row.contents" :key="index">
                    {{ index + 1 }}. {{ item.text }}
                    <div v-if="item.questions && item.questions.length > 0" style="margin-left: 20px; color: #666;">
                      <div v-for="(question, qIndex) in item.questions" :key="qIndex">
                        - {{ question.question }}
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else>暂无详细说明</div>
              </div>
            </template>
            <div class="text-ellipsis">{{ scope.row.firstContent }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="项目问题" align="center" width="300">
        <template #default="scope">
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 400px; white-space: pre-wrap;">
                <h4>项目问题</h4>
                <div v-if="scope.row.questionsList && scope.row.questionsList.length > 0">
                  <div v-for="(item, index) in scope.row.questionsList" :key="index">
                    {{ index + 1 }}. {{ item.question }}
                    <div v-if="item.answer" style="margin-left: 20px; color: #666;">
                      答案: {{ item.answer }}
                    </div>
                  </div>
                </div>
                <div v-else>暂无问题</div>
              </div>
            </template>
            <div class="text-ellipsis">
              {{ scope.row.questionsList && scope.row.questionsList.length > 0 ? scope.row.questionsList[0].question : '暂无问题' }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:project:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:project:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目对话框 -->
    <el-dialog :title="title" v-model="open" width="1080px" append-to-body>
      <el-form ref="projectRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="职位类别" prop="catName">
          <el-select 
            v-model="form.catName" 
            placeholder="请选择职位类别" 
            clearable
            @change="handleCategoryChange"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目内容" prop="contents">
          <div class="detail-container">
            <div class="detail-input" style="margin-top: 20px;">
              <MdEditor
                v-model="currentContent"
                language="zh-CN"
                preview-theme="default"
                code-theme="github"
                style="height: 200px"
              />
              <el-button type="primary" @click="handleAddContent" style="margin-top: 10px">添加说明</el-button>
            </div>
          </div>
          <div class="detail-list" v-if="tempContents && tempContents.length > 0">
            <h4>已添加的内容：</h4>
            <div v-for="(item, index) in tempContents" :key="index" class="detail-item">
              <div class="detail-index">{{ index + 1 }}.</div>
              <div class="detail-content">
                <div>{{item.text}}</div>
                <div v-if="item.questions && item.questions.length > 0" class="content-questions">
                  <h5>相关问题：</h5>
                  <div v-for="(question, qIndex) in item.questions" :key="qIndex" class="question-item">
                    <div class="question-content">
                      <div class="question-text">{{ question.question }}</div>
                      <div v-if="question.answer" class="answer-text">
                        <el-collapse>
                          <el-collapse-item title="查看答案">
                            <div style="white-space: pre-wrap;">{{ question.answer }}</div>
                          </el-collapse-item>
                        </el-collapse>
                      </div>
                    </div>
                    <div class="question-actions">
                      <el-button link type="primary" @click="handleEditQuestion(question, qIndex, index)">编辑</el-button>
                      <el-button link type="primary" @click="handleEditAnswer(question, qIndex, index)">编辑答案</el-button>
                      <el-button link type="danger" @click="handleRemoveQuestion(qIndex, index)">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="detail-actions">
                <el-button link type="primary" @click="handleEditContent(item, index)">编辑</el-button>
                <el-button link type="primary" @click="handleShowQuestionEditor(index)">编辑问题</el-button>
                <el-button link type="primary" @click="handleGenerateQuestions(item)">生成问题</el-button>
                <el-button link type="danger" @click="handleRemoveContent(index)">删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="项目问题" prop="questions" v-if="showQuestionEditor">
          <div class="question-editor">
            <div v-show="editorMode === 'question'" class="editor-section">
              <h4>问题内容</h4>
              <MdEditor
                v-model="currentQuestion"
                language="zh-CN"
                preview-theme="default"
                code-theme="github"
                style="height: 200px"
              />
              <el-button type="primary" @click="handleAddQuestion" style="margin-top: 10px">添加问题</el-button>
            </div>
            <div v-show="editorMode === 'answer'" class="editor-section">
              <h4>答案内容</h4>
              <MdEditor
                v-model="currentAnswer"
                language="zh-CN"
                preview-theme="default"
                code-theme="github"
                style="height: 200px"
              />
              <el-button type="primary" @click="handleSaveAnswer" style="margin-top: 10px">保存答案</el-button>
            </div>
          </div>
        </el-form-item>
        <!-- <el-form-item label="已生成的问题" v-if="generatedQuestions && generatedQuestions.length > 0">
          <div class="generated-questions">
            <h4>已生成的问题：</h4>
            <div v-for="(item, index) in generatedQuestions" :key="item.id || index" class="detail-item">
              <div class="detail-index">{{ index + 1 }}.</div>
              <div class="detail-content">
                <div class="question-text">{{item.question}}</div>
                <div class="answer-text">
                  <el-collapse>
                    <el-collapse-item title="查看答案">
                      <div style="white-space: pre-wrap;">{{item.answer}}</div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
              <div class="detail-actions">
                <el-button link type="primary" @click="handleAddGeneratedQuestion(item)">添加到问题列表</el-button>
              </div>
            </div>
          </div>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Project">
import MdEditor from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import { listProject, getProject, delProject, addProject, updateProject } from "@/api/interview/project"
import { listCategory } from "@/api/interview/category"
import request from '@/utils/request'
import { selectByContent } from "@/api/interview/project"
const { proxy } = getCurrentInstance()

const projectList = ref(JSON.parse(JSON.stringify([])))
const mergedProjectList = ref(JSON.parse(JSON.stringify([])))
const categoryOptions = ref(JSON.parse(JSON.stringify([])))
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const currentContent = ref(JSON.parse(JSON.stringify('')))
const currentQuestion = ref(JSON.parse(JSON.stringify('')))
const showQuestionEditor = ref(JSON.parse(JSON.stringify(false)))
const generatedQuestions = ref(JSON.parse(JSON.stringify([])))
const currentContentIndex = ref(null)
const questionDialogTitle = ref('编辑问题')
const editorMode = ref('question')
const currentAnswer = ref('')
const editingQuestionIndex = ref(null)
const editingContentIndex = ref(null)

const data = reactive({
  form: {},
  tempContents: [],
  tempQuestions: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: null
  },
  rules: {
    name: [
      { required: true, message: "项目名称不能为空", trigger: "blur" }
    ],
    startTime: [
      { required: true, message: "开始时间不能为空", trigger: "blur" }
    ],
    endTime: [
      { required: true, message: "结束时间不能为空", trigger: "blur" }
    ],
    catName: [
      { required: true, message: "职位类别不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules, tempContents, tempQuestions } = toRefs(data)

/** 查询项目列表 */
function getList() {
  loading.value = true
  listProject(queryParams.value).then(response => {
    console.log('获取到的项目列表数据:', response)
    if (response.code === 0) {
      projectList.value = response.data.records
      // 处理数据以匹配表格显示需求
      mergedProjectList.value = projectList.value.map(item => {
        // 获取所有内容的问题列表
        const allQuestions = item.contents.reduce((acc, content) => {
          if (content.questions && content.questions.length > 0) {
            acc.push(...content.questions)
          }
          return acc
        }, [])

        return {
          ...item,
          // 保留原始的contents数据
          contents: item.contents || [],
          // 提取第一个content的text用于显示
          firstContent: item.contents && item.contents.length > 0 ? item.contents[0].text : '暂无详细说明',
          // 合并所有问题
          questionsList: allQuestions
        }
      })
      console.log('处理后的表格数据:', mergedProjectList.value)
      // 更新分页数据
      total.value = response.data.total || response.data.records.length
      queryParams.value.pageNum = response.data.current
      queryParams.value.pageSize = response.data.size
    } else {
      proxy.$modal.msgError(response.message || '获取项目列表失败')
    }
    loading.value = false
  }).catch(error => {
    console.error('获取项目列表错误:', error)
    proxy.$modal.msgError('获取项目列表失败')
    loading.value = false
  })
}

/** 查询职位类别列表 */
function getCategoryList() {
  listCategory().then(response => {
    console.log('职位类别列表响应:', response)
    if (response.code === 0) {
      categoryOptions.value = response.data.map(item => ({
        value: item.catId,  // 使用catId作为value
        label: item.name    // 使用name作为label
      }))
      console.log('处理后的选项:', categoryOptions.value)
    } else {
      proxy.$modal.msgError(response.message || '获取职位类别列表失败')
    }
  }).catch(error => {
    console.error('获取职位类别列表错误:', error)
    proxy.$modal.msgError('获取职位类别列表失败')
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  console.log('重置表单前的数据:', {
    form: form.value,
    tempContents: tempContents.value,
    tempQuestions: tempQuestions.value
  })
  
  form.value = {
    proId: null,
    name: null,
    startTime: null,
    endTime: null,
    catName: null,
    content: null,
    segments: []
  }
  tempContents.value = []
  tempQuestions.value = []  // 清空项目问题数组
  currentContent.value = ''
  currentQuestion.value = ''
  showQuestionEditor.value = false  // 重置项目问题编辑框显示状态
  generatedQuestions.value = []  // 清空生成的问题
  
  console.log('重置表单后的数据:', {
    form: form.value,
    tempContents: tempContents.value,
    tempQuestions: tempQuestions.value
  })
  
  proxy.resetForm("projectRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.proId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加项目"
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  console.log('开始修改操作，行数据:', row)
  reset()
  const _proId = row.proId || ids.value
  console.log('要查询的项目ID:', _proId)
  
  try {
    // 先获取职位类别列表
    await getCategoryList()
    
    // 再获取项目数据
    const response = await getProject(_proId)
    console.log('获取到的项目数据:', response)
    
    if (response.code === 0) {
      // 设置基本表单数据
      form.value = {
        proId: response.data.proId,
        name: response.data.name,
        startTime: response.data.startTime,
        endTime: response.data.endTime,
        catId: response.data.catId,
        catName: response.data.catName
      }
      console.log('设置form后的数据:', form.value)
      
      // 设置职位类别
      if (response.data.catId) {
        // 从categoryOptions中查找对应的类别
        const selectedCategory = categoryOptions.value.find(item => item.value === response.data.catId)
        if (selectedCategory) {
          form.value.catId = selectedCategory.value
          form.value.catName = selectedCategory.label
          console.log('设置的职位类别:', form.value.catId, form.value.catName)
        } else {
          console.warn('未找到对应的职位类别:', response.data.catId)
        }
      }
      
      // 设置详细说明数据
      if (response.data.contents && response.data.contents.length > 0) {
        tempContents.value = response.data.contents.map(item => ({
          conId: item.conId,
          text: item.text,
          contentOrder: item.contentOrder,
          questions: item.questions ? item.questions.map(q => ({
            queId: q.queId,
            question: q.question,
            answer: q.answer || '',
            questionOrder: q.questionOrder,
            conId: q.conId
          })) : []
        }))
        console.log('设置的详细说明数据:', tempContents.value)
      }
      
      open.value = true
      title.value = "修改项目"
    } else {
      proxy.$modal.msgError(response.message || '获取项目信息失败')
    }
  } catch (error) {
    console.error('获取数据错误:', error)
    proxy.$modal.msgError('获取数据失败')
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["projectRef"].validate(valid => {
    if (valid) {
      // 构建提交的数据结构
      const submitData = {
        proId: form.value.proId, // 添加proId用于更新
        name: form.value.name,
        startTime: form.value.startTime,
        endTime: form.value.endTime,
        catId: form.value.catId,
        catName: form.value.catName,
        contents: tempContents.value.map((content, index) => ({
          conId: content.conId, // 保留conId用于更新
          text: content.text,
          contentOrder: index + 1,
          questions: content.questions ? content.questions.map((question, qIndex) => ({
            queId: question.queId, // 保留queId用于更新
            question: question.question,
            answer: question.answer || '',
            questionOrder: qIndex + 1,
            conId: question.conId // 保留conId用于更新
          })) : []
        }))
      }

      console.log('提交的数据:', submitData)
      
      if (form.value.proId != null) {
        updateProject(submitData).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
          } else {
            proxy.$modal.msgError(response.message || '修改失败')
          }
        })
      } else {
        addProject(submitData).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
          } else {
            proxy.$modal.msgError(response.message || '新增失败')
          }
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _proIds = row.proId || ids.value
  proxy.$modal.confirm('是否确认删除项目编号为"' + _proIds + '"的数据项？').then(function() {
    return delProject(_proIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/project/export', {
    ...queryParams.value
  }, `project_${new Date().getTime()}.xlsx`)
}

// 添加详细说明
function handleAddContent() {
  if (!currentContent.value.trim()) {
    proxy.$modal.msgError("项目内容不能为空")
    return
  }
  
  tempContents.value.push({
    text: currentContent.value,
    contentOrder: tempContents.value.length + 1
  })
  
  currentContent.value = ''
}

// 删除详细说明
function handleRemoveContent(index) {
  proxy.$modal.confirm('是否确认删除该条项目内容？').then(() => {
    tempContents.value.splice(index, 1)
    // 重新排序
    tempContents.value.forEach((item, idx) => {
      item.contentOrder = idx + 1
    })
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

// 修改显示问题编辑框的方法
function handleShowQuestionEditor(contentIndex) {
  showQuestionEditor.value = true
  currentContentIndex.value = contentIndex
  questionDialogTitle.value = '编辑问题'
  editorMode.value = 'question'
  currentQuestion.value = ''
  currentAnswer.value = ''
}

// 修改编辑问题的方法
function handleEditQuestion(question, qIndex, contentIndex) {
  showQuestionEditor.value = true
  questionDialogTitle.value = '编辑问题'
  editorMode.value = 'question'
  currentQuestion.value = question.question || question.text // 兼容两种数据结构
  editingQuestionIndex.value = qIndex
  editingContentIndex.value = contentIndex
}

// 修改添加问题的方法
function handleAddQuestion() {
  if (!currentQuestion.value.trim()) {
    proxy.$modal.msgError("项目问题不能为空")
    return
  }
  
  const question = {
    question: currentQuestion.value,
    answer: '',
    questionOrder: 0  // 将在提交时重新排序
  }
  
  // 将问题添加到当前编辑的内容下
  if (typeof currentContentIndex.value === 'number' && tempContents.value[currentContentIndex.value]) {
    if (!tempContents.value[currentContentIndex.value].questions) {
      tempContents.value[currentContentIndex.value].questions = []
    }
    tempContents.value[currentContentIndex.value].questions.push(question)
  }
  
  currentQuestion.value = ''
  proxy.$modal.msgSuccess("添加问题成功")
  showQuestionEditor.value = false // 添加成功后关闭对话框
}

// 修改编辑答案的方法
function handleEditAnswer(question, qIndex, contentIndex) {
  showQuestionEditor.value = true
  questionDialogTitle.value = '编辑答案'
  editorMode.value = 'answer'
  currentAnswer.value = question.answer || ''
  editingQuestionIndex.value = qIndex
  editingContentIndex.value = contentIndex
}

// 修改保存答案的方法
function handleSaveAnswer() {
  if (typeof editingQuestionIndex.value === 'number' && 
      typeof editingContentIndex.value === 'number' && 
      tempContents.value[editingContentIndex.value] &&
      tempContents.value[editingContentIndex.value].questions) {
    const question = tempContents.value[editingContentIndex.value].questions[editingQuestionIndex.value]
    question.answer = currentAnswer.value
    proxy.$modal.msgSuccess("答案保存成功")
    showQuestionEditor.value = false // 保存成功后关闭对话框
    editorMode.value = 'question' // 重置编辑状态
    currentAnswer.value = '' // 清空答案内容
  }
}

// 监听对话框关闭
watch(showQuestionEditor, (newVal) => {
  if (!newVal) {
    // 对话框关闭时重置所有状态
    editorMode.value = 'question'
    currentQuestion.value = ''
    currentAnswer.value = ''
    editingQuestionIndex.value = null
    editingContentIndex.value = null
  }
})

// 修改删除问题的方法
function handleRemoveQuestion(qIndex, contentIndex) {
  proxy.$modal.confirm('是否确认删除该条问题？').then(() => {
    if (tempContents.value[contentIndex] && tempContents.value[contentIndex].questions) {
      tempContents.value[contentIndex].questions.splice(qIndex, 1)
      proxy.$modal.msgSuccess("删除成功")
    }
  }).catch(() => {})
}

// 添加职位类别变更处理函数
function handleCategoryChange(value) {
  console.log('选择的职位类别ID:', value)
  const selectedCategory = categoryOptions.value.find(item => item.value === value)
  if (selectedCategory) {
    form.value.catId = selectedCategory.value    // 设置catId
    form.value.catName = selectedCategory.label  // 设置catName
    console.log('设置的职位类别:', form.value.catId, form.value.catName)
  }
}

// 添加编辑详细说明的函数
function handleEditContent(item, index) {
  currentContent.value = item.text
  handleRemoveContent(index) // 删除原有的，编辑后会作为新的添加
}

// 修改生成问题的处理函数
function handleGenerateQuestions(content) {
  console.log('开始生成问题，内容:', content)
  proxy.$modal.confirm('是否确认生成项目问题？').then(() => {
    loading.value = true
    selectByContent(content.text, { timeout: 60000 }).then(response => {
      console.log('生成问题接口返回数据:', response)
      
      if (response.code === 0 && response.data) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData = JSON.parse(response.data[0])
          if (parsedData.questions && Array.isArray(parsedData.questions)) {
            const questions = parsedData.questions.map((item, index) => ({
              question: item.question,
              answer: item.answer || '',
              questionOrder: index + 1
            }))
            
            // 找到对应的内容项并添加问题
            const contentIndex = tempContents.value.findIndex(c => c.text === content.text)
            if (contentIndex !== -1) {
              if (!tempContents.value[contentIndex].questions) {
                tempContents.value[contentIndex].questions = []
              }
              console.log('tempContents.value[contentIndex].questions isExtensible before push:', Object.isExtensible(tempContents.value[contentIndex].questions));
              tempContents.value[contentIndex].questions.push(...questions)
            }
            
            proxy.$modal.msgSuccess("生成问题成功")
          } else {
            console.log('解析后的数据格式不正确:', parsedData)
            proxy.$modal.msgError('生成问题失败：数据格式不正确')
          }
        } catch (error) {
          console.error('解析响应数据失败:', error)
          proxy.$modal.msgError('生成问题失败：数据解析错误')
        }
      } else {
        console.log('返回的数据格式不正确，response:', response)
        proxy.$modal.msgError(response.msg || '生成问题失败')
      }
    }).catch(error => {
      console.error('生成问题错误:', error)
      if (error.code === 'ECONNABORTED') {
        proxy.$modal.msgError('生成问题超时，请稍后重试')
      } else {
        proxy.$modal.msgError('生成问题失败')
      }
    }).finally(() => {
      loading.value = false
    })
  }).catch(() => {})
}

// 添加将生成的问题添加到问题列表的方法
function handleAddGeneratedQuestion(item) {
  tempQuestions.value.push({
    queId: null,
    question: item.question,
    answer: item.answer,
    questionOrder: tempQuestions.value.length + 1
  })
  proxy.$modal.msgSuccess("已添加到问题列表")
}

// 在组件挂载时获取职位类别列表
onMounted(() => {
  getCategoryList()
})

getList()
</script>

<style scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.detail-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-input {
  width: 100%;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.detail-list::-webkit-scrollbar {
  width: 6px;
}

.detail-list::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.detail-list::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.detail-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.detail-index {
  font-weight: bold;
  color: #409eff;
  margin-right: 10px;
  min-width: 24px;
}

.detail-content {
  flex: 1;
  margin-right: 15px;
  word-break: break-all;
}

.detail-actions {
  display: flex;
  gap: 12px;
  padding-top: 5px;
}

.detail-actions .el-button {
  padding: 4px 8px;
  font-size: 13px;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.question-text {
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
  line-height: 1.5;
}

.answer-text {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-top: 8px;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 12px;
  color: #606266;
  line-height: 1.6;
}

.detail-list h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

/* 添加内容按钮样式 */
.detail-input .el-button {
  margin-top: 15px;
  padding: 10px 20px;
}

/* 问题编辑器样式 */
:deep(.md-editor) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
}

:deep(.md-editor-toolbar) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

/* 已生成问题模块样式 */
.generated-questions {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.generated-questions h4 {
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.content-questions {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #e4e7ed;
}

.content-questions h5 {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
}

.question-item {
  margin-bottom: 12px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.question-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.editor-section {
  width: 100%;
}

.editor-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.question-content {
  flex: 1;
  margin-right: 15px;
}

.question-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.question-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.question-text {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.answer-text {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

.content-questions {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #e4e7ed;
}

.content-questions h5 {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
}
</style>
