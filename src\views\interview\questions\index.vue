<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="问题类型" prop="questionType">
        <el-input
          v-model="queryParams.questionType"
          placeholder="请输入问题类型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目内容" prop="question">
        <el-input
          v-model="queryParams.question"
          placeholder="请输入题目内容"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目答案" prop="answer">
        <el-input
          v-model="queryParams.answer"
          placeholder="请输入题目答案"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createAt">
        <el-input
          v-model="queryParams.createAt"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:questions:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:questions:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:questions:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:questions:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="问题类型" align="center" prop="questionType" width="100" />
      <el-table-column label="题目内容" align="center" prop="question" width="290" />
      <el-table-column label="题目答案" align="center" prop="answer">
        <template #default="scope">
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 400px; white-space: pre-wrap;">
                {{ scope.row.answer }}
              </div>
            </template>
            <div class="text-ellipsis">{{ scope.row.answer }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createAt" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:questions:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:questions:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改HR问题对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="questionsRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="问题类型" prop="questionType">
          <el-input v-model="form.questionType" placeholder="请输入问题类型" />
        </el-form-item>
        <el-form-item label="题目内容">
          <el-input v-model="form.question" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="题目答案" prop="answer">
          <el-input v-model="form.answer" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="创建人" prop="createAt">
          <el-input v-model="form.createAt" placeholder="请输入创建人" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Questions">
import { listQuestions, getQuestions, delQuestions, addQuestions, updateQuestions } from "@/api/interview/questions"

const { proxy } = getCurrentInstance()

const questionsList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    questionType: null,
    question: null,
    answer: null,
    createAt: null
  },
  rules: {
    questionType: [
      { required: true, message: "问题类型不能为空", trigger: "change" }
    ],
    question: [
      { required: true, message: "题目内容不能为空", trigger: "blur" }
    ],
    answer: [
      { required: true, message: "题目答案不能为空", trigger: "blur" }
    ],
    createAt: [
      { required: true, message: "创建人不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询HR问题列表 */
function getList() {
  loading.value = true
  listQuestions(queryParams.value).then(response => {
    if (response.code === 0) {
      questionsList.value = response.data
      total.value = response.data.length
    } else {
      proxy.$modal.msgError(response.message || "获取数据失败")
    }
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    queId: null,
    questionType: "HR问题",
    question: null,
    answer: null,
    createAt: null,
    updateTime: null
  }
  proxy.resetForm("questionsRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.queId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加HR问题"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _queId = row.queId || ids.value
  getQuestions(_queId).then(response => {
    if (response.code === 0) {
      form.value = {
        queId: response.data.queId,
        questionType: response.data.questionType,
        question: response.data.question,
        answer: response.data.answer,
        createAt: response.data.createAt,
        updateTime: response.data.updateTime
      }
      open.value = true
      title.value = "修改HR问题"
    } else {
      proxy.$modal.msgError(response.message || "获取数据失败")
    }
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["questionsRef"].validate(valid => {
    if (valid) {
      if (form.value.queId != null) {
        updateQuestions(form.value).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
          } else {
            proxy.$modal.msgError(response.message || "修改失败")
          }
        })
      } else {
        addQuestions(form.value).then(response => {
          if (response.code === 0) {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
          } else {
            proxy.$modal.msgError(response.message || "新增失败")
          }
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _queIds = row.queId || ids.value
  proxy.$modal.confirm('是否确认删除HR问题编号为"' + _queIds + '"的数据项？').then(function() {
    return delQuestions(_queIds)
  }).then(response => {
    if (response.code === 0) {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    } else {
      proxy.$modal.msgError(response.message || "删除失败")
    }
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/questions/export', {
    ...queryParams.value
  }, `questions_${new Date().getTime()}.xlsx`)
}

getList()
</script>

<style scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>
