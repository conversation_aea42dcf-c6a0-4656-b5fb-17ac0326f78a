import{_ as M,u as Q,a as T,r as k,C as H,F as K,w as $,e as p,c as f,i as m,t as U,f as s,h as l,n as h,k as z,o as d,H as O,I as w,j as S,l as J,U as G,V as P,K as _,v as W,x as X}from"./index-BJsoK47l.js";import{g as Y,u as Z,a as ee}from"./questions-BTIDeXeU.js";const te=g=>(W("data-v-d2be75b6"),g=g(),X(),g),oe={class:"app-container"},se={class:"page-header"},ae={class:"page-title"},le={class:"header-actions"},ne={class:"form-content"},re={key:0,class:"options-section"},ie={class:"options-header"},ce=te(()=>m("span",{class:"options-title"},"选项列表",-1)),ue={class:"option-item"},pe={class:"option-label"},de={__name:"questionForm",setup(g){const r=Q(),B=T(),C=k(JSON.parse(JSON.stringify(null))),e=H({questionId:void 0,type:"",category:"",content:"",options:[],analysis:""}),F={type:[{required:!0,message:"请选择题目类型",trigger:"change"}],category:[{required:!0,message:"请选择题目分类",trigger:"change"}],content:[{required:!0,message:"请输入题目内容",trigger:"blur"}]},j=k(JSON.parse(JSON.stringify([{value:"单选题",label:"单选题"},{value:"多选题",label:"多选题"},{value:"填空题",label:"填空题"},{value:"简答题",label:"简答题"},{value:"不定项选择题",label:"不定项选择题"}]))),q=k(JSON.parse(JSON.stringify([]))),D=async()=>{try{const a=await Y();Array.isArray(a)&&(q.value=a.map(t=>({value:t.id,label:t.name})))}catch(a){console.error("获取类目列表失败:",a),_.error("获取类目列表失败")}},V=()=>{e.options.push({content:"",isCorrect:!1})},R=a=>{e.options.splice(a,1)},I=()=>e.options.filter(a=>a.isCorrect).length,A=async()=>{C.value&&await C.value.validate(async a=>{if(a){if(["单选题","多选题","不定项选择题"].includes(e.type)){const t=I();if(t===0){_.error("请至少选择一个正确答案");return}if(e.type==="单选题"&&t>1){_.error("单选题只能有一个正确答案");return}}try{const t={};e.options.forEach((u,n)=>{const y=String.fromCharCode(65+n);t[y]=u.content});const i=e.options.map((u,n)=>u.isCorrect?String.fromCharCode(65+n):null).filter(Boolean).join(""),c={questionId:e.questionId,type:e.type,topicClassification:e.category,content:e.content,options:JSON.stringify(t),analysis:e.analysis,answer:i};console.log("提交的数据:",c),e.questionId?(console.log("更新题目，ID:",e.questionId),await Z(c)):(console.log("新增题目"),await ee(c)),_.success("保存成功"),N()}catch(t){console.error("保存题目失败:",t),_.error("保存失败，请重试")}}})},N=()=>{B.replace("/aqsystem/exams/setting")};return K(async()=>{if(await D(),r.query.id){e.questionId=r.query.id,e.type=r.query.type;const a=q.value.find(t=>t.label===r.query.topicClassification);if(e.category=a?a.value:r.query.topicClassification,e.content=r.query.content,e.analysis=r.query.analysis,["单选题","多选题","不定项选择题"].includes(e.type)&&r.query.options)try{const t=JSON.parse(r.query.options),i=r.query.answer;e.options=Object.entries(t).map(([c,u])=>{const n=e.type==="单选题"?c===i:i&&i.includes(c);return{content:u,isCorrect:n}})}catch(t){console.error("解析选项数据失败:",t),e.options=[]}}}),$(()=>e.type,a=>{e.questionId&&e.options.length>0||(["单选题","多选题","不定项选择题"].includes(a)?(e.options=[],V(),V()):e.options=[])},{immediate:!0}),(a,t)=>{const i=p("el-button"),c=p("el-option"),u=p("el-select"),n=p("el-form-item"),y=p("el-input"),x=p("el-icon"),E=p("el-checkbox"),L=p("el-form");return d(),f("div",oe,[m("div",se,[m("h1",ae,U(e.questionId?"编辑题目":"添加题目"),1),m("div",le,[s(i,{onClick:N},{default:l(()=>[h("返回")]),_:1})])]),m("div",ne,[s(L,{ref_key:"formRef",ref:C,model:e,rules:F,"label-width":"100px",class:"custom-form"},{default:l(()=>[s(n,{label:"题目类型",prop:"type"},{default:l(()=>[s(u,{modelValue:e.type,"onUpdate:modelValue":t[0]||(t[0]=o=>e.type=o),placeholder:"请选择题目类型",class:"form-select"},{default:l(()=>[(d(!0),f(O,null,w(j.value,o=>(d(),S(c,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(n,{label:"题目分类",prop:"category"},{default:l(()=>[s(u,{modelValue:e.category,"onUpdate:modelValue":t[1]||(t[1]=o=>e.category=o),placeholder:"请选择题目分类",class:"form-select"},{default:l(()=>[(d(!0),f(O,null,w(q.value,o=>(d(),S(c,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(n,{label:"题目内容",prop:"content"},{default:l(()=>[s(y,{modelValue:e.content,"onUpdate:modelValue":t[2]||(t[2]=o=>e.content=o),type:"textarea",rows:3,placeholder:"请输入题目内容",class:"content-input"},null,8,["modelValue"])]),_:1}),["单选题","多选题","不定项选择题"].includes(e.type)?(d(),f("div",re,[m("div",ie,[ce,s(i,{type:"primary",link:"",onClick:V,class:"add-option-btn"},{default:l(()=>[s(x,null,{default:l(()=>[s(J(G))]),_:1}),h("添加选项 ")]),_:1})]),(d(!0),f(O,null,w(e.options,(o,b)=>(d(),S(n,{key:b,prop:"options."+b+".content",rules:{required:!0,message:"选项内容不能为空",trigger:"blur"},class:"option-form-item"},{default:l(()=>[m("div",ue,[m("div",pe,U(String.fromCharCode(65+b)),1),s(y,{modelValue:o.content,"onUpdate:modelValue":v=>o.content=v,placeholder:"请输入选项内容",class:"option-input"},null,8,["modelValue","onUpdate:modelValue"]),s(E,{modelValue:o.isCorrect,"onUpdate:modelValue":v=>o.isCorrect=v,disabled:e.type==="单选题"&&I()>0&&!o.isCorrect,class:"correct-checkbox"},{default:l(()=>[h(" 正确答案 ")]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"]),s(i,{type:"danger",link:"",onClick:v=>R(b),class:"delete-option-btn"},{default:l(()=>[s(x,null,{default:l(()=>[s(J(P))]),_:1})]),_:2},1032,["onClick"])])]),_:2},1032,["prop"]))),128))])):z("",!0),s(n,{label:"答案解析",prop:"analysis"},{default:l(()=>[s(y,{modelValue:e.analysis,"onUpdate:modelValue":t[3]||(t[3]=o=>e.analysis=o),type:"textarea",rows:3,placeholder:"请输入答案解析",class:"analysis-input"},null,8,["modelValue"])]),_:1}),s(n,{class:"form-footer"},{default:l(()=>[s(i,{type:"primary",onClick:A,class:"submit-btn"},{default:l(()=>[h("保存题目")]),_:1})]),_:1})]),_:1},8,["model"])])])}}},fe=M(de,[["__scopeId","data-v-d2be75b6"]]);export{fe as default};
