import request from '@/utils/request'

// 查询HR问题列表
export function listQuestions(query) {
  return request({
    url: '/resume/questions/hrQuestionList',
    method: 'post',
    data: query
  })
}

// 查询HR问题详细
export function getQuestions(queId) {
  return request({
    url: '/resume/questions/hrQuestion/' + queId,
    method: 'get'
  })
}

// 新增HR问题
export function addQuestions(data) {
  return request({
    url: '/resume/questions/add',
    method: 'post',
    data: data
  })
}

// 修改HR问题
export function updateQuestions(data) {
  return request({
    url: '/resume/questions/update',
    method: 'put',
    data: data
  })
}

// 删除HR问题
export function delQuestions(queId) {
  return request({
    url: '/resume/questions/delete/' + queId,
    method: 'delete'
  })
}
