import{cf as t,_ as Ae,r as g,e as u,c as w,o as i,f as s,i as m,h as e,l as o,D as W,H as z,I as Q,M as Le,t as K,v as Oe,x as he,B as ye,d as be,C as ke,a5 as we,G as te,J as U,k as I,a4 as xe,m as Ce,j as r,n as _,cg as oe,a6 as Ne}from"./index-BJsoK47l.js";import{l as le,g as Se,d as Ue,u as qe,a as Fe}from"./menu-Dg3cDNiO.js";let M=[];const $e=Object.assign({"../../assets/icons/svg/404.svg":()=>t(()=>import("./404-Dy3nURRX.js"),[]),"../../assets/icons/svg/bug.svg":()=>t(()=>import("./bug-10dePVta.js"),[]),"../../assets/icons/svg/build.svg":()=>t(()=>import("./build-2jMyI6eP.js"),[]),"../../assets/icons/svg/button.svg":()=>t(()=>import("./button-BlSCM_GH.js"),[]),"../../assets/icons/svg/cascader.svg":()=>t(()=>import("./cascader-CXIOcY1C.js"),[]),"../../assets/icons/svg/chart.svg":()=>t(()=>import("./chart-BsLMrzXU.js"),[]),"../../assets/icons/svg/checkbox.svg":()=>t(()=>import("./checkbox-Bpiun3bf.js"),[]),"../../assets/icons/svg/clipboard.svg":()=>t(()=>import("./clipboard-DaV3cn7f.js"),[]),"../../assets/icons/svg/code.svg":()=>t(()=>import("./code-DgJ8cT4a.js"),[]),"../../assets/icons/svg/color.svg":()=>t(()=>import("./color-y1Sshoou.js"),[]),"../../assets/icons/svg/component.svg":()=>t(()=>import("./component-Djp9s69L.js"),[]),"../../assets/icons/svg/dashboard.svg":()=>t(()=>import("./dashboard-Dy7qt_a2.js"),[]),"../../assets/icons/svg/date-range.svg":()=>t(()=>import("./date-range-B8MgYLb1.js"),[]),"../../assets/icons/svg/date.svg":()=>t(()=>import("./date-B1FSITvi.js"),[]),"../../assets/icons/svg/dict.svg":()=>t(()=>import("./dict-Bi_GqSXR.js"),[]),"../../assets/icons/svg/documentation.svg":()=>t(()=>import("./documentation-uH9BvL5U.js"),[]),"../../assets/icons/svg/download.svg":()=>t(()=>import("./download-DeIzgQWH.js"),[]),"../../assets/icons/svg/drag.svg":()=>t(()=>import("./drag-BG1_I1vT.js"),[]),"../../assets/icons/svg/druid.svg":()=>t(()=>import("./druid-BybW_S_B.js"),[]),"../../assets/icons/svg/edit.svg":()=>t(()=>import("./edit-D0DI9pAq.js"),[]),"../../assets/icons/svg/education.svg":()=>t(()=>import("./education-47KsSYIl.js"),[]),"../../assets/icons/svg/email.svg":()=>t(()=>import("./email-Dig28Vt2.js"),[]),"../../assets/icons/svg/example.svg":()=>t(()=>import("./example-CnLLAFb9.js"),[]),"../../assets/icons/svg/excel.svg":()=>t(()=>import("./excel-D3hj5F35.js"),[]),"../../assets/icons/svg/exit-fullscreen.svg":()=>t(()=>import("./exit-fullscreen-dXhGKlQm.js"),[]),"../../assets/icons/svg/eye-open.svg":()=>t(()=>import("./eye-open-BxlshWqB.js"),[]),"../../assets/icons/svg/eye.svg":()=>t(()=>import("./eye-DqRz4sMZ.js"),[]),"../../assets/icons/svg/form.svg":()=>t(()=>import("./form-BDTA_i-I.js"),[]),"../../assets/icons/svg/fullscreen.svg":()=>t(()=>import("./fullscreen-0JHt5yWX.js"),[]),"../../assets/icons/svg/github.svg":()=>t(()=>import("./github-AJ0WQBa2.js"),[]),"../../assets/icons/svg/guide.svg":()=>t(()=>import("./guide-DZWUPi2j.js"),[]),"../../assets/icons/svg/icon.svg":()=>t(()=>import("./icon-BtMv6Od8.js"),[]),"../../assets/icons/svg/input.svg":()=>t(()=>import("./input-BJoPMnBW.js"),[]),"../../assets/icons/svg/international.svg":()=>t(()=>import("./international-CmzG1OHg.js"),[]),"../../assets/icons/svg/job.svg":()=>t(()=>import("./job-BcmuINx7.js"),[]),"../../assets/icons/svg/language.svg":()=>t(()=>import("./language-CaW1LMEk.js"),[]),"../../assets/icons/svg/link.svg":()=>t(()=>import("./link-C93f4PgI.js"),[]),"../../assets/icons/svg/list.svg":()=>t(()=>import("./list-C7O8B4zW.js"),[]),"../../assets/icons/svg/lock.svg":()=>t(()=>import("./lock-Bexeb9hp.js"),[]),"../../assets/icons/svg/log.svg":()=>t(()=>import("./log-CF2F-nSs.js"),[]),"../../assets/icons/svg/logininfor.svg":()=>t(()=>import("./logininfor-Bm9ZYYR7.js"),[]),"../../assets/icons/svg/message.svg":()=>t(()=>import("./message-UkR-VIBB.js"),[]),"../../assets/icons/svg/money.svg":()=>t(()=>import("./money-B1qqPuhn.js"),[]),"../../assets/icons/svg/monitor.svg":()=>t(()=>import("./monitor-gwnnVq4l.js"),[]),"../../assets/icons/svg/moon.svg":()=>t(()=>import("./moon-BOcjHwCq.js"),[]),"../../assets/icons/svg/nested.svg":()=>t(()=>import("./nested-B4d5u3hW.js"),[]),"../../assets/icons/svg/number.svg":()=>t(()=>import("./number-D4hB_nHC.js"),[]),"../../assets/icons/svg/online.svg":()=>t(()=>import("./online-C2ZP8pdY.js"),[]),"../../assets/icons/svg/password.svg":()=>t(()=>import("./password-DfGvqQpB.js"),[]),"../../assets/icons/svg/pdf.svg":()=>t(()=>import("./pdf-CD9mOGjJ.js"),[]),"../../assets/icons/svg/people.svg":()=>t(()=>import("./people-CdGMHN63.js"),[]),"../../assets/icons/svg/peoples.svg":()=>t(()=>import("./peoples-BRYsIqmI.js"),[]),"../../assets/icons/svg/phone.svg":()=>t(()=>import("./phone-BpAUIz0g.js"),[]),"../../assets/icons/svg/post.svg":()=>t(()=>import("./post-DrLDyPY9.js"),[]),"../../assets/icons/svg/qq.svg":()=>t(()=>import("./qq-D8j4O83Y.js"),[]),"../../assets/icons/svg/question.svg":()=>t(()=>import("./question-CvYWQbyW.js"),[]),"../../assets/icons/svg/radio.svg":()=>t(()=>import("./radio-B0t9wPBQ.js"),[]),"../../assets/icons/svg/rate.svg":()=>t(()=>import("./rate-CgnHQvKS.js"),[]),"../../assets/icons/svg/redis-list.svg":()=>t(()=>import("./redis-list-BtKGPnqO.js"),[]),"../../assets/icons/svg/redis.svg":()=>t(()=>import("./redis-D4ECyT6a.js"),[]),"../../assets/icons/svg/row.svg":()=>t(()=>import("./row-CRXKIHjm.js"),[]),"../../assets/icons/svg/search.svg":()=>t(()=>import("./search-CUfclCsR.js"),[]),"../../assets/icons/svg/select.svg":()=>t(()=>import("./select-DhuHHMxz.js"),[]),"../../assets/icons/svg/server.svg":()=>t(()=>import("./server-unS7EyF7.js"),[]),"../../assets/icons/svg/shopping.svg":()=>t(()=>import("./shopping-CU1IRvxM.js"),[]),"../../assets/icons/svg/size.svg":()=>t(()=>import("./size-Cj9fB5Rp.js"),[]),"../../assets/icons/svg/skill.svg":()=>t(()=>import("./skill-B8f_I4m_.js"),[]),"../../assets/icons/svg/slider.svg":()=>t(()=>import("./slider-BGfehM6X.js"),[]),"../../assets/icons/svg/star.svg":()=>t(()=>import("./star-kST8a72V.js"),[]),"../../assets/icons/svg/sunny.svg":()=>t(()=>import("./sunny-DvkHW8g8.js"),[]),"../../assets/icons/svg/swagger.svg":()=>t(()=>import("./swagger-BHGXZ2Jt.js"),[]),"../../assets/icons/svg/switch.svg":()=>t(()=>import("./switch-CvaargRJ.js"),[]),"../../assets/icons/svg/system.svg":()=>t(()=>import("./system-DcNSH_Fq.js"),[]),"../../assets/icons/svg/tab.svg":()=>t(()=>import("./tab-nA3f0aBt.js"),[]),"../../assets/icons/svg/table.svg":()=>t(()=>import("./table-5PRh60AQ.js"),[]),"../../assets/icons/svg/textarea.svg":()=>t(()=>import("./textarea-CJWXlgbJ.js"),[]),"../../assets/icons/svg/theme.svg":()=>t(()=>import("./theme-CyGq941x.js"),[]),"../../assets/icons/svg/time-range.svg":()=>t(()=>import("./time-range-D3dxgtLj.js"),[]),"../../assets/icons/svg/time.svg":()=>t(()=>import("./time-BVERp0sU.js"),[]),"../../assets/icons/svg/tool.svg":()=>t(()=>import("./tool-D8kXk1l-.js"),[]),"../../assets/icons/svg/tree-table.svg":()=>t(()=>import("./tree-table-CnOS99I9.js"),[]),"../../assets/icons/svg/tree.svg":()=>t(()=>import("./tree-BCtS3oPD.js"),[]),"../../assets/icons/svg/upload.svg":()=>t(()=>import("./upload-BueI-Il1.js"),[]),"../../assets/icons/svg/user.svg":()=>t(()=>import("./user-DqMuW5cU.js"),[]),"../../assets/icons/svg/validCode.svg":()=>t(()=>import("./validCode-COB1iLxa.js"),[]),"../../assets/icons/svg/wechat.svg":()=>t(()=>import("./wechat-lmQOcPZu.js"),[]),"../../assets/icons/svg/zip.svg":()=>t(()=>import("./zip-DIOSZc69.js"),[])});for(const A in $e){const c=A.split("assets/icons/svg/")[1].split(".svg")[0];M.push(c)}const Me=A=>(Oe("data-v-7ed7ecf0"),A=A(),he(),A),Be={class:"icon-body"},je=Me(()=>m("i",{class:"el-icon-search el-input__icon"},null,-1)),ze={class:"icon-list"},Qe={class:"list-container"},Ke=["onClick"],Ge={__name:"index",props:{activeIcon:{type:String}},emits:["selected"],setup(A,{expose:c,emit:G}){const f=g(""),k=g(M),E=G;function x(){k.value=M,f.value&&(k.value=M.filter(L=>L.indexOf(f.value)!==-1))}function C(L){E("selected",L),document.body.click()}function q(){f.value="",k.value=M}return c({reset:q}),(L,N)=>{const F=u("el-input"),B=u("svg-icon");return i(),w("div",Be,[s(F,{modelValue:o(f),"onUpdate:modelValue":N[0]||(N[0]=O=>W(f)?f.value=O:null),class:"icon-search",clearable:"",placeholder:"请输入图标名称",onClear:x,onInput:x},{suffix:e(()=>[je]),_:1},8,["modelValue"]),m("div",ze,[m("div",Qe,[(i(!0),w(z,null,Q(o(k),(O,h)=>(i(),w("div",{class:"icon-item-wrapper",key:h,onClick:n=>C(O)},[m("div",{class:Le(["icon-item",{active:A.activeIcon===O}])},[s(B,{"icon-class":O,"class-name":"icon",style:{height:"25px",width:"16px"}},null,8,["icon-class"]),m("span",null,K(O),1)],2)],8,Ke))),128))])])])}}},He=Ae(Ge,[["__scopeId","data-v-7ed7ecf0"]]),Je={class:"app-container"},We={class:"dialog-footer"},Xe=ye({name:"Menu"}),es=Object.assign(Xe,{setup(A){const{proxy:c}=be(),{sys_show_hide:G,sys_normal_disable:f}=c.useDict("sys_show_hide","sys_normal_disable"),k=g([]),E=g(!1),x=g(!0),C=g(!0),q=g(""),L=g([]),N=g(!1),F=g(!0),B=g(null),O=ke({form:{},queryParams:{menuName:void 0,visible:void 0},rules:{menuName:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"菜单顺序不能为空",trigger:"blur"}],path:[{required:!0,message:"路由地址不能为空",trigger:"blur"}]}}),{queryParams:h,form:n,rules:ne}=we(O);function S(){x.value=!0,le(h.value).then(d=>{k.value=c.handleTree(d.data,"menuId"),x.value=!1})}function X(){L.value=[],le().then(d=>{const a={menuId:0,menuName:"主类目",children:[]};a.children=c.handleTree(d.data,"menuId"),L.value.push(a)})}function ae(){E.value=!1,H()}function H(){n.value={menuId:void 0,parentId:0,menuName:void 0,icon:void 0,menuType:"M",orderNum:void 0,isFrame:"1",isCache:"0",visible:"0",status:"0"},c.resetForm("menuRef")}function _e(){B.value.reset()}function ie(d){n.value.icon=d}function J(){S()}function ue(){c.resetForm("queryRef"),J()}function Y(d){H(),X(),d!=null&&d.menuId?n.value.parentId=d.menuId:n.value.parentId=0,E.value=!0,q.value="添加菜单"}function re(){F.value=!1,N.value=!N.value,Ne(()=>{F.value=!0})}async function de(d){H(),await X(),Se(d.menuId).then(a=>{n.value=a.data,E.value=!0,q.value="修改菜单"})}function pe(){c.$refs.menuRef.validate(d=>{d&&(n.value.menuId!=null?qe(n.value).then(a=>{c.$modal.msgSuccess("修改成功"),E.value=!1,S()}):Fe(n.value).then(a=>{c.$modal.msgSuccess("新增成功"),E.value=!1,S()}))})}function ve(d){c.$modal.confirm('是否确认删除名称为"'+d.menuName+'"的数据项?').then(function(){return Ue(d.menuId)}).then(()=>{S(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}return S(),(d,a)=>{const y=u("el-input"),p=u("el-form-item"),me=u("el-option"),ce=u("el-select"),T=u("el-button"),Z=u("el-form"),v=u("el-col"),ge=u("right-toolbar"),ee=u("el-row"),b=u("el-table-column"),fe=u("dict-tag"),Ee=u("el-table"),Ve=u("el-tree-select"),R=u("el-radio"),$=u("el-radio-group"),Ie=u("search"),V=u("el-icon"),Te=u("el-popover"),Re=u("el-input-number"),P=u("question-filled"),D=u("el-tooltip"),Pe=u("el-dialog"),j=te("hasPermi"),De=te("loading");return i(),w("div",Je,[U(s(Z,{model:o(h),ref:"queryRef",inline:!0},{default:e(()=>[s(p,{label:"菜单名称",prop:"menuName"},{default:e(()=>[s(y,{modelValue:o(h).menuName,"onUpdate:modelValue":a[0]||(a[0]=l=>o(h).menuName=l),placeholder:"请输入菜单名称",clearable:"",style:{width:"200px"},onKeyup:Ce(J,["enter"])},null,8,["modelValue"])]),_:1}),s(p,{label:"状态",prop:"status"},{default:e(()=>[s(ce,{modelValue:o(h).status,"onUpdate:modelValue":a[1]||(a[1]=l=>o(h).status=l),placeholder:"菜单状态",clearable:"",style:{width:"200px"}},{default:e(()=>[(i(!0),w(z,null,Q(o(f),l=>(i(),r(me,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(p,null,{default:e(()=>[s(T,{type:"primary",icon:"Search",onClick:J},{default:e(()=>[_("搜索")]),_:1}),s(T,{icon:"Refresh",onClick:ue},{default:e(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[xe,o(C)]]),s(ee,{gutter:10,class:"mb8"},{default:e(()=>[s(v,{span:1.5},{default:e(()=>[U((i(),r(T,{type:"primary",plain:"",icon:"Plus",onClick:Y},{default:e(()=>[_("新增")]),_:1})),[[j,["system:menu:add"]]])]),_:1}),s(v,{span:1.5},{default:e(()=>[s(T,{type:"info",plain:"",icon:"Sort",onClick:re},{default:e(()=>[_("展开/折叠")]),_:1})]),_:1}),s(ge,{showSearch:o(C),"onUpdate:showSearch":a[2]||(a[2]=l=>W(C)?C.value=l:null),onQueryTable:S},null,8,["showSearch"])]),_:1}),o(F)?U((i(),r(Ee,{key:0,data:o(k),"row-key":"menuId","default-expand-all":o(N),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:e(()=>[s(b,{prop:"menuName",label:"菜单名称","show-overflow-tooltip":!0,width:"160"}),s(b,{prop:"icon",label:"图标",align:"center",width:"100"},{default:e(l=>[s(o(oe),{"icon-class":l.row.icon},null,8,["icon-class"])]),_:1}),s(b,{prop:"orderNum",label:"排序",width:"60"}),s(b,{prop:"perms",label:"权限标识","show-overflow-tooltip":!0}),s(b,{prop:"component",label:"组件路径","show-overflow-tooltip":!0}),s(b,{prop:"status",label:"状态",width:"80"},{default:e(l=>[s(fe,{options:o(f),value:l.row.status},null,8,["options","value"])]),_:1}),s(b,{label:"创建时间",align:"center",width:"160",prop:"createTime"},{default:e(l=>[m("span",null,K(d.parseTime(l.row.createTime)),1)]),_:1}),s(b,{label:"操作",align:"center",width:"210","class-name":"small-padding fixed-width"},{default:e(l=>[U((i(),r(T,{link:"",type:"primary",icon:"Edit",onClick:se=>de(l.row)},{default:e(()=>[_("修改")]),_:2},1032,["onClick"])),[[j,["system:menu:edit"]]]),U((i(),r(T,{link:"",type:"primary",icon:"Plus",onClick:se=>Y(l.row)},{default:e(()=>[_("新增")]),_:2},1032,["onClick"])),[[j,["system:menu:add"]]]),U((i(),r(T,{link:"",type:"primary",icon:"Delete",onClick:se=>ve(l.row)},{default:e(()=>[_("删除")]),_:2},1032,["onClick"])),[[j,["system:menu:remove"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[De,o(x)]]):I("",!0),s(Pe,{title:o(q),modelValue:o(E),"onUpdate:modelValue":a[17]||(a[17]=l=>W(E)?E.value=l:null),width:"680px","append-to-body":""},{footer:e(()=>[m("div",We,[s(T,{type:"primary",onClick:pe},{default:e(()=>[_("确 定")]),_:1}),s(T,{onClick:ae},{default:e(()=>[_("取 消")]),_:1})])]),default:e(()=>[s(Z,{ref:"menuRef",model:o(n),rules:o(ne),"label-width":"100px"},{default:e(()=>[s(ee,null,{default:e(()=>[s(v,{span:24},{default:e(()=>[s(p,{label:"上级菜单"},{default:e(()=>[s(Ve,{modelValue:o(n).parentId,"onUpdate:modelValue":a[3]||(a[3]=l=>o(n).parentId=l),data:o(L),props:{value:"menuId",label:"menuName",children:"children"},"value-key":"menuId",placeholder:"选择上级菜单","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1}),s(v,{span:24},{default:e(()=>[s(p,{label:"菜单类型",prop:"menuType"},{default:e(()=>[s($,{modelValue:o(n).menuType,"onUpdate:modelValue":a[4]||(a[4]=l=>o(n).menuType=l)},{default:e(()=>[s(R,{value:"M"},{default:e(()=>[_("目录")]),_:1}),s(R,{value:"C"},{default:e(()=>[_("菜单")]),_:1}),s(R,{value:"F"},{default:e(()=>[_("按钮")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(n).menuType!="F"?(i(),r(v,{key:0,span:12},{default:e(()=>[s(p,{label:"菜单图标",prop:"icon"},{default:e(()=>[s(Te,{placement:"bottom-start",width:540,trigger:"click"},{reference:e(()=>[s(y,{modelValue:o(n).icon,"onUpdate:modelValue":a[5]||(a[5]=l=>o(n).icon=l),placeholder:"点击选择图标",onBlur:_e,readonly:""},{prefix:e(()=>[o(n).icon?(i(),r(o(oe),{key:0,"icon-class":o(n).icon,class:"el-input__icon",style:{height:"32px",width:"16px"}},null,8,["icon-class"])):(i(),r(V,{key:1,style:{height:"32px",width:"16px"}},{default:e(()=>[s(Ie)]),_:1}))]),_:1},8,["modelValue"])]),default:e(()=>[s(o(He),{ref_key:"iconSelectRef",ref:B,onSelected:ie,"active-icon":o(n).icon},null,8,["active-icon"])]),_:1})]),_:1})]),_:1})):I("",!0),s(v,{span:12},{default:e(()=>[s(p,{label:"显示排序",prop:"orderNum"},{default:e(()=>[s(Re,{modelValue:o(n).orderNum,"onUpdate:modelValue":a[6]||(a[6]=l=>o(n).orderNum=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),s(v,{span:12},{default:e(()=>[s(p,{label:"菜单名称",prop:"menuName"},{default:e(()=>[s(y,{modelValue:o(n).menuName,"onUpdate:modelValue":a[7]||(a[7]=l=>o(n).menuName=l),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1})]),_:1}),o(n).menuType=="C"?(i(),r(v,{key:1,span:12},{default:e(()=>[s(p,{prop:"routeName"},{label:e(()=>[m("span",null,[s(D,{content:"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 路由名称 ")])]),default:e(()=>[s(y,{modelValue:o(n).routeName,"onUpdate:modelValue":a[8]||(a[8]=l=>o(n).routeName=l),placeholder:"请输入路由名称"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType!="F"?(i(),r(v,{key:2,span:12},{default:e(()=>[s(p,null,{label:e(()=>[m("span",null,[s(D,{content:"选择是外链则路由地址需要以`http(s)://`开头",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_("是否外链 ")])]),default:e(()=>[s($,{modelValue:o(n).isFrame,"onUpdate:modelValue":a[9]||(a[9]=l=>o(n).isFrame=l)},{default:e(()=>[s(R,{value:"0"},{default:e(()=>[_("是")]),_:1}),s(R,{value:"1"},{default:e(()=>[_("否")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType!="F"?(i(),r(v,{key:3,span:12},{default:e(()=>[s(p,{prop:"path"},{label:e(()=>[m("span",null,[s(D,{content:"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 路由地址 ")])]),default:e(()=>[s(y,{modelValue:o(n).path,"onUpdate:modelValue":a[10]||(a[10]=l=>o(n).path=l),placeholder:"请输入路由地址"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType=="C"?(i(),r(v,{key:4,span:12},{default:e(()=>[s(p,{prop:"component"},{label:e(()=>[m("span",null,[s(D,{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 组件路径 ")])]),default:e(()=>[s(y,{modelValue:o(n).component,"onUpdate:modelValue":a[11]||(a[11]=l=>o(n).component=l),placeholder:"请输入组件路径"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType!="M"?(i(),r(v,{key:5,span:12},{default:e(()=>[s(p,null,{label:e(()=>[m("span",null,[s(D,{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 权限字符 ")])]),default:e(()=>[s(y,{modelValue:o(n).perms,"onUpdate:modelValue":a[12]||(a[12]=l=>o(n).perms=l),placeholder:"请输入权限标识",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType=="C"?(i(),r(v,{key:6,span:12},{default:e(()=>[s(p,null,{label:e(()=>[m("span",null,[s(D,{content:'访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 路由参数 ")])]),default:e(()=>[s(y,{modelValue:o(n).query,"onUpdate:modelValue":a[13]||(a[13]=l=>o(n).query=l),placeholder:"请输入路由参数",maxlength:"255"},null,8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType=="C"?(i(),r(v,{key:7,span:12},{default:e(()=>[s(p,null,{label:e(()=>[m("span",null,[s(D,{content:"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 是否缓存 ")])]),default:e(()=>[s($,{modelValue:o(n).isCache,"onUpdate:modelValue":a[14]||(a[14]=l=>o(n).isCache=l)},{default:e(()=>[s(R,{value:"0"},{default:e(()=>[_("缓存")]),_:1}),s(R,{value:"1"},{default:e(()=>[_("不缓存")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),o(n).menuType!="F"?(i(),r(v,{key:8,span:12},{default:e(()=>[s(p,null,{label:e(()=>[m("span",null,[s(D,{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 显示状态 ")])]),default:e(()=>[s($,{modelValue:o(n).visible,"onUpdate:modelValue":a[15]||(a[15]=l=>o(n).visible=l)},{default:e(()=>[(i(!0),w(z,null,Q(o(G),l=>(i(),r(R,{key:l.value,value:l.value},{default:e(()=>[_(K(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})):I("",!0),s(v,{span:12},{default:e(()=>[s(p,null,{label:e(()=>[m("span",null,[s(D,{content:"选择停用则路由将不会出现在侧边栏，也不能被访问",placement:"top"},{default:e(()=>[s(V,null,{default:e(()=>[s(P)]),_:1})]),_:1}),_(" 菜单状态 ")])]),default:e(()=>[s($,{modelValue:o(n).status,"onUpdate:modelValue":a[16]||(a[16]=l=>o(n).status=l)},{default:e(()=>[(i(!0),w(z,null,Q(o(f),l=>(i(),r(R,{key:l.value,value:l.value},{default:e(()=>[_(K(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{es as default};
