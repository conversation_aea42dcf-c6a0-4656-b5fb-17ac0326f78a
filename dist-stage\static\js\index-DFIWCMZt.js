import{B as _e,u as ge,d as be,r as p,C as ve,a5 as he,a3 as ye,e as r,G as V,c as G,o as m,J as d,f as e,a4 as Y,l as a,h as l,m as A,D as K,n as b,j as v,i as L,t as Q,H as we,I as Ce}from"./index-BJsoK47l.js";import{a as Ne,b as ke,d as Se,p as $e,s as xe}from"./gen-DxLavV6h.js";import Ve from"./importTable-jV-YEshe.js";import Re from"./createTable-CkHqXfp8.js";const Te={class:"app-container"},De=_e({name:"Gen"}),Oe=Object.assign(De,{setup(Ie){const U=ge(),{proxy:s}=be(),q=p([]),R=p(!0),x=p(!0),T=p([]),B=p(!0),D=p(!0),I=p(0),P=p([]),y=p([]),O=p(""),N=p({prop:"createTime",order:"descending"}),M=ve({queryParams:{pageNum:1,pageSize:10,tableName:void 0,tableComment:void 0,orderByColumn:N.value.prop,isAsc:N.value.order},preview:{open:!1,title:"代码预览",data:{},activeName:"domain.java"}}),{queryParams:i,preview:f}=he(M);ye(()=>{const n=U.query.t;n!=null&&n!=O.value&&(O.value=n,i.value.pageNum=Number(U.query.pageNum),y.value=[],s.resetForm("queryForm"),h())});function h(){R.value=!0,Ne(s.addDateRange(i.value,y.value)).then(n=>{q.value=n.rows,I.value=n.total,R.value=!1})}function k(){i.value.pageNum=1,h()}function z(n){const t=n.tableName||P.value;if(t==""){s.$modal.msgError("请选择要生成的数据");return}n.genType==="1"?ke(n.tableName).then(w=>{s.$modal.msgSuccess("成功生成到自定义路径："+n.genPath)}):s.$download.zip("/tool/gen/batchGenCode?tables="+t,"ruoyi.zip")}function H(n){const t=n.tableName;s.$modal.confirm('确认要强制同步"'+t+'"表结构吗？').then(function(){return xe(t)}).then(()=>{s.$modal.msgSuccess("同步成功")}).catch(()=>{})}function J(){s.$refs.importRef.show()}function W(){s.$refs.createRef.show()}function X(){y.value=[],s.resetForm("queryRef"),i.value.pageNum=1,s.$refs.genRef.sort(N.value.prop,N.value.order)}function Z(n){$e(n.tableId).then(t=>{f.value.data=t.data,f.value.open=!0,f.value.activeName="domain.java"})}function ee(){s.$modal.msgSuccess("复制成功")}function te(n){T.value=n.map(t=>t.tableId),P.value=n.map(t=>t.tableName),B.value=n.length!=1,D.value=!n.length}function ae(n,t,w){i.value.orderByColumn=n.prop,i.value.isAsc=n.order,h()}function E(n){const t=n.tableId||T.value[0],w=n.tableName||P.value[0],C={pageNum:i.value.pageNum};s.$tab.openPage("修改["+w+"]生成配置","/tool/gen-edit/index/"+t,C)}function j(n){const t=n.tableId||T.value;s.$modal.confirm('是否确认删除表编号为"'+t+'"的数据项？').then(function(){return Se(t)}).then(()=>{h(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return h(),(n,t)=>{const w=r("el-input"),C=r("el-form-item"),le=r("el-date-picker"),u=r("el-button"),ne=r("el-form"),S=r("el-col"),oe=r("right-toolbar"),ie=r("el-row"),g=r("el-table-column"),$=r("el-tooltip"),re=r("el-table"),se=r("pagination"),de=r("el-link"),ue=r("el-tab-pane"),ce=r("el-tabs"),pe=r("el-dialog"),_=V("hasPermi"),me=V("hasRole"),fe=V("loading"),F=V("copyText");return m(),G("div",Te,[d(e(ne,{model:a(i),ref:"queryRef",inline:!0},{default:l(()=>[e(C,{label:"表名称",prop:"tableName"},{default:l(()=>[e(w,{modelValue:a(i).tableName,"onUpdate:modelValue":t[0]||(t[0]=o=>a(i).tableName=o),placeholder:"请输入表名称",clearable:"",style:{width:"200px"},onKeyup:A(k,["enter"])},null,8,["modelValue"])]),_:1}),e(C,{label:"表描述",prop:"tableComment"},{default:l(()=>[e(w,{modelValue:a(i).tableComment,"onUpdate:modelValue":t[1]||(t[1]=o=>a(i).tableComment=o),placeholder:"请输入表描述",clearable:"",style:{width:"200px"},onKeyup:A(k,["enter"])},null,8,["modelValue"])]),_:1}),e(C,{label:"创建时间",style:{width:"308px"}},{default:l(()=>[e(le,{modelValue:a(y),"onUpdate:modelValue":t[2]||(t[2]=o=>K(y)?y.value=o:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(C,null,{default:l(()=>[e(u,{type:"primary",icon:"Search",onClick:k},{default:l(()=>[b("搜索")]),_:1}),e(u,{icon:"Refresh",onClick:X},{default:l(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Y,a(x)]]),e(ie,{gutter:10,class:"mb8"},{default:l(()=>[e(S,{span:1.5},{default:l(()=>[d((m(),v(u,{type:"primary",plain:"",icon:"Download",disabled:a(D),onClick:z},{default:l(()=>[b("生成")]),_:1},8,["disabled"])),[[_,["tool:gen:code"]]])]),_:1}),e(S,{span:1.5},{default:l(()=>[d((m(),v(u,{type:"primary",plain:"",icon:"Plus",onClick:W},{default:l(()=>[b("创建")]),_:1})),[[me,["admin"]]])]),_:1}),e(S,{span:1.5},{default:l(()=>[d((m(),v(u,{type:"info",plain:"",icon:"Upload",onClick:J},{default:l(()=>[b("导入")]),_:1})),[[_,["tool:gen:import"]]])]),_:1}),e(S,{span:1.5},{default:l(()=>[d((m(),v(u,{type:"success",plain:"",icon:"Edit",disabled:a(B),onClick:E},{default:l(()=>[b("修改")]),_:1},8,["disabled"])),[[_,["tool:gen:edit"]]])]),_:1}),e(S,{span:1.5},{default:l(()=>[d((m(),v(u,{type:"danger",plain:"",icon:"Delete",disabled:a(D),onClick:j},{default:l(()=>[b("删除")]),_:1},8,["disabled"])),[[_,["tool:gen:remove"]]])]),_:1}),e(oe,{showSearch:a(x),"onUpdate:showSearch":t[3]||(t[3]=o=>K(x)?x.value=o:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),d((m(),v(re,{ref:"genRef",data:a(q),onSelectionChange:te,"default-sort":a(N),onSortChange:ae},{default:l(()=>[e(g,{type:"selection",align:"center",width:"55"}),e(g,{label:"序号",type:"index",width:"50",align:"center"},{default:l(o=>[L("span",null,Q((a(i).pageNum-1)*a(i).pageSize+o.$index+1),1)]),_:1}),e(g,{label:"表名称",align:"center",prop:"tableName","show-overflow-tooltip":!0}),e(g,{label:"表描述",align:"center",prop:"tableComment","show-overflow-tooltip":!0}),e(g,{label:"实体",align:"center",prop:"className","show-overflow-tooltip":!0}),e(g,{label:"创建时间",align:"center",prop:"createTime",width:"160",sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"更新时间",align:"center",prop:"updateTime",width:"160",sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"操作",align:"center",width:"330","class-name":"small-padding fixed-width"},{default:l(o=>[e($,{content:"预览",placement:"top"},{default:l(()=>[d(e(u,{link:"",type:"primary",icon:"View",onClick:c=>Z(o.row)},null,8,["onClick"]),[[_,["tool:gen:preview"]]])]),_:2},1024),e($,{content:"编辑",placement:"top"},{default:l(()=>[d(e(u,{link:"",type:"primary",icon:"Edit",onClick:c=>E(o.row)},null,8,["onClick"]),[[_,["tool:gen:edit"]]])]),_:2},1024),e($,{content:"删除",placement:"top"},{default:l(()=>[d(e(u,{link:"",type:"primary",icon:"Delete",onClick:c=>j(o.row)},null,8,["onClick"]),[[_,["tool:gen:remove"]]])]),_:2},1024),e($,{content:"同步",placement:"top"},{default:l(()=>[d(e(u,{link:"",type:"primary",icon:"Refresh",onClick:c=>H(o.row)},null,8,["onClick"]),[[_,["tool:gen:edit"]]])]),_:2},1024),e($,{content:"生成代码",placement:"top"},{default:l(()=>[d(e(u,{link:"",type:"primary",icon:"Download",onClick:c=>z(o.row)},null,8,["onClick"]),[[_,["tool:gen:code"]]])]),_:2},1024)]),_:1})]),_:1},8,["data","default-sort"])),[[fe,a(R)]]),d(e(se,{total:a(I),page:a(i).pageNum,"onUpdate:page":t[4]||(t[4]=o=>a(i).pageNum=o),limit:a(i).pageSize,"onUpdate:limit":t[5]||(t[5]=o=>a(i).pageSize=o),onPagination:h},null,8,["total","page","limit"]),[[Y,a(I)>0]]),e(pe,{title:a(f).title,modelValue:a(f).open,"onUpdate:modelValue":t[7]||(t[7]=o=>a(f).open=o),width:"80%",top:"5vh","append-to-body":"",class:"scrollbar"},{default:l(()=>[e(ce,{modelValue:a(f).activeName,"onUpdate:modelValue":t[6]||(t[6]=o=>a(f).activeName=o)},{default:l(()=>[(m(!0),G(we,null,Ce(a(f).data,(o,c)=>(m(),v(ue,{label:c.substring(c.lastIndexOf("/")+1,c.indexOf(".vm")),name:c.substring(c.lastIndexOf("/")+1,c.indexOf(".vm")),key:o},{default:l(()=>[d((m(),v(de,{underline:!1,icon:"DocumentCopy",style:{float:"right"}},{default:l(()=>[b(" 复制")]),_:2},1024)),[[F,o],[F,ee,"callback"]]),L("pre",null,Q(o),1)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"]),e(a(Ve),{ref:"importRef",onOk:k},null,512),e(a(Re),{ref:"createRef",onOk:k},null,512)])}}});export{Oe as default};
