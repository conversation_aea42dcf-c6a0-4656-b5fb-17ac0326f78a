<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="创建人" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="简历标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入简历标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:resume:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:resume:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:resume:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:resume:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="resumeList" @selection-change="handleSelectionChange">
      <el-table-column type="expand">
        <template #default="props">
          <div style="padding: 0 50px;">
            <h4>教育经历</h4>
            <el-table :data="props.row.educationList" :show-header="false">
              <el-table-column label="学校" prop="school"></el-table-column>
              <el-table-column label="专业" prop="major"></el-table-column>
              <el-table-column label="学历" prop="education"></el-table-column>
              <el-table-column label="时间段" prop="timePeriod"></el-table-column>
              <el-table-column label="主修课程" prop="mainCourses"></el-table-column>
            </el-table>

            <h4>工作经历</h4>
            <el-table :data="props.row.workList" :show-header="false">
              <el-table-column label="公司" prop="company"></el-table-column>
              <el-table-column label="职位" prop="position"></el-table-column>
              <el-table-column label="时间段" prop="timePeriod"></el-table-column>
              <el-table-column label="工作描述" prop="workDescription"></el-table-column>
            </el-table>

            <h4>项目经验</h4>
            <el-table :data="props.row.projectList" :show-header="false">
              <el-table-column label="项目名称" prop="projectName"></el-table-column>
              <el-table-column label="时间段" prop="timePeriod"></el-table-column>
              <el-table-column label="职位类型" prop="positionType"></el-table-column>
              <el-table-column label="角色" prop="role"></el-table-column>
              <el-table-column label="项目描述" prop="projectDescription"></el-table-column>
            </el-table>

            <h4>实践经历</h4>
            <el-table :data="props.row.practiceList" :show-header="false">
              <el-table-column label="时间段" prop="timePeriod"></el-table-column>
              <el-table-column label="项目名称" prop="projectName"></el-table-column>
              <el-table-column label="角色" prop="role"></el-table-column>
              <el-table-column label="项目描述" prop="projectDescription"></el-table-column>
              <el-table-column label="项目链接" prop="projectUrl"></el-table-column>
            </el-table>

            <h4>技能列表</h4>
            <el-table :data="props.row.talentList" :show-header="false">
              <el-table-column label="技能名称" prop="skillName"></el-table-column>
              <el-table-column label="熟练度" prop="proficiency"></el-table-column>
              <el-table-column label="技能描述" prop="skillDescription"></el-table-column>
            </el-table>

            <h4>证书列表</h4>
            <el-table :data="props.row.certificateList" :show-header="false">
              <el-table-column label="证书名称" prop="certificateName"></el-table-column>
            </el-table>

            <h4>校园经历</h4>
            <el-table :data="props.row.campusList" :show-header="false">
              <el-table-column label="经历描述" prop="campusExperience"></el-table-column>
            </el-table>

            <h4>兴趣爱好</h4>
            <el-table :data="props.row.interestList" :show-header="false">
              <el-table-column label="爱好描述" prop="interest"></el-table-column>
            </el-table>

            <h4>自我评价</h4>
            <div v-for="(item, index) in props.row.evaluateList" :key="index" style="margin-bottom: 10px;">
              <p>{{ item.selfEvaluation }}</p>
            </div>

            <div class="detail-list" v-if="tempContents && tempContents.length > 0">
              <h4>已添加的内容：</h4>
              <div v-for="(item, index) in tempContents" :key="index" class="detail-item">
                <div class="detail-index">{{ index + 1 }}.</div>
                <div class="detail-content">
                  <div>{{item.text}}</div>
                  <div class="questions-list" v-if="item.questions && item.questions.length > 0">
                    <div v-for="(question, qIndex) in item.questions" :key="qIndex" class="question-item">
                      <div class="question-header">
                        <span class="question-index">{{ qIndex + 1 }}.</span>
                        <span class="question-text">{{ question.question }}</span>
                      </div>
                      <div class="question-actions">
                        <el-button link type="primary" @click="handleEditQuestion(item, question, qIndex)">编辑</el-button>
                        <el-button link type="danger" @click="handleRemoveQuestion(item, qIndex)">删除1</el-button>
                      </div>
                      <div v-if="question.answer" class="question-answer">
                        <el-collapse>
                          <el-collapse-item title="查看答案">
                            <div style="white-space: pre-wrap;">{{ question.answer }}</div>
                          </el-collapse-item>
                        </el-collapse>
                      </div>
                    </div>
                  </div>
                  <div class="question-editor" v-if="item.showEditor">
                    <div class="detail-input">
                      <el-input
                        v-model="item.currentQuestion"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入问题内容"
                      />
                      <div class="button-group">
                        <el-button type="primary" @click="handleAddQuestion(item)">添加问题</el-button>
                        <el-button type="success" @click="handleGenerateQuestions(item)">生成问题</el-button>
                        <el-button @click="handleCloseEditor(item)">关闭</el-button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="detail-actions">
                  <el-button link type="primary" @click="handleEditContent(item, index)">编辑内容</el-button>
                  <el-button link type="primary" @click="handleShowQuestionEditor(item)">编辑问题</el-button>
                  <el-button link type="danger" @click="handleRemoveContent(index)">删除内容</el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="简历ID" align="center" prop="resumeId" width="80">
        <template #default="scope">
          {{ scope.row.resumeVo.resumeId }}
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="userName" width="70">
        <template #default="scope">
          {{ scope.row.information.name }}
        </template>
      </el-table-column>
      <el-table-column label="生日" align="center" prop="birthDate">
        <template #default="scope">
          {{ scope.row.information.birthDate }}
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center" prop="gender" width="50">
        <template #default="scope">
          {{ scope.row.information.gender }}
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="phone">
        <template #default="scope">
          {{ scope.row.information.phone }}
        </template>
      </el-table-column>
      <el-table-column label="邮箱" align="center" prop="email">
        <template #default="scope">
          {{ scope.row.information.email }}
        </template>
      </el-table-column>
      <el-table-column label="居住地" align="center" prop="hometown">
        <template #default="scope">
          {{ scope.row.information.hometown }}
        </template>
      </el-table-column>
      <el-table-column label="简历标题" align="center" prop="title">
        <template #default="scope">
          {{ scope.row.resumeVo.title }}
        </template>
      </el-table-column>
      <el-table-column label="简历模板" align="center" prop="templateId" width="80">
        <template #default="scope">
          {{ scope.row.resumeVo.templateId }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.resumeVo.status)">
            {{ getStatusText(scope.row.resumeVo.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createAt">
        <template #default="scope">
        {{ scope.row.information.createAt }}
      </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['system:resume:audit']"
          >
            {{ scope.row.resumeVo.status === 0 ? '审核' : '查看' }}
          </el-button>
          <!-- <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:resume:edit']">修改</el-button> -->
          <!-- <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:resume:remove']">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改简历主对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="resumeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="简历标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入简历标题" />
        </el-form-item>
        <el-form-item label="认是否默简历(0-否,1-是)" prop="isDefault">
          <el-input v-model="form.isDefault" placeholder="请输入认是否默简历(0-否,1-是)" />
        </el-form-item>
        <el-form-item label="简历模板ID" prop="templateId">
          <el-input v-model="form.templateId" placeholder="请输入简历模板ID" />
        </el-form-item>
        <el-form-item label="创建人" prop="createAt">
          <el-input v-model="form.createAt" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="删除标识(0-未删除,1-已删除)" prop="isDelete">
          <el-input v-model="form.isDelete" placeholder="请输入删除标识(0-未删除,1-已删除)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核简历对话框 -->
    <el-dialog title="简历审核" v-model="auditOpen" width="800px" append-to-body>
      <div v-loading="auditLoading">
        <!-- 显示审核意见（如果有的话） -->
        <div v-if="auditForm.resumeVo?.status !== 0 && auditForm.resumeVo?.auditOpinion" class="audit-opinion-display">
          <h4>审核意见：</h4>
          <div class="audit-opinion-content">{{ auditForm.resumeVo.auditOpinion }}</div>
        </div>
        <!-- Template 1 -->
        <div v-if="auditForm.resumeVo?.templateId === 1" class="resume-template template-1">
          <!-- 简历头部 -->
          <div class="resume-header">
            <div class="resume-title">
              <h1>{{ auditForm.information?.name || '求职者' }}</h1>
            </div>
            <div class="resume-contact">
              <div class="contact-item" v-if="auditForm.information?.birthDate">
                <el-icon><user /></el-icon>
                {{ auditForm.information.birthDate }}
              </div>
              <div class="contact-item" v-if="auditForm.information?.phone">
                <el-icon><phone /></el-icon>
                {{ auditForm.information.phone }}
              </div>
              <div class="contact-item" v-if="auditForm.information?.email">
                <el-icon><message /></el-icon>
                {{ auditForm.information.email }}
              </div>
              <div class="contact-item" v-if="auditForm.information?.hometown">
                <el-icon><location /></el-icon>
                {{ auditForm.information.hometown }}
              </div>
            </div>
            <div class="avatar-container" v-if="auditForm.information?.avatar">
              <img class="avatar" :src="auditForm.information.avatar" alt="头像"   style="width: 100%;height: 100%;"/>
            </div>
          </div>

          <!-- Template 1 简历内容 -->
          <div class="resume-content">
            <!-- 教育经历 -->
            <div v-if="auditForm.educationList && auditForm.educationList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>教育经历</h2>
              </div>
              <div class="education-content">
                <div v-for="(edu, index) in auditForm.educationList" :key="index" class="education-item">
                  <div class="education-header">
                    <div class="edu-date">{{ edu.timePeriod }}</div>
                    <div class="edu-school">{{ edu.school }}</div>
                    <div class="edu-degree">
                      {{ edu.major }}
                      <span v-if="edu.education">（{{ edu.education }}）</span>
                    </div>
                  </div>
                  <div class="education-details">
                    <div v-if="edu.mainCourses" class="edu-courses">主修课程：{{ edu.mainCourses }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工作经验 -->
            <div v-if="auditForm.workList && auditForm.workList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>工作经验</h2>
              </div>
              <div class="work-content">
                <div v-for="(work, index) in auditForm.workList" :key="index" class="work-item">
                  <div class="work-header">
                    <div class="work-date">{{ work.timePeriod }}</div>
                    <div class="work-company">{{ work.company }}</div>
                    <div class="work-position">{{ work.position }}</div>
                  </div>
                  <div class="work-description" v-html="formatContent(work.workDescription)"></div>
                </div>
              </div>
            </div>

            <!-- 项目经验 -->
            <div v-if="auditForm.projectList && auditForm.projectList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>项目经验</h2>
              </div>
              <div class="section-content">
                <div v-for="(project, index) in auditForm.projectList" :key="index" class="project-item">
                  <div class="project-header">
                    <div class="project-title">{{ project.projectName }}</div>
                    <div class="project-date">{{ project.timePeriod }}</div>
                  </div>
                  <div class="project-role">{{ project.role }}</div>
                  <div class="project-description" v-html="formatContent(project.projectDescription)"></div>
                </div>
              </div>
            </div>

            <!-- 技能特长 -->
            <div v-if="auditForm.talentList && auditForm.talentList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>技能特长</h2>
              </div>
              <div class="skills-content">
                <div v-for="(skill, index) in auditForm.talentList" :key="index" class="skill-description-item">
                  <div class="skill-description-text" v-html="formatContent(skill.skillDescription)"></div>
                </div>
              </div>
            </div>

            <!-- 证书列表 -->
            <div v-if="auditForm.certificateList && auditForm.certificateList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>证书列表</h2>
              </div>
              <div class="certificate-content">
                <ul>
                  <li v-for="(cert, index) in auditForm.certificateList" :key="index">
                    {{ cert.certificateName }}
                  </li>
                </ul>
              </div>
            </div>

            <!-- 校园经历 -->
            <div v-if="auditForm.campusList && auditForm.campusList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>校园经历</h2>
              </div>
              <div class="campus-content">
                <ul>
                  <li v-for="(campus, index) in auditForm.campusList" :key="index">
                    {{ campus.campusExperience }}
                  </li>
                </ul>
              </div>
            </div>

            <!-- 兴趣爱好 -->
            <div v-if="auditForm.interestList && auditForm.interestList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>兴趣爱好</h2>
              </div>
              <div class="interests-content">
                <ul>
                  <li v-for="(interest, index) in auditForm.interestList" :key="index">
                    {{ interest.interest }}
                  </li>
                </ul>
              </div>
            </div>

            <!-- 自我评价 -->
            <div v-if="auditForm.evaluateList && auditForm.evaluateList.length > 0" class="resume-section">
              <div class="section-header">
                <h2>自我评价</h2>
              </div>
              <div class="evaluation-content">
                <div v-for="(evaluate, index) in auditForm.evaluateList" :key="index">
                  {{ evaluate.selfEvaluation }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Template 2 -->
        <div v-else-if="auditForm.resumeVo?.templateId === 2" class="resume-template template-3">
          <div class="resume-container">
            <div>
              <!-- 简历头部 -->
              <div class="header-content">
                <div class="header-text">
                  <div class="resume-title">
                    {{ auditForm.information?.name || '姓名' }}
                  </div>
                  <div class="basic-info-section">
                    <div class="basic-info-grid">
                      <div class="info-item">
                        <span class="info-label">姓名：</span>
                        <span class="info-value">{{ auditForm.information?.name || '' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">年龄：</span>
                        <span class="info-value">{{ auditForm.information?.birthDate || '' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">电话：</span>
                        <span class="info-value">{{ auditForm.information?.phone || '' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">邮箱：</span>
                        <span class="info-value">{{ auditForm.information?.email || '' }}</span>
                      </div>
                    </div>
                    <div class="basic-info-grid">
                      <div class="info-item">
                        <span class="info-label">性别：</span>
                        <span class="info-value">{{ auditForm.information?.gender || '' }}</span>
                      </div>
                      <div v-if="auditForm.information?.hometown" class="info-item">
                        <span class="info-label">籍贯：</span>
                        <span class="info-value">{{ auditForm.information?.hometown }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="avatar-container" v-if="auditForm.information?.avatar">
                  <img class="avatar" :src="auditForm.information.avatar" alt="头像"  style="width: 100%;height: 100%;" />
                </div>
              </div>
            </div>
          </div>

          <!-- 教育经历 -->
          <div v-if="auditForm.educationList && auditForm.educationList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>教育经历</h2>
            </div>
            <div class="education-content">
              <div v-for="(edu, index) in auditForm.educationList" :key="index" class="education-item">
                <div class="edu-header">
                  <div class="edu-date">{{ edu.timePeriod }}</div>
                  <div class="edu-school">{{ edu.school }}</div>
                  <div>{{ edu.education }}，{{ edu.major }}</div>
                </div>
                <div class="edu-info">
                  <div v-if="edu.mainCourses" class="edu-courses">
                    <span class="courses-label">主修课程：</span>{{ edu.mainCourses }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 工作经验 -->
          <div v-if="auditForm.workList && auditForm.workList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>工作经验</h2>
            </div>
            <div class="work-content">
              <div v-for="(work, index) in auditForm.workList" :key="index" class="work-item">
                <div class="work-header">
                  <div class="work-time">{{ work.timePeriod }}</div>
                  <div class="work-company">{{ work.company }}</div>
                  <div class="work-position">{{ work.position }}</div>
                </div>
                <div class="work-description" v-html="formatContent(work.workDescription)"></div>
              </div>
            </div>
          </div>

          <!-- 项目经验 -->
          <div v-if="auditForm.projectList && auditForm.projectList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>项目经验</h2>
            </div>
            <div class="work-content">
              <div v-for="(project, index) in auditForm.projectList" :key="index" class="project-item">
                <div class="work-header">
                  <div class="project-date">{{ project.timePeriod }}</div>
                  <div class="project-title">{{ project.projectName }}</div>
                  <div class="project-role">{{ project.role }}</div>
                </div>
                <div class="project-description" v-html="formatContent(project.projectDescription)"></div>
              </div>
            </div>
          </div>

          <!-- 技能特长 -->
          <div v-if="auditForm.talentList && auditForm.talentList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>技能特长</h2>
            </div>
            <div class="skills-content">
              <div class="skills-description">
                <div v-for="(skill, index) in auditForm.talentList" :key="'desc-'+index" class="skill-description-item">
                  <div class="skill-description-body">{{ index + 1 }}. {{ skill.skillDescription }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 证书列表 -->
          <div v-if="auditForm.certificateList && auditForm.certificateList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>荣誉证书</h2>
            </div>
            <div class="certificate-content">
              <ul>
                <li v-for="(cert, index) in auditForm.certificateList" :key="index">
                  {{ cert.certificateName }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 校园经历 -->
          <div v-if="auditForm.campusList && auditForm.campusList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>校园经历</h2>
            </div>
            <div class="campus-content">
              <ul>
                <li v-for="(campus, index) in auditForm.campusList" :key="index">
                  {{ campus.campusExperience }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 兴趣爱好 -->
          <div v-if="auditForm.interestList && auditForm.interestList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>兴趣爱好</h2>
            </div>
            <div class="interests-content">
              <ul>
                <li v-for="(interest, index) in auditForm.interestList" :key="index">
                  {{ interest.interest }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 自我评价 -->
          <div v-if="auditForm.evaluateList && auditForm.evaluateList.length > 0" class="resume-section">
            <div class="section-header">
              <h2>个人评价</h2>
            </div>
            <div class="evaluation-content">
              <div v-for="(evaluate, index) in auditForm.evaluateList" :key="index">
                {{ evaluate.selfEvaluation }}
              </div>
            </div>
          </div>
        </div>

        <!-- Template 3 -->
        <div v-else-if="auditForm.resumeVo?.templateId === 3" class="resume-template template-4">
          <div class="resume-container">
            <!-- 个人信息部分 -->
            <div class="header-section">
              <div class="personal-info">
                <div class="info-name">{{ auditForm.information?.name || '' }}</div>
                <div class="info-row">
                  <div class="info-item"><span class="label">性别：</span>{{ auditForm.information?.gender || '未填写' }}</div>
                  <div class="info-item"><span class="label">年龄：</span>{{ auditForm.information?.birthDate || '未填写' }}</div>
                </div>
                <div class="info-row">
                  <div class="info-item"><span class="label">电话：</span>{{ auditForm.information?.phone || '未填写' }}</div>
                  <div class="info-item"><span class="label">邮箱：</span>{{ auditForm.information?.email || '未填写' }}</div>
                </div>
                <div class="info-row">
                  <div class="info-item full-width"><span class="label">籍贯：</span>{{ auditForm.information?.hometown || '未填写' }}</div>
                </div>
              </div>
              <div class="avatar-container"  style="width: 85px;height: 110px;" v-if="auditForm.information?.avatar">
                <img class="avatar" :src="auditForm.information.avatar" alt="头像"  style="width: 100%;height: 100%;"/>
              </div>
            </div>
            <!-- 自我评价部分 -->
            <div v-if="auditForm.evaluateList && auditForm.evaluateList.length > 0" class="section">
              <div class="section-header">
                <h2>自我评价</h2>
              </div>
              <div class="evaluation-content">
                <div class="evaluation-text" v-html="formatContent1(auditForm.evaluateList[0]?.selfEvaluation)">
                </div>
              </div>
            </div>
            <!-- 工作经验部分 -->
            <div v-if="auditForm.workList && auditForm.workList.length > 0" class="section">
              <div class="section-header">
                <h2>工作经验</h2>
              </div>
              <div class="section-content">
                <div v-for="(work, index) in auditForm.workList" :key="index" class="experience-item">
                  <div class="experience-header">
                    <div class="experience-date">{{ work.timePeriod }}</div>
                    <div class="experience-company">{{ work.company }}</div>
                    <div class="experience-position">{{ work.position }}</div>
                  </div>
                  <div class="experience-description" v-html="formatContent(work.workDescription)"></div>
                </div>
              </div>
            </div>
            <!-- 项目经验部分 -->
            <div v-if="auditForm.projectList && auditForm.projectList.length > 0" class="section">
              <div class="section-header">
                <h2>项目经验</h2>
              </div>
              <div class="section-content">
                <div v-for="(project, index) in auditForm.projectList" :key="index" class="project-item">
                  <div class="project-header">
                    <div class="project-title">{{ project.projectName }}</div>
                    <div class="project-role">{{ project.role }}</div>
                    <div class="project-date">{{ project.timePeriod }}</div>
                  </div>
                 
                  <div class="project-description" v-html="formatContent(project.projectDescription)"></div>
                </div>
              </div>
            </div>
            <!-- 相关技能部分 -->
            <div v-if="auditForm.talentList && auditForm.talentList.length > 0" class="section">
              <div class="section-header">
                <h2>相关技能</h2>
              </div>
              <div class="section-content skills-content">
                <div class="skills-list">
                  <div v-for="(skill, index) in auditForm.talentList" :key="index" class="skill-item">
                    <div class="skill-name">• 熟练 {{ skill.skillName }}：</div>
                    <div class="skill-description" v-html="formatContent(skill.skillDescription)"></div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 教育背景部分 -->
            <div v-if="auditForm.educationList && auditForm.educationList.length > 0" class="section">
              <div class="section-header">
                <h2>教育背景</h2>
              </div>
              <div class="section-content">
                <div v-for="(edu, index) in auditForm.educationList" :key="index" class="education-item">
                  <div class="education-header">
                    <div class="education-school">{{ edu.school }}({{ edu.education }})</div>
                    <div class="education-date">{{ edu.timePeriod }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Template 4 -->
        <template v-else-if="auditForm.resumeVo?.templateId === 4">
          <Template4 :resume="adaptToTemplate4(auditForm)" />
        </template>
        <!-- Template 5 -->
        <template v-else-if="auditForm.resumeVo?.templateId === 5">
          <Template5 :resume="adaptToTemplate4(auditForm)" />
        </template>
      </div>
      <template #footer>
        <div v-if="auditForm.resumeVo?.status === 0" class="dialog-footer">
          <div class="audit-opinion-container" style="margin-bottom: 15px;">
            <el-input
              v-model="auditForm.auditOpinion"
              type="textarea"
              :rows="3"
              placeholder="请输入审核意见"
              :required="auditRejectRequireOpinion"
            />
            <div v-if="auditRejectRequireOpinion && !auditForm.auditOpinion" class="el-form-item__error">
              审核拒绝时，审核意见为必填
            </div>
          </div>
          <el-button type="primary" @click="confirmAudit">审核通过</el-button>
          <el-button type="danger" @click="rejectAudit">审核拒绝</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
        <div v-else class="dialog-footer">
          <el-button @click="auditOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Resume">
import { listResume, getResume, delResume, addResume, updateResume } from "@/api/interview/resume"
import { auditResume, resumeDetail } from "@/api/interview/resume"
import { User, Phone, Message, Location } from '@element-plus/icons-vue'
import Template4 from './Template4.vue'
import Template5 from './Template5.vue'

const { proxy } = getCurrentInstance()

const resumeList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const auditOpen = ref(false)
const auditLoading = ref(false)
const auditForm = ref({})
const auditRejectRequireOpinion = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: null,
    title: null,
    createAt: null,
    isDelete: null
  },
  rules: {
    userId: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    title: [
      { required: true, message: "简历标题不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询简历主列表 */
function getList() {
  loading.value = true
  // 构建请求体
  const requestBody = {
    // pageNum: queryParams.value.pageNum,
    // pageSize: queryParams.value.pageSize,
    title: queryParams.value.title,
    name: queryParams.value.userId  // 将前端的 userId 映射到后端的 name 参数
  }
  listResume(requestBody).then(response => {
    // 确保从 response.data 中获取简历列表
    if (response.data && Array.isArray(response.data)) {
      // 假设后端返回的数据结构外层仍然是 data
      resumeList.value = response.data
      // 如果后端提供了总数，使用后端提供的 total 字段，如果存在的话
      // 否则，如果后端返回的总数据在 response.total 字段，可以使用 total.value = response.total
      // 这里假设后端返回的response直接就是数据列表，或者 total 在 response.total
      total.value = response.total || response.data.length // 或者使用后端提供的 total 字段，如果存在的话
      console.log('简历列表数据加载成功:', resumeList.value)
    } else {
      console.error('简历列表数据格式不正确:', response)
      resumeList.value = []
      total.value = 0
    }
    loading.value = false
  }).catch(error => {
    console.error('获取简历列表失败:', error)
    resumeList.value = []
    total.value = 0
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  console.log('重置表单前的数据:', {
    form: form.value,
    tempContents: tempContents.value
  })
  
  form.value = {
    proId: null,
    name: null,
    startTime: null,
    endTime: null,
    catName: null,
    content: null,
    segments: []
  }
  
  // 重置内容列表，确保每个内容项都有questions数组和showEditor标志
  tempContents.value = tempContents.value.map(item => ({
    ...item,
    questions: [],
    currentQuestion: '',
    showEditor: false
  }))
  
  console.log('重置表单后的数据:', {
    form: form.value,
    tempContents: tempContents.value
  })
  
  proxy.resetForm("projectRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.resumeId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加简历主"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _resumeId = row.resumeId || ids.value
  getResume(_resumeId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改简历主"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["resumeRef"].validate(valid => {
    if (valid) {
      if (form.value.resumeId != null) {
        updateResume(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addResume(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _resumeIds = row.resumeId || ids.value
  proxy.$modal.confirm('是否确认删除简历主编号为"' + _resumeIds + '"的数据项？').then(function() {
    return delResume(_resumeIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/resume/export', {
    ...queryParams.value
  }, `resume_${new Date().getTime()}.xlsx`)
}

/** 审核按钮操作 */
function handleAudit(row) {
  auditLoading.value = true
  auditOpen.value = true
  auditRejectRequireOpinion.value = false
  
  // 获取简历详情
  resumeDetail(row.resumeVo.resumeId).then(response => {
    if (response.code === 0) {
      auditForm.value = response.data
      // 确保auditOpinion字段存在
      auditForm.value.auditOpinion = auditForm.value.resumeVo.auditOpinion || ''
    } else {
      proxy.$modal.msgError(response.message || "获取简历详情失败")
      auditOpen.value = false
    }
  }).catch(error => {
    proxy.$modal.msgError("获取简历详情失败")
    auditOpen.value = false
  }).finally(() => {
    auditLoading.value = false
  })
}

/** 确认审核 */
function confirmAudit() {
  proxy.$modal.confirm('您确定审核通过吗？', '审核确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const resumeId = auditForm.value.resumeVo.resumeId
    auditResume(resumeId, {
      status: 1,
      auditOpinion: auditForm.value.auditOpinion || ''
    }).then(response => {
      if (response.code === 0) {
        proxy.$modal.msgSuccess("审核通过")
        auditOpen.value = false
        getList()
      } else {
        proxy.$modal.msgError(response.message || "审核失败")
      }
    }).catch(error => {
      proxy.$modal.msgError("审核失败")
    })
  }).catch(() => {
    // 用户取消操作
  })
}

/** 审核不通过 */
function rejectAudit() {
  // 验证审核意见是否填写
  if (!auditForm.value.auditOpinion) {
    auditRejectRequireOpinion.value = true
    return
  }
  
  proxy.$modal.confirm('您确定审核拒绝吗？', '审核确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const resumeId = auditForm.value.resumeVo.resumeId
    auditResume(resumeId, {
      status: 2,
      auditOpinion: auditForm.value.auditOpinion
    }).then(response => {
      if (response.code === 0) {
        proxy.$modal.msgSuccess("审核拒绝成功")
        auditOpen.value = false
        getList()
      } else {
        proxy.$modal.msgError(response.message || "审核失败")
      }
    }).catch(error => {
      proxy.$modal.msgError("审核失败")
    })
  }).catch(() => {
    // 用户取消操作
  })
}

function getStatusType(status) {
  // 根据简历状态返回不同的标签类型
  switch (status) {
    case 0:
      return 'info'
    case 1:
      return 'success'
    case 2:
      return 'warning'
    case 3:
      return 'danger'
    default:
      return 'info'
  }
}

function getStatusText(status) {
  // 根据简历状态返回不同的标签文本
  switch (status) {
    case 0:
      return '未审核'
    case 1:
      return '审核通过'
    case 2:
      return '审核未通过'
    case 3:
      return '已删除'
    default:
      return '未知状态'
  }
}

// 添加项目问题
function handleAddQuestion(item) {
  if (!item.currentQuestion || !item.currentQuestion.trim()) {
    proxy.$modal.msgError("项目问题不能为空")
    return
  }
  
  if (!item.questions) {
    item.questions = []
  }
  
  item.questions.push({
    queId: null,
    question: item.currentQuestion,
    questionOrder: item.questions.length + 1
  })
  item.currentQuestion = ''
  proxy.$modal.msgSuccess("添加问题成功")
}

// 删除项目问题
function handleRemoveQuestion(item, index) {
  proxy.$modal.confirm('是否确认删除该条项目问题？').then(() => {
    item.questions.splice(index, 1)
    // 重新排序
    item.questions.forEach((question, idx) => {
      question.questionOrder = idx + 1
    })
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

// 编辑项目问题
function handleEditQuestion(item, question, index) {
  item.currentQuestion = question.question
  item.showEditor = true
  handleRemoveQuestion(item, index)
}

// 修改生成问题的处理函数
function handleGenerateQuestions(item) {
  console.log('开始生成问题，内容:', item)
  proxy.$modal.confirm('是否确认生成项目问题？').then(() => {
    loading.value = true
    request({
      url: 'http://localhost:7091/ai/chatAPI/contentQuestion',
      method: 'post',
      data: {
        content: item.text
      },
      timeout: 60000
    }).then(response => {
      console.log('生成问题接口返回数据:', response)
      
      let responseData
      try {
        let cleanResponse = response
        if (typeof response === 'string') {
          cleanResponse = response.replace(/^```json\s*/, '').replace(/\s*```$/, '')
        }
        responseData = typeof cleanResponse === 'string' ? JSON.parse(cleanResponse) : cleanResponse
        console.log('解析后的responseData:', responseData)
      } catch (error) {
        console.error('解析响应数据失败:', error)
        proxy.$modal.msgError('生成问题失败：数据格式不正确')
        return
      }
      
      if (responseData && responseData.questions && Array.isArray(responseData.questions)) {
        if (!item.questions) {
          item.questions = []
        }
        responseData.questions.forEach(question => {
          console.log('item.questions isExtensible before push:', Object.isExtensible(item.questions));
          item.questions.push({
            queId: null,
            question: question.question,
            answer: question.answer,
            questionOrder: item.questions.length + 1
          })
        })
        
        proxy.$modal.msgSuccess("生成问题成功")
      } else {
        console.log('返回的数据格式不正确，responseData:', responseData)
        proxy.$modal.msgError('生成问题失败：数据格式不正确')
      }
    }).catch(error => {
      console.error('生成问题错误:', error)
      proxy.$modal.msgError('生成问题失败')
    }).finally(() => {
      loading.value = false
    })
  }).catch(() => {})
}

function handleShowQuestionEditor(item) {
  item.showEditor = true
}

function handleCloseEditor(item) {
  item.showEditor = false
}

// 格式化内容
const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 简单的Markdown转HTML处理
  return content
    // 处理标题
    .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
    .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
    .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
    // 处理加粗和斜体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 处理列表
    .replace(/^\- (.*?)$/gm, '<li>$1</li>')
    .replace(/(<\/li>\n<li>)/g, '</li><li>')
    .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
    // 处理段落
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^(.+)$/, '<p>$1</p>');
};

// 添加formatContent1函数
const formatContent1 = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 处理列表（保持原有逻辑）
  let formatted = content
    .replace(/^\- (.*?)$/gm, '<li>$1</li>')
    .replace(/(<\/li>\n<li>)/g, '</li><li>')
    .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');

  // 处理标题（保持原有逻辑）
  formatted = formatted
    .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
    .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
    .replace(/^# (.*?)$/gm, '<h1>$1</h1>');

  // 处理加粗和斜体（保持原有逻辑）
  formatted = formatted
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 为非列表、非标题的普通文本行添加序号
  const lines = formatted.split('\n');
  let numberedLines = '';
  let lineNumber = 1;

  lines.forEach(line => {
    // 跳过空行、HTML标签行（如列表、标题）
    if (!line.trim() || line.match(/^<\/?[a-z][\s\S]*>$/i)) {
      numberedLines += line + '\n';
    } else {
      // 添加序号
      numberedLines += `<span class="line-number">${lineNumber++}.</span> ${line}\n`;
    }
  });

  // 处理段落（保持原有逻辑，但在添加序号后）
  formatted = numberedLines
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^(.+)$/, '<p>$1</p>');

  return formatted;
};

// 在 <script setup> 里添加适配函数
function adaptToTemplate4(raw) {
  return {
    modules: {
      basic: {
        name: raw.information?.name || '',
        gender: raw.information?.gender || '',
        age: raw.information?.birthDate || '',
        phone: raw.information?.phone || '',
        email: raw.information?.email || '',
        jobObjective: '', // 可根据实际字段补充
        avatar: raw.information?.avatar || ''
      },
      work: (raw.workList || []).map(item => ({
        ...item,
        time: item.timePeriod ? [item.timePeriod, item.timePeriod] : ['', '']
      })),
      projects: (raw.projectList || []).map(item => ({
        ...item,
        name: item.projectName,
        time: item.timePeriod ? [item.timePeriod, item.timePeriod] : ['', '']
      })),
      practices: (raw.practiceList || []).map(item => ({
        ...item,
        name: item.projectName,
        time: item.timePeriod ? [item.timePeriod, item.timePeriod] : ['', '']
      })),
      skills: (raw.talentList || []).map(item => ({
        description: item.skillDescription || ''
      })),
      education: (raw.educationList || []).map(item => ({
        school: item.school,
        degree: item.education,
        major: item.major,
        time: item.timePeriod ? [item.timePeriod, item.timePeriod] : ['', ''],
        courses: item.mainCourses || ''
      })),
      certificates: (raw.certificateList || []).map(item => ({
        certificateName: item.certificateName || ''
      })),
      campus: raw.campusList || [],
      interests: raw.interestList || [],
      selfEvaluation: (raw.evaluateList && raw.evaluateList[0]?.selfEvaluation) || ''
    }
  }
}

getList()
</script>

<style scoped>
.detail-item {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.audit-opinion-display {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-left: 4px solid #409EFF;
  border-radius: 4px;
}

.audit-opinion-display h4 {
  margin: 0 0 10px 0;
  color: #606266;
}

.audit-opinion-content {
  color: #303133;
  line-height: 1.5;
  white-space: pre-wrap;
}

.detail-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
}

.detail-content {
  margin: 10px 0;
}

.question-editor {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.detail-input {
  margin-bottom: 20px;
}

.button-group {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.questions-list {
  margin-top: 20px;
}

.questions-list h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.question-item {
  margin-bottom: 15px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.question-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.question-index {
  font-weight: bold;
  color: #409eff;
  margin-right: 8px;
  min-width: 24px;
}

.question-text {
  flex: 1;
  color: #303133;
  line-height: 1.5;
}

.question-actions {
  margin: 8px 0;
  display: flex;
  gap: 12px;
}

.question-answer {
  margin-top: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  color: #409eff;
}

:deep(.el-collapse-item__content) {
  padding: 12px;
  color: #606266;
  line-height: 1.6;
}

.detail-actions {
  margin-top: 10px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.resume-sections {
  margin-top: 20px;
}

.resume-sections h4 {
  margin: 20px 0 10px;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.resume-sections .el-table {
  margin-bottom: 20px;
}

/* 添加简历模板样式 */
.resume-template {
  width: 100%;
  background: white;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
}

.resume-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 2px solid #3498db;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  color: white;
  height: 113px;
}

.resume-title h1 {
  font-size: 28px;
  margin: 0;
  font-weight: bold;
}

.resume-contact {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.resume-content {
  padding: 20px;
}

.resume-section {
  margin-bottom: 20px;
}

.section-header {
  position: relative;
  border-bottom: none;
  margin-bottom: 10px;
}

.section-header h2 {
  font-size: 18px;
  color: #3498db;
  margin: 0;
  padding: 10px 0;
  font-weight: bold;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: #3498db;
}

.education-content,
.work-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.education-item,
.work-item,
.project-item {
  margin-bottom: 15px;
}

.education-header,
.work-header,
.project-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.edu-date,
.work-date,
.project-date {
  font-weight: bold;
}

.edu-school,
.work-company,
.project-title {
  font-weight: bold;
}

.edu-degree,
.work-position,
.project-role {
  font-weight: bold;
}

.edu-courses,
.work-description,
.project-description {
  line-height: 1.4;
  text-align: justify;
}

.skills-content {
  display: flex;
  flex-direction: column;
}

.skill-description-item {
  margin-bottom: 3px;
}

.skill-description-text {
  font-size: 13px;
  color: #444;
  line-height: 1.4;
  text-align: justify;
  white-space: pre-line;
}

.certificate-content,
.campus-content,
.interests-content,
.evaluation-content {
  line-height: 1.4;
  text-align: justify;
}

.certificate-content ul,
.campus-content ul,
.interests-content ul {
  padding-left: 20px;
  margin: 0;
}

.certificate-content li,
.campus-content li,
.interests-content li {
  margin-bottom: 3px;
}

/* Template 2 specific styles */
.template-3 {
  width: 100%;
  background: white;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
  padding: 15px;
}

.template-3 .resume-container {
  padding: 0 15px;
}

.template-3 .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  padding-bottom: 5px;
  margin-left: -15px;
}

.template-3 .header-text {
  flex-grow: 1;
  margin-right: 20px;
}

.template-3 .resume-title {
  font-size: 24px;
  font-weight: bold;
  color: #000;
}

.template-3 .basic-info-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.template-3 .basic-info-grid {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  line-height: 1.8;
}

.template-3 .info-item {
  display: inline-flex;
  margin: 0 10px 5px 0;
  white-space: nowrap;
}

.template-3 .info-label {
  font-weight: bold;
  margin-right: 5px;
}

.template-3 .info-value {
  color: #333;
}

.template-3 .section-header h2 {
  font-size: 15px;
  color: #FF0000;
  margin: 0;
  font-weight: bold;
  flex-grow: 1;
  border-bottom: 1px solid #FF0000;
  padding-bottom: 3px;
}

.template-3 .edu-header,
.template-3 .work-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

.template-3 .edu-header > div:nth-child(1),
.template-3 .work-header > div:nth-child(1) {
  flex-basis: 30%;
  text-align: left;
}

.template-3 .edu-header > div:nth-child(2),
.template-3 .work-header > div:nth-child(2) {
  flex-basis: 40%;
  text-align: center;
}

.template-3 .edu-header > div:nth-child(3),
.template-3 .work-header > div:nth-child(3) {
  flex-basis: 30%;
  text-align: right;
}

.template-3 .skill-description-body {
  font-size: 12px;
  color: #000000;
  line-height: 1.4;
  text-align: justify;
  white-space: pre-line;
}

/* Template 4 specific styles */
.template-4 {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  background-color: #fff;
  line-height: 1;
  color: rgb(68, 84, 106);
  padding: 0 !important;
}

.template-4 .resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  border: 15px solid rgb(68, 84, 106);
}

.template-4 .info-name {
  font-weight: bold;
  font-size: 30px;
  padding-bottom: 20px;
}

.template-4 .header-section {
  display: flex;
  justify-content: space-between;
  padding-bottom: 15px;
}

.template-4 .personal-info {
  flex: 1;
}

.template-4 .info-row {
  display: flex;
  margin-bottom: 10px;
}

.template-4 .info-item {
  flex: 1;
  min-width: 0;
}

.template-4 .info-item.full-width {
  flex: 2;
}

.template-4 .label {
  font-weight: bold;
}

.template-4 .section {
  margin-bottom: 5px;
  padding: 0 !important;
}

.template-4 .section-header {
  margin-bottom: 5px;
  position: relative;
  display: inline-block;
  padding: 0 5px;
}

.template-4 .section-header::before,
.template-4 .section-header::after {
  content: '';
  position: absolute;
  height: 1px;
  background-color: #333;
  width: calc(100% + 10px);
  left: -5px;
}

.template-4 .section-header::before {
  bottom: -2px;
}

.template-4 .section-header::after {
  bottom: -5px;
}

.template-4 .section-header h2 {
  display: inline-block;
  margin: 0;
  padding: 5px 15px;
  font-weight: bold;
  background-color: #f0f0f0;
  border-radius: 5px 5px 0 0;
}

.template-4 .section-content {
  padding: 5px 0;
}

.template-4 .evaluation-content {
  padding: 5px;
}

.template-4 .evaluation-text {
  line-height: 1.6;
}

.template-4 .experience-item {
  margin-bottom: 8px;
}

.template-4 .experience-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.template-4 .education-date {
  flex: 1;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: right;
}

.template-4 .experience-date {
  margin-right: 40px;
  font-weight: bold;
  flex: 1;
}

.template-4 .experience-company {
  margin-right: 15px;
  font-weight: bold;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 18px;
}

.template-4 .experience-company::after {
  color: inherit;
}
.template-4 .experience-description {
  padding-left: 10px;
}

.template-4 .project-item {
  margin-bottom: 8px;
}

.template-4 .project-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.template-4 .project-title {
  font-weight: bold;
  flex: 1;
}

.template-4 .project-date {
  flex: 1;
  text-align: right;
}

.template-4 .project-role {
  margin-right: 15px;
}

.template-4 .project-description {
  padding-left: 10px;
}

.template-4 .skills-content {
  padding: 10px;
}

.template-4 .skills-list {
  display: flex;
  flex-wrap: wrap;
}

.template-4 .skill-item {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.template-4 .skill-name {
  font-weight: bold;
  margin-right: 10px;
  white-space: nowrap;
}

.template-4 .skill-description {
  flex: 1;
}

.template-4 .education-item {
  margin-bottom: 5px;
}

.template-4 .education-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.template-4 .education-school {
  width: 200px;
  flex: 1;
  font-weight: bold;
  margin-right: 20px;
  margin-bottom: 10px;
}

/* Add styles for line numbers in template-3 */
.template-3 .line-number {
  color: #666;
  margin-right: 8px;
  font-weight: bold;
}
.avatar-container{
  width: 90px;
  height: 100px;
}
</style>
