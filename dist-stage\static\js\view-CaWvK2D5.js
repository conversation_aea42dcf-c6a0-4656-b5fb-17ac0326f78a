import{_ as K,u as O,a as U,r as f,C as W,F as X,K as q,e as d,G as Y,c as T,i as t,f as e,t as c,n as y,h as o,l as g,O as Z,P as ee,Q as te,R as se,S as ae,J as oe,o as b,j as A,H as B,I as L,T as ne,v as le,x as ie}from"./index-BJsoK47l.js";import{u as S,w as re}from"./xlsx-DrgRuPKf.js";import{e as ce}from"./questionnaire-D6WvR6WF.js";const C=N=>(le("data-v-c5386106"),N=N(),ie(),N),ue={class:"app-container"},de={class:"page-header"},_e={class:"page-title"},ve={class:"page-subtitle"},pe={class:"info-item"},he={class:"info-item"},me={class:"info-item"},fe={class:"header-actions"},we={class:"card-header"},ge=C(()=>t("span",null,"总人数",-1)),be={class:"card-value"},Ce={class:"card-header"},qe=C(()=>t("span",null,"已提交",-1)),ye={class:"card-value"},Ne={class:"card-footer"},Ie={class:"card-header"},Re=C(()=>t("span",null,"完成率",-1)),Te={class:"card-value"},ke={class:"card-header"},De=C(()=>t("span",null,"平均用时",-1)),xe={class:"card-value"},Se={class:"table-container"},Ve={class:"preview-content"},Ee={class:"questionnaire-header"},$e={class:"header-title"},Ae=C(()=>t("div",{class:"info-tag"},"信息已加密",-1)),Be={class:"question-list"},Le={class:"question-title"},Pe=C(()=>t("span",{class:"required"},"*",-1)),je={class:"number"},Fe={class:"answer text-answer"},Qe={__name:"view",setup(N){const P=O();U();const l=f({questionnaireId:"",title:"",lableName:"",status:"",className:"",totalCount:0,submittedCount:0,completionRate:0,averageTime:"",questions:[],scoresVos:[],answerRows:[]}),v=f({totalCount:0,submittedCount:0,averageDuration:0}),k=f(!1),I=f(0),p=f([]);W({pageNum:1,pageSize:10});const D=f(!1),V=f({});function j(i){switch(i){case"进行中":return"success";case"未开始":return"info";case"已结束":return"danger";default:return""}}function F(i,r){const a=i/r*100;return a<30?"#f56c6c":a<70?"#e6a23c":"#67c23a"}async function Q(i){k.value=!0;try{const r=await ce(i);if(r.code===200&&r.data){const a=r.data;l.value={questionnaireId:a.questionnaireId||"",title:a.title||"",lableName:a.lableName||"",status:a.status||"",className:a.className||"",totalCount:a.totalCount||0,submittedCount:a.submittedCount||0,completionRate:a.completionRate||0,averageTime:a.averageTime||"0秒",questions:a.questions||[],scoresVos:a.scoresVos||[],answerRows:a.answerRows||[]},v.value={totalCount:a.totalCount||0,submittedCount:a.submittedCount||0,averageDuration:a.averageTime||"0秒"},Array.isArray(a.answerRows)?(p.value=a.answerRows.map((s,h)=>{const n=a.scoresVos&&a.scoresVos[h]||{};return{index:s.index||h+1,startTime:n.startTime||"",endTime:n.submitTime||"",duration:n.duration||"0秒",studentName:s.studentName||"",studentId:s.answers&&s.answers[1]||"",phone:s.phone||"",gender:s.answers&&s.answers[3]==="男"?"male":"female",classPosition:s.answers&&s.answers[4]!=="无"||!1,classRank:s.answers&&s.answers[5]||"",javaSkill:E(s.answers&&s.answers[6]),algorithmSkill:E(s.answers&&s.answers[7]),careerExpectation:s.answers&&s.answers[8]||"",answers:s.answers||[]}}),I.value=p.value.length):(p.value=[],I.value=0)}else q.error(r.msg||"获取问卷详情失败"),l.value={questionnaireId:"",title:"",lableName:"",status:"",className:"",totalCount:0,submittedCount:0,completionRate:0,averageTime:"0秒",questions:[],scoresVos:[],answerRows:[]},v.value={totalCount:0,submittedCount:0,averageDuration:"0秒"},p.value=[],I.value=0}catch(r){console.error("获取问卷详情失败:",r),q.error("获取问卷详情失败"),l.value={questionnaireId:"",title:"",lableName:"",status:"",className:"",totalCount:0,submittedCount:0,completionRate:0,averageTime:"0秒",questions:[],scoresVos:[],answerRows:[]},v.value={totalCount:0,submittedCount:0,averageDuration:"0秒"},p.value=[],I.value=0}finally{k.value=!1}}function E(i){switch(i){case"A":return 5;case"B":return 4;case"C":return 3;case"D":return 2;case"E":return 1;default:return 0}}function M(){try{const i=p.value.map(n=>{const m={编号:n.index,姓名:n.studentName,学号:n.studentId,手机号:n.phone,开始答题时间:n.startTime,结束答题时间:n.endTime,答题时长:n.duration};return n.answers&&Array.isArray(n.answers)&&n.answers.forEach((x,R)=>{const u=l.value.questions[R];u&&(m[`${R+1}. ${u.content}`]=x||"")}),m}),r=S.book_new(),a=S.json_to_sheet(i);S.book_append_sheet(r,a,"问卷数据");const s=[{wch:8},{wch:10},{wch:12},{wch:15},{wch:20},{wch:20},{wch:12}];l.value.questions&&l.value.questions.forEach(()=>{s.push({wch:30})}),a["!cols"]=s;const h=`${l.value.title}_${new Date().toLocaleDateString()}.xlsx`;re(r,h),q.success("导出成功")}catch(i){console.error("导出失败:",i),q.error("导出失败，请重试")}}function z(i){V.value={...i},D.value=!0}return X(()=>{const i=P.query.questionnaireId;i?Q(i):q.warning("未获取到问卷ID")}),(i,r)=>{const a=d("el-tag"),s=d("el-icon"),h=d("el-button"),n=d("el-card"),m=d("el-col"),x=d("el-progress"),R=d("el-row"),u=d("el-table-column"),G=d("el-table"),H=d("el-dialog"),J=Y("loading");return b(),T("div",ue,[t("div",de,[t("div",null,[t("h1",_e,c(l.value.title),1),t("p",ve,[t("span",pe,"班级："+c(l.value.className),1),t("span",he,"类型："+c(l.value.lableName),1),t("span",me,[y("状态："),e(a,{type:j(l.value.status)},{default:o(()=>[y(c(l.value.status),1)]),_:1},8,["type"])])])]),t("div",fe,[e(h,{type:"primary",onClick:M},{default:o(()=>[e(s,null,{default:o(()=>[e(g(Z))]),_:1}),y("导出数据 ")]),_:1})])]),e(R,{gutter:20,class:"overview-cards"},{default:o(()=>[e(m,{span:6},{default:o(()=>[e(n,{shadow:"hover",class:"overview-card"},{header:o(()=>[t("div",we,[ge,e(s,null,{default:o(()=>[e(g(ee))]),_:1})])]),default:o(()=>[t("div",be,c(v.value.totalCount),1)]),_:1})]),_:1}),e(m,{span:6},{default:o(()=>[e(n,{shadow:"hover",class:"overview-card"},{header:o(()=>[t("div",Ce,[qe,e(s,null,{default:o(()=>[e(g(te))]),_:1})])]),default:o(()=>[t("div",ye,c(v.value.submittedCount),1),t("div",Ne,[e(x,{percentage:l.value.completionRate,color:F(v.value.submittedCount,v.value.totalCount)},null,8,["percentage","color"])])]),_:1})]),_:1}),e(m,{span:6},{default:o(()=>[e(n,{shadow:"hover",class:"overview-card"},{header:o(()=>[t("div",Ie,[Re,e(s,null,{default:o(()=>[e(g(se))]),_:1})])]),default:o(()=>[t("div",Te,c(l.value.completionRate)+"%",1)]),_:1})]),_:1}),e(m,{span:6},{default:o(()=>[e(n,{shadow:"hover",class:"overview-card"},{header:o(()=>[t("div",ke,[De,e(s,null,{default:o(()=>[e(g(ae))]),_:1})])]),default:o(()=>[t("div",xe,c(l.value.averageTime),1)]),_:1})]),_:1})]),_:1}),e(n,{class:"box-card",shadow:"hover"},{default:o(()=>[t("div",Se,[oe((b(),A(G,{data:p.value,style:{width:"100%"}},{default:o(()=>[e(u,{type:"index",label:"编号",width:"80",fixed:"left",align:"center"}),e(u,{label:"姓名",prop:"studentName",width:"120",align:"center"}),e(u,{label:"手机号",prop:"phone",width:"150",align:"center"}),e(u,{label:"开始答题时间",prop:"startTime",width:"180",align:"center"}),e(u,{label:"结束答题时间",prop:"endTime",width:"180",align:"center"}),e(u,{label:"答题时长",prop:"duration",width:"120",align:"center"}),(b(!0),T(B,null,L(l.value.questions,(_,w)=>(b(),A(u,{key:_.questionId,label:`${w+1}. ${_.content}`,prop:"answers."+w,"min-width":"120",align:"center","show-overflow-tooltip":""},null,8,["label","prop"]))),128)),e(u,{label:"操作",width:"100",fixed:"right",align:"center"},{default:o(_=>[e(h,{type:"primary",link:"",onClick:w=>z(_.row)},{default:o(()=>[e(s,null,{default:o(()=>[e(g(ne))]),_:1}),y("预览 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,k.value]])])]),_:1}),e(H,{modelValue:D.value,"onUpdate:modelValue":r[0]||(r[0]=_=>D.value=_),title:l.value.title+" - 问卷预览",width:"700px","destroy-on-close":"","close-on-click-modal":!1},{default:o(()=>[t("div",Ve,[t("div",Ee,[t("div",$e,c(l.value.title),1),Ae]),t("div",Be,[(b(!0),T(B,null,L(l.value.questions,(_,w)=>{var $;return b(),T("div",{key:_.questionId,class:"question-item"},[t("div",Le,[Pe,t("span",je,c(String(w+1).padStart(2,"0")),1),y(" "+c(_.content),1)]),t("div",Fe,c(($=V.value.answers)==null?void 0:$[w]),1)])}),128))])])]),_:1},8,["modelValue","title"])])}}},He=K(Qe,[["__scopeId","data-v-c5386106"]]);export{He as default};
