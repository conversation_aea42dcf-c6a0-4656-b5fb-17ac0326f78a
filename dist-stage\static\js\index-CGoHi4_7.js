import{Z as w,B as ae,d as ne,r as i,C as oe,a5 as se,e as r,G as F,c as ue,o as y,J as p,f as e,a4 as Q,l as t,h as a,m as I,n as m,j as h,D as T,i as re}from"./index-BJsoK47l.js";function de(d){return w({url:"/system/class/list",method:"get",params:d})}function ce(d){return w({url:"/system/class/"+d,method:"get"})}function me(d){return w({url:"/system/class",method:"post",data:d})}function ie(d){return w({url:"/system/class",method:"put",data:d})}function pe(d){return w({url:"/system/class/"+d,method:"delete"})}const fe={class:"app-container"},_e={class:"dialog-footer"},ge=ae({name:"Class"}),ve=Object.assign(ge,{setup(d){const{proxy:f}=ne(),R=i([]),_=i(!1),N=i(!0),D=i(!0),B=i([]),q=i(!0),P=i(!0),x=i(0),S=i(""),j=oe({form:{},queryParams:{pageNum:1,pageSize:10,className:null,createdBy:null,isDelete:null},rules:{className:[{required:!0,message:"$comment不能为空",trigger:"blur"}],createdBy:[{required:!0,message:"$comment不能为空",trigger:"blur"}],isDelete:[{required:!0,message:"$comment不能为空",trigger:"blur"}]}}),{queryParams:o,form:u,rules:L}=se(j);function v(){N.value=!0,de(o.value).then(s=>{R.value=s.rows,x.value=s.total,N.value=!1})}function A(){_.value=!1,U()}function U(){u.value={classId:null,className:null,createdBy:null,isDelete:null},f.resetForm("classRef")}function V(){o.value.pageNum=1,v()}function G(){f.resetForm("queryRef"),V()}function J(s){B.value=s.map(l=>l.classId),q.value=s.length!=1,P.value=!s.length}function O(){U(),_.value=!0,S.value="添加班级"}function E(s){U();const l=s.classId||B.value;ce(l).then(g=>{u.value=g.data,_.value=!0,S.value="修改班级"})}function Z(){f.$refs.classRef.validate(s=>{s&&(u.value.classId!=null?ie(u.value).then(l=>{f.$modal.msgSuccess("修改成功"),_.value=!1,v()}):me(u.value).then(l=>{f.$modal.msgSuccess("新增成功"),_.value=!1,v()}))})}function K(s){const l=s.classId||B.value;f.$modal.confirm('是否确认删除班级编号为"'+l+'"的数据项？').then(function(){return pe(l)}).then(()=>{v(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function H(){f.download("system/class/export",{...o.value},`class_${new Date().getTime()}.xlsx`)}return v(),(s,l)=>{const g=r("el-input"),b=r("el-form-item"),c=r("el-button"),z=r("el-form"),k=r("el-col"),M=r("right-toolbar"),W=r("el-row"),$=r("el-table-column"),X=r("el-table"),Y=r("pagination"),ee=r("el-dialog"),C=F("hasPermi"),le=F("loading");return y(),ue("div",fe,[p(e(z,{model:t(o),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[e(b,{label:"${comment}",prop:"className"},{default:a(()=>[e(g,{modelValue:t(o).className,"onUpdate:modelValue":l[0]||(l[0]=n=>t(o).className=n),placeholder:"请输入${comment}",clearable:"",onKeyup:I(V,["enter"])},null,8,["modelValue"])]),_:1}),e(b,{label:"${comment}",prop:"createdBy"},{default:a(()=>[e(g,{modelValue:t(o).createdBy,"onUpdate:modelValue":l[1]||(l[1]=n=>t(o).createdBy=n),placeholder:"请输入${comment}",clearable:"",onKeyup:I(V,["enter"])},null,8,["modelValue"])]),_:1}),e(b,{label:"${comment}",prop:"isDelete"},{default:a(()=>[e(g,{modelValue:t(o).isDelete,"onUpdate:modelValue":l[2]||(l[2]=n=>t(o).isDelete=n),placeholder:"请输入${comment}",clearable:"",onKeyup:I(V,["enter"])},null,8,["modelValue"])]),_:1}),e(b,null,{default:a(()=>[e(c,{type:"primary",icon:"Search",onClick:V},{default:a(()=>[m("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:G},{default:a(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Q,t(D)]]),e(W,{gutter:10,class:"mb8"},{default:a(()=>[e(k,{span:1.5},{default:a(()=>[p((y(),h(c,{type:"primary",plain:"",icon:"Plus",onClick:O},{default:a(()=>[m("新增")]),_:1})),[[C,["system:class:add"]]])]),_:1}),e(k,{span:1.5},{default:a(()=>[p((y(),h(c,{type:"success",plain:"",icon:"Edit",disabled:t(q),onClick:E},{default:a(()=>[m("修改")]),_:1},8,["disabled"])),[[C,["system:class:edit"]]])]),_:1}),e(k,{span:1.5},{default:a(()=>[p((y(),h(c,{type:"danger",plain:"",icon:"Delete",disabled:t(P),onClick:K},{default:a(()=>[m("删除")]),_:1},8,["disabled"])),[[C,["system:class:remove"]]])]),_:1}),e(k,{span:1.5},{default:a(()=>[p((y(),h(c,{type:"warning",plain:"",icon:"Download",onClick:H},{default:a(()=>[m("导出")]),_:1})),[[C,["system:class:export"]]])]),_:1}),e(M,{showSearch:t(D),"onUpdate:showSearch":l[3]||(l[3]=n=>T(D)?D.value=n:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),p((y(),h(X,{data:t(R),onSelectionChange:J},{default:a(()=>[e($,{type:"selection",width:"55",align:"center"}),e($,{label:"${comment}",align:"center",prop:"classId"}),e($,{label:"${comment}",align:"center",prop:"className"}),e($,{label:"${comment}",align:"center",prop:"createdBy"}),e($,{label:"${comment}",align:"center",prop:"isDelete"}),e($,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(n=>[p((y(),h(c,{link:"",type:"primary",icon:"Edit",onClick:te=>E(n.row)},{default:a(()=>[m("修改")]),_:2},1032,["onClick"])),[[C,["system:class:edit"]]]),p((y(),h(c,{link:"",type:"primary",icon:"Delete",onClick:te=>K(n.row)},{default:a(()=>[m("删除")]),_:2},1032,["onClick"])),[[C,["system:class:remove"]]])]),_:1})]),_:1},8,["data"])),[[le,t(N)]]),p(e(Y,{total:t(x),page:t(o).pageNum,"onUpdate:page":l[4]||(l[4]=n=>t(o).pageNum=n),limit:t(o).pageSize,"onUpdate:limit":l[5]||(l[5]=n=>t(o).pageSize=n),onPagination:v},null,8,["total","page","limit"]),[[Q,t(x)>0]]),e(ee,{title:t(S),modelValue:t(_),"onUpdate:modelValue":l[9]||(l[9]=n=>T(_)?_.value=n:null),width:"500px","append-to-body":""},{footer:a(()=>[re("div",_e,[e(c,{type:"primary",onClick:Z},{default:a(()=>[m("确 定")]),_:1}),e(c,{onClick:A},{default:a(()=>[m("取 消")]),_:1})])]),default:a(()=>[e(z,{ref:"classRef",model:t(u),rules:t(L),"label-width":"80px"},{default:a(()=>[e(b,{label:"${comment}",prop:"className"},{default:a(()=>[e(g,{modelValue:t(u).className,"onUpdate:modelValue":l[6]||(l[6]=n=>t(u).className=n),placeholder:"请输入${comment}"},null,8,["modelValue"])]),_:1}),e(b,{label:"${comment}",prop:"createdBy"},{default:a(()=>[e(g,{modelValue:t(u).createdBy,"onUpdate:modelValue":l[7]||(l[7]=n=>t(u).createdBy=n),placeholder:"请输入${comment}"},null,8,["modelValue"])]),_:1}),e(b,{label:"${comment}",prop:"isDelete"},{default:a(()=>[e(g,{modelValue:t(u).isDelete,"onUpdate:modelValue":l[8]||(l[8]=n=>t(u).isDelete=n),placeholder:"请输入${comment}"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ve as default};
