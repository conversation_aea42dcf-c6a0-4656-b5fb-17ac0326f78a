import { createWebHashHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/aqsystem',
    component: Layout,
    redirect: 'noRedirect',
    name: 'AQSystem',
    meta: { title: '问卷系统', icon: 'form' },
    hidden: true,
    children: [
      {
        path: 'questionnaires',
        component: () => import('@/views/aqsystem/questionnaires/index'),
        name: 'Questionnaires',
        hidden: true
      },
      {
        path: 'questionnaires/edit',
        component: () => import('@/views/aqsystem/questionnaires/edit'),
        name: 'QuestionnaireEdit',
        hidden: true
      },
      {
        path: 'questionnaires/view',
        component: () => import('@/views/aqsystem/questionnaires/view'),
        name: 'QuestionnaireView',
        meta: { title: '问卷详情', icon: 'document' },
        hidden: true
      },
      {
        path: 'questionnaires/questionForm1',
        component: () => import('@/views/aqsystem/questionnaires/questionForm1'),
        name: 'QuestionForm1',
        meta: { title: '题目表单', icon: 'edit' },
        hidden: true
      },
      {
        path: 'exams',
        component: () => import('@/views/aqsystem/exams/index'),
        name: 'Exams',
        meta: { title: '考试管理', icon: 'education' }
      },
      {
        path: 'exams/edit',
        component: () => import('@/views/aqsystem/exams/edit'),
        name: 'ExamEdit',
        meta: { title: '编辑考试', icon: 'edit' },
        hidden: true
      },
      {
        path: 'exams/student-list',
        component: () => import('@/views/aqsystem/exams/student-list'),
        name: 'ExamStudentList',
        meta: { title: '班级答题情况', icon: 'user' },
        hidden: true
      },
      {
        path: 'exams/student-detail',
        component: () => import('@/views/aqsystem/exams/student-detail'),
        name: 'ExamStudentDetail',
        meta: { title: '学生答题详情', icon: 'document' },
        hidden: true
      },
      {
        path: 'exams/setting',
        component: () => import('@/views/aqsystem/exams/setting'),
        name: 'QuestionBank',
        meta: { title: '题库管理', icon: 'list' },
        hidden: true
      },
      {
        path: 'exams/questionForm',
        component: () => import('@/views/aqsystem/exams/questionForm'),
        name: 'QuestionForm',
        meta: { title: '题目表单', icon: 'edit' },
        hidden: true
      }
    ]
  },
  {
    path: '/selecttitle',
    component: Layout,
    redirect: 'noRedirect',
    name: 'SelectTitle',
    meta: { title: '选题管理', icon: 'education' },
    hidden: true,
    children: [
      {
        path: 'supervisor',
        component: () => import('@/views/selecttitle/supervisor/index'),
        name: 'Supervisor',
        meta: { title: '导师管理', icon: 'user' },
        hidden: true
      },
      {
        path: 'supervisor/detail/:supervisorId',
        component: () => import('@/views/selecttitle/supervisor/detail'),
        name: 'SupervisorDetail',
        meta: { title: '导师详情', icon: 'form', activeMenu: '/selecttitle/supervisor' },
        hidden: true
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})

export default router
