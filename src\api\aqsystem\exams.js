import request from '@/utils/request'

// 查询考试管理列表
export function listExams(query) {
  return request({
    url: '/aqsystem/exams/list',
    method: 'get',
    params: query
  })
}

// 查询考试管理详细
export function getExams(examId) {
  return request({
    url: '/aqsystem/exams/' + examId,
    method: 'get'
  })
}

// 新增考试管理
export function addExams(data) {
  return request({
    url: '/aqsystem/exams',
    method: 'post',
    data: data
  })
}

// 修改考试管理
export function updateExams(data) {
  return request({
    url: '/aqsystem/exams',
    method: 'put',
    data: data
  })
}

// 删除考试管理
export function delExams(examId) {
  return request({
    url: '/aqsystem/exams/' + examId,
    method: 'delete'
  })
}

///aqsystem/class
export function getClassList() {
  return request({
    url: '/aqsystem/class',
    method: 'get'
  })
}
// aqsystem/exams/select/exam001
export function selectExams(examId) {
  return request({
    url: '/aqsystem/exams/select/' + examId,
    method: 'get'
  })
}