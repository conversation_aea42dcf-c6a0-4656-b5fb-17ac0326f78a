<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {  Microphone } from '@element-plus/icons-vue'
import { VideoPlay } from '@element-plus/icons-vue'
import { interviewPage,interviewDetail,interviewFeedback,interviewTranscription } from "@/api/blew/m"
import InterviewFeedbackDetail from './components/InterviewFeedbackDetail.vue'
// 将毫秒格式化为 mm:ss.SSS 格式
const formatMilliseconds = (ms) => {
  if (ms === undefined || ms === null) return '00:00.000'
  
  // 计算分钟、秒和毫秒
  const totalSeconds = Math.floor(ms / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  const milliseconds = ms % 1000
  
  // 格式化为 mm:ss.SSS
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`
}

// 面试列表数据
const interviewList = ref(JSON.parse(JSON.stringify([])))
const loading = ref(JSON.parse(JSON.stringify(false)))
const total = ref(JSON.parse(JSON.stringify(0)))
const currentPage = ref(JSON.parse(JSON.stringify(1)))
const pageSize = ref(JSON.parse(JSON.stringify(10)))

// 查询参数
const queryParams = reactive({
  candidateName: '',
  type: '',
  position: '',
  stage: '',
  status: null,
  startTime: '',
  endTime: ''
})

// 对话框控制
const dialogVisible = ref(JSON.parse(JSON.stringify(false)))
const dialogTitle = ref(JSON.parse(JSON.stringify('添加面试')))
const dialogType = ref(JSON.parse(JSON.stringify('add'))) // add或edit

// 表单数据
const formData = reactive({
  id: null,
  userId: null,
  type: 'mock',
  candidateName: '',
  company: '',
  position: '',
  stage: 'tech',
  experience: 'fresh',
  status: 0,
  overallScore: null,
  feedback: '',
  strengths: '',
  improvements: '',
  videoUrl: '',
  interviewTime: '',
  questions: []
})

// 反馈对话框控制
const feedbackDialogVisible = ref(JSON.parse(JSON.stringify(false)))
const feedbackForm = reactive({
  id: '',
  score: 0,
  feedback: '',
  strengths: '',
  improvements: '',
  videoUrl: '',
  isTranslating: false,
  result: null // 添加面试结果字段
})

// 章节段落和对话内容
const speechChapters = ref(JSON.parse(JSON.stringify([])))
const speechRecords = ref(JSON.parse(JSON.stringify([])))
const currentChapter = ref(JSON.parse(JSON.stringify(null)))
const currentChapterRecords = ref(JSON.parse(JSON.stringify([])))
const isLoadingChapters = ref(JSON.parse(JSON.stringify(false)))
const isLoadingRecords = ref(JSON.parse(JSON.stringify(false)))

// 新增：记录当前评分反馈的面试类型
const feedbackInterviewType = ref(JSON.parse(JSON.stringify('mock')))

// 问答回顾数据
const transcriptions = ref(JSON.parse(JSON.stringify([])))
const isLoadingTranscriptions = ref(JSON.parse(JSON.stringify(false)))

// 当前激活的页签
const activeTab = ref(JSON.parse(JSON.stringify('chapters')))

// 查看详情相关的状态
const detailDialogRef = ref(JSON.parse(JSON.stringify(null)))
const detailData = reactive({
  score: 0,
  feedback: '',
  strengths: '',
  improvements: '',
  videoUrl: '',
  type: 'mock', // 新增type字段
  result: null  // 添加面试结果字段
})

// 查看详情弹窗显示控制
const detailDialogVisible = ref(JSON.parse(JSON.stringify(false)))

// 表单规则
const rules = {
  type: [
    { required: true, message: '请选择面试类型', trigger: 'change' }
  ],
  candidateName: [
    { required: true, message: '请输入应聘者姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请输入面试职位', trigger: 'blur' }
  ],
  stage: [
    { required: true, message: '请选择面试阶段', trigger: 'change' }
  ],
  experience: [
    { required: true, message: '请选择工作经验', trigger: 'change' }
  ],
  interviewTime: [
    { required: true, message: '请选择面试时间', trigger: 'change' }
  ]
}

// 面试类型选项
const typeOptions = [
  { value: 'mock', label: '模拟面试' },
  { value: 'formal', label: '正式面试' }
]

// 面试阶段选项
const stageOptions = [
  { value: 'hr', label: 'HR面试' },
  { value: 'tech', label: '技术面试' }
]

// 工作经验选项
const experienceOptions = [
  { value: 'fresh', label: '应届生' },
  { value: '1-31', label: '1-3年' },
  { value: '3-5', label: '3-5年' },
  { value: '5+', label: '5年以上' }
]

// 职位映射选项
const positionOptions = [
  { value: '1', label: '开发' },
  { value: '2', label: '测试' },
  { value: '3', label: '技术支持' }
]

// 面试状态选项
const statusOptions = [
  { value: 0, label: '进行中' },
  { value: 1, label: '等待结果' },
  { value: 2, label: '已完成' }
]

// 面试状态文本映射
const statusTextMap = {
  0: '进行中',
  1: '等待结果',
  2: '已完成'
}

const formRef = ref(JSON.parse(JSON.stringify(null)))
const feedbackFormRef = ref(JSON.parse(JSON.stringify(null)))

// 获取面试列表
const getInterviews = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      candidateName: queryParams.candidateName,
      type: queryParams.type,
      position: queryParams.position,
      stage: queryParams.stage,
      status: queryParams.status,
      startTime: queryParams.startTime,
      endTime: queryParams.endTime
    }
    // 使用 interviewPage API接口
    const res = await interviewPage(params)
    if (res.code === 0) {
      interviewList.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.message || '获取面试列表失败')
    }
  } catch (error) {
    console.error('获取面试列表失败:', error)
    ElMessage.error('获取面试列表失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  queryParams.candidateName = ''
  queryParams.type = ''
  queryParams.position = ''
  queryParams.stage = ''
  queryParams.status = null
  queryParams.startTime = ''
  queryParams.endTime = ''
  getInterviews()
}

// 处理查询
const handleQuery = () => {
  currentPage.value = 1
  getInterviews()
}

// 处理分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  getInterviews()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  getInterviews()
}

// 获取职位标签
const getPositionLabel = (value) => {
  const option = positionOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 获取工作经验标签
const getExperienceLabel = (value) => {
  const option = experienceOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 获取状态文本
const getStatusText = (status) => {
  return statusTextMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const map = {
    0: 'warning',      // 进行中：黄色
    1: 'danger',       // 等待结果：红色
    2: 'success'       // 已完成：绿色
  }
  return map[status] || 'info'
}

// 打开添加对话框
const handleAdd = () => {
  resetForm()
  dialogTitle.value = '添加面试'
  dialogType.value = 'add'
  dialogVisible.value = true
}

// 打开编辑对话框
const handleEdit = (row) => {
  resetForm()
  dialogTitle.value = '编辑面试'
  dialogType.value = 'edit'
  
  // 调用后台API获取详细数据
  getInterviewDetail(row.id).then(res => {
    if (res.code === 0) {
      const interviewData = res.data
      
      // 填充表单数据
      Object.keys(formData).forEach(key => {
        if (interviewData[key] !== undefined) {
          formData[key] = interviewData[key]
        }
      })
      
      console.log('从后端获取的面试详情数据:', interviewData)
    } else {
      ElMessage.error(res.message || '获取面试详情失败')
      // 填充表单数据(使用行数据作为备选)
      Object.keys(formData).forEach(key => {
        if (row[key] !== undefined) {
          formData[key] = row[key]
        }
      })
    }
    
    dialogVisible.value = true
  }).catch(error => {
    console.error('获取面试详情失败:', error)
    // 出错时依然可以使用行数据（作为备选）
    Object.keys(formData).forEach(key => {
      if (row[key] !== undefined) {
        formData[key] = row[key]
      }
    })
    
    dialogVisible.value = true
  })
}

// 打开反馈对话框
const handleFeedback = async (row) => {
  // 先重置所有相关数据，防止弹窗内容残留上一次的数据
  feedbackForm.id = '';
  feedbackForm.score = 0;
  feedbackForm.feedback = '';
  feedbackForm.strengths = '';
  feedbackForm.improvements = '';
  feedbackForm.videoUrl = '';
  feedbackForm.isTranslating = false;
  feedbackForm.result = null; // 重置面试结果
  speechChapters.value = [];
  speechRecords.value = [];
  transcriptions.value = [];
  currentChapter.value = null;
  currentChapterRecords.value = [];
  isLoadingChapters.value = false;
  isLoadingRecords.value = false;
  isLoadingTranscriptions.value = false;
  try {
    loading.value = true;
    // 新增：记录面试类型
    feedbackInterviewType.value = row.type;
    // 调用 interviewDetail 接口获取详情
    const res = await interviewDetail(row.id);
    if (res.code === 0 && res.data) {
      // 填充反馈表单
      feedbackForm.id = res.data.id;
      feedbackForm.score = res.data.overallScore || 0;
      feedbackForm.feedback = res.data.feedback || '';
      feedbackForm.strengths = res.data.strengths || '';
      feedbackForm.improvements = res.data.improvements || '';
      feedbackForm.videoUrl = res.data.videoUrl || '';
      feedbackForm.isTranslating = false;
      feedbackForm.result = res.data.result; // 添加获取result值
      // 填充章节和对话内容
      speechChapters.value = res.data.chapters || [];
      speechRecords.value = res.data.records || [];
      // 修正发言人名称
      speechRecords.value.forEach(record => {
        record.speakerName = record.speakerId !== undefined && record.speakerId !== null
          ? `发言人${record.speakerId}`
          : '未知发言人';
      });
      // 默认选中第一个章节
      if (speechChapters.value.length > 0) {
        selectChapter(speechChapters.value[0]);
      } else {
        currentChapter.value = null;
        currentChapterRecords.value = [];
      }
    } else {
      ElMessage.error(res.message || '获取面试详情失败');
    }

    // 新增：获取问答回顾数据
    isLoadingTranscriptions.value = true;
    try {
      const qaRes = await interviewTranscription(row.id);
      if (qaRes.code === 0 && qaRes.data) {
        transcriptions.value = qaRes.data;
      } else {
        transcriptions.value = [];
        ElMessage.error(qaRes.message || '获取问答回顾失败');
      }
    } catch (qaError) {
      transcriptions.value = [];
      console.error('获取问答回顾失败:', qaError);
      ElMessage.error('获取问答回顾失败');
    } finally {
      isLoadingTranscriptions.value = false;
    }
    // 自动切换tab
    activeTab.value = row.type === 'formal' ? 'chapters' : 'qa';
  } catch (error) {
    console.error('获取面试详情失败:', error);
    ElMessage.error('获取面试详情失败');
  } finally {
    loading.value = false;
    feedbackDialogVisible.value = true;
  }
}

// 加载章节段落
const loadSpeechChapters = async (interviewId) => {
  try {
    isLoadingChapters.value = true
    const res = await getSpeechChapters(interviewId)
    if (res.code === 0) {
      speechChapters.value = res.data || []
      if (speechChapters.value.length > 0) {
        selectChapter(speechChapters.value[0])
      }
    } else {
      ElMessage.error(res.message || '获取章节段落失败')
    }
  } catch (error) {
    console.error('获取章节段落失败:', error)
    ElMessage.error('获取章节段落失败')
  } finally {
    isLoadingChapters.value = false
  }
}

// 加载对话内容
const loadSpeechRecords = async (interviewId) => {
  try {
    isLoadingRecords.value = true
    const res = await getSpeechRecords(interviewId)
    if (res.code === 0) {
      speechRecords.value = res.data || []
      
      // 调试: 打印获取到的记录
      console.log('获取到的对话记录:', speechRecords.value)
      
      // 手动设置发言人ID和名称（临时解决方案）
      speechRecords.value.forEach((record, index) => {
        if (!record.speakerId) {
          // 如果没有发言人ID，根据索引设置一个
          const speakerId = index % 2 === 0 ? 1 : 2
          record.speakerId = speakerId
        }
        // 强制设置发言人名称
        record.speakerName = "发言人" + record.speakerId
      })
      
      updateCurrentChapterRecords()
    } else {
      ElMessage.error(res.message || '获取对话内容失败')
    }
  } catch (error) {
    console.error('获取对话内容失败:', error)
    ElMessage.error('获取对话内容失败')
  } finally {
    isLoadingRecords.value = false
  }
}

// 选择章节
const selectChapter = (chapter) => {
  currentChapter.value = chapter
  updateCurrentChapterRecords()
  
  // 跳转到章节开始时间
  jumpToTime(chapter.startTime)
}

// 更新当前章节的对话内容
const updateCurrentChapterRecords = () => {
  if (!currentChapter.value) {
    currentChapterRecords.value = []
    return
  }
  
  // 筛选出当前章节时间范围内的对话记录
  currentChapterRecords.value = speechRecords.value.filter(record => 
    record.startTime >= currentChapter.value.startTime && 
    record.endTime <= currentChapter.value.endTime
  )
}

// 跳转到指定时间位置
const jumpToTime = (timeInMs) => {
  const mediaElement = document.querySelector('video') || document.querySelector('audio')
  if (mediaElement && timeInMs !== undefined) {
    // 将毫秒转换为秒后设置当前播放时间
    mediaElement.currentTime = timeInMs / 1000
  }
}

// 获取发言人标签类型
const getSpeakerTagType = (speakerId) => {
  if (speakerId === undefined || speakerId === null) return 'info'
  
  // 所有发言人使用统一的标签类型
  return 'primary'
}

// 语音转文字
const handleTranslateAudio = async () => {
  try {
    feedbackForm.isTranslating = true;
    
    // 使用固定的测试URL或从表单中获取
    const videoUrl = feedbackForm.videoUrl || "https://example.com/test-audio.mp3";
    
    const res = await translateAudioToText(videoUrl);
    
    if (res.code === 0 && res.data) {
      // 根据实际返回格式调整
      const translatedText = res.data.text || res.data;
      
      // 将转换结果添加到反馈中
      if (feedbackForm.feedback) {
        feedbackForm.feedback += '\n\n语音转文字结果：\n' + translatedText;
      } else {
        feedbackForm.feedback = '语音转文字结果：\n' + translatedText;
      }
      
      ElMessage.success('语音转文字成功');
    } else {
      ElMessage.error(res.message || '语音转文字失败');
    }
  } catch (error) {
    console.error('语音转文字失败:', error);
    ElMessage.error('语音转文字失败');
  } finally {
    feedbackForm.isTranslating = false;
  }
}

// 提交反馈表单
const submitFeedbackForm = () => {
  feedbackFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 构造请求体
        const payload = {
          id: feedbackForm.id,
          overallScore: feedbackForm.score,
          feedback: feedbackForm.feedback,
          strengths: feedbackForm.strengths,
          improvements: feedbackForm.improvements,
          result: feedbackForm.result // 添加面试结果
        }
        // 调用 interviewFeedback 接口
        const res = await interviewFeedback(payload)
        if (res.code === 0) {
          ElMessage.success('评分和反馈已更新')
          feedbackDialogVisible.value = false
          getInterviews()
        } else {
          ElMessage.error(res.message || '更新评分和反馈失败')
        }
      } catch (error) {
        console.error('更新评分和反馈失败:', error)
        ElMessage.error('更新评分和反馈失败')
      }
    }
  })
}

// 处理状态变更
const handleStatusChange = (row, status) => {
  ElMessageBox.confirm(`确认将面试状态变更为"${statusTextMap[status]}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await updateInterviewStatus(row.id, status)
      if (res.code === 0) {
        ElMessage.success('状态更新成功')
        getInterviews()
      } else {
        ElMessage.error(res.message || '状态更新失败')
      }
    } catch (error) {
      console.error('更新面试状态失败:', error)
      ElMessage.error('状态更新失败')
    }
  }).catch(() => {})
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除面试记录 "${row.candidateName}-${row.position}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteInterview(row.id)
      if (res.code === 0) {
        ElMessage.success('删除成功')
        getInterviews()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除面试失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 克隆数据以进行处理
        const data = JSON.parse(JSON.stringify(formData))
        
        if (dialogType.value === 'add') {
          // 添加面试
          const res = await addInterview(data)
          if (res.code === 0) {
            ElMessage.success('添加成功')
            dialogVisible.value = false
            getInterviews()
          } else {
            ElMessage.error(res.message || '添加失败')
          }
        } else {
          // 编辑面试
          const res = await updateInterview(data)
          if (res.code === 0) {
            ElMessage.success('编辑成功')
            dialogVisible.value = false
            getInterviews()
          } else {
            ElMessage.error(res.message || '编辑失败')
          }
        }
      } catch (error) {
        console.error('保存面试失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  formData.id = null
  formData.userId = null
  formData.type = 'mock'
  formData.candidateName = ''
  formData.company = ''
  formData.position = ''
  formData.stage = 'tech'
  formData.experience = 'fresh'
  formData.status = 0
  formData.overallScore = null
  formData.feedback = ''
  formData.strengths = ''
  formData.improvements = ''
  formData.videoUrl = ''
  formData.interviewTime = ''
  formData.questions = []
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return dateTime.replace('T', ' ').substring(0, 19)
}

// 加载问答回顾数据
const loadTranscriptions = async (interviewId) => {
  try {
    isLoadingTranscriptions.value = true
    const res = await getInterviewTranscriptions(interviewId)
    if (res.code === 0) {
      transcriptions.value = res.data || []
      console.log('获取到的问答回顾数据:', transcriptions.value)
    } else {
      ElMessage.error(res.message || '获取问答回顾数据失败')
    }
  } catch (error) {
    console.error('获取问答回顾数据失败:', error)
    ElMessage.warning('接口暂未实现，使用模拟数据')
  } finally {
    isLoadingTranscriptions.value = false
  }
}

// 查看详情的方法
const handleViewDetail = async (row) => {
  try {
    loading.value = true;
    // 调用 interviewDetail 接口获取详情
    const res = await interviewDetail(row.id);
    if (res.code === 0 && res.data) {
      // 填充详情数据
      detailData.score = res.data.overallScore || 0;
      detailData.feedback = res.data.feedback || '';
      detailData.strengths = res.data.strengths || '';
      detailData.improvements = res.data.improvements || '';
      detailData.videoUrl = res.data.videoUrl || '';
      detailData.type = res.data.type || 'mock'; // 新增赋值type
      detailData.result = res.data.result; // 新增赋值result
      // 填充章节和对话内容
      speechChapters.value = res.data.chapters || [];
      speechRecords.value = res.data.records || [];
      // 修正发言人名称
      speechRecords.value.forEach(record => {
        record.speakerName = record.speakerId !== undefined && record.speakerId !== null
          ? `发言人${record.speakerId}`
          : '未知发言人';
      });
      // 默认选中第一个章节
      if (speechChapters.value.length > 0) {
        selectChapter(speechChapters.value[0]);
      } else {
        currentChapter.value = null;
        currentChapterRecords.value = [];
      }
      // 自动切换tab
      activeTab.value = res.data.type === 'formal' ? 'chapters' : 'qa';
      // 新增：获取问答回顾数据
      isLoadingTranscriptions.value = true;
      try {
        const qaRes = await interviewTranscription(row.id);
        if (qaRes.code === 0 && qaRes.data) {
          transcriptions.value = qaRes.data;
        } else {
          transcriptions.value = [];
          ElMessage.error(qaRes.message || '获取问答回顾失败');
        }
      } catch (qaError) {
        transcriptions.value = [];
        console.error('获取问答回顾失败:', qaError);
        ElMessage.error('获取问答回顾失败');
      } finally {
        isLoadingTranscriptions.value = false;
      }
      // 打开详情对话框
      detailDialogVisible.value = true;
    } else {
      ElMessage.error(res.message || '获取面试详情失败');
    }
  } catch (error) {
    console.error('获取面试详情失败:', error);
    ElMessage.error('获取面试详情失败');
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getInterviews()
})
</script>

<template>
  <div class="interview-manage-container">
    <!-- 查询区域 -->
    <el-card class="filter-card">
      <div class="filter-header">
        <span class="filter-title">筛选查询</span>
        <div class="filter-buttons">
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </div>
      </div>
      
      <el-form :model="queryParams" inline>
        <el-form-item label="应聘者姓名">
          <el-input v-model="queryParams.candidateName" placeholder="请输入应聘者姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="面试类型">
          <el-select v-model="queryParams.type" placeholder="请选择面试类型" clearable style="width: 180px;">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="面试职位">
          <el-select v-model="queryParams.position" placeholder="请选择面试职位" clearable style="width: 180px;">
            <el-option
              v-for="item in positionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="面试阶段">
          <el-select v-model="queryParams.stage" placeholder="请选择面试阶段" clearable style="width: 180px;">
            <el-option
              v-for="item in stageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="面试状态">
          <el-select v-model="queryParams.status" placeholder="请选择面试状态" clearable style="width: 180px;">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="面试时间">
          <el-date-picker
            v-model="queryParams.startTime"
            type="datetime"
            placeholder="开始时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 180px;">
          </el-date-picker>
          <span class="time-separator">至</span>
          <el-date-picker
            v-model="queryParams.endTime"
            type="datetime"
            placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 180px;">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 操作与表格区域 -->
    <el-card class="data-card">
      <el-table
        v-loading="loading"
        :data="interviewList"
        border
        stripe
        style="width: 100%"
        row-key="id">
        
        <el-table-column type="index" width="50" align="center" label="序号"></el-table-column>
        <el-table-column prop="candidateName" label="应聘者姓名" width="120"></el-table-column>
        <el-table-column label="面试类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'mock' ? 'info' : 'primary'">
              {{ scope.row.type === 'mock' ? '模拟面试' : '正式面试' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="面试职位" width="150">
          <template #default="scope">
            {{ getPositionLabel(scope.row.position) }}
          </template>
        </el-table-column>
        <el-table-column label="面试阶段" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.stage === 'hr' ? 'success' : 'warning'">
              {{ scope.row.stage === 'hr' ? 'HR面试' : '技术面试' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="工作经验" width="100">
          <template #default="scope">
            {{ getExperienceLabel(scope.row.experience) }}
          </template>
        </el-table-column>
        <el-table-column label="面试状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="评分" width="80">
          <template #default="scope">
            <span v-if="scope.row.overallScore">{{ scope.row.overallScore }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="面试时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.interviewTime) }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleFeedback(scope.row)" v-if="scope.row.status === 0 || scope.row.status === 1">评分反馈</el-button>
            <el-button size="small" type="info" @click="handleViewDetail(scope.row)" v-else-if="scope.row.status === 2">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </el-card>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="650px"
      @close="resetForm">
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px">
        
        <el-form-item label="面试类型" prop="type" >
          <el-select v-model="formData.type" placeholder="请选择面试类型" style="width: 100%">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="应聘者姓名" prop="candidateName">
          <el-input v-model="formData.candidateName" placeholder="请输入应聘者姓名"></el-input>
        </el-form-item>
        
        <el-form-item label="面试公司" prop="company" v-if="formData.type === 'formal'">
          <el-input v-model="formData.company" placeholder="请输入面试公司"></el-input>
        </el-form-item>
        
        <el-form-item label="面试职位" prop="position">
          <el-select v-model="formData.position" placeholder="请选择面试职位" style="width: 100%">
            <el-option
              v-for="item in positionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试阶段" prop="stage">
          <el-select v-model="formData.stage" placeholder="请选择面试阶段" style="width: 100%">
            <el-option
              v-for="item in stageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="工作经验" prop="experience">
          <el-select v-model="formData.experience" placeholder="请选择工作经验" style="width: 100%">
            <el-option
              v-for="item in experienceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试时间" prop="interviewTime">
          <el-date-picker
            v-model="formData.interviewTime"
            type="datetime"
            placeholder="请选择面试时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="视频URL" prop="videoUrl">
          <el-input v-model="formData.videoUrl" placeholder="请输入面试视频URL"></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 评分反馈对话框 -->
    <el-dialog
      v-model="feedbackDialogVisible"
      title="评分与反馈"
      width="800px">
      <!-- 红框区域：没有视频URL时显示的语音转文字按钮 -->
      <!-- 视频/音频播放区域 -->
      <div v-if="feedbackForm.videoUrl" class="media-container">
        <div class="media-header">
          <h3>媒体播放</h3>
          <!-- <el-button 
            type="primary" 
            :icon="Microphone" 
            @click="handleTranslateAudio"
            :loading="feedbackForm.isTranslating"
            :disabled="feedbackForm.isTranslating">
            语音转文字
          </el-button> -->
        </div>
        <div v-if="feedbackForm.videoUrl.endsWith('.mp4') || feedbackForm.videoUrl.endsWith('.webm')">
          <video controls style="width: 100%; max-height: 400px;">
            <source :src="feedbackForm.videoUrl" :type="'video/' + feedbackForm.videoUrl.split('.').pop()">
            您的浏览器不支持视频播放
          </video>
        </div>
        <div v-else-if="feedbackForm.videoUrl.endsWith('.mp3') || feedbackForm.videoUrl.endsWith('.wav')">
          <div class="audio-container">
            <audio controls style="width: 100%">
              <source :src="feedbackForm.videoUrl" :type="'audio/' + feedbackForm.videoUrl.split('.').pop()">
              您的浏览器不支持音频播放
            </audio>
          </div>
        </div>
        <div v-else class="media-url">
          <p>媒体链接: <a :href="feedbackForm.videoUrl" target="_blank">{{ feedbackForm.videoUrl }}</a></p>
        </div>
      </div>
      <!-- 章节段落和对话内容展示区域 -->
      <div class="speech-content-container">
        <div class="speech-header">
          <h3>语音转文字内容</h3>
        </div>
        <el-tabs v-model="activeTab" class="speech-tabs">
          <el-tab-pane
            v-if="feedbackInterviewType === 'formal'"
            label="章节速览"
            name="chapters"
          >
            <div class="speech-content">
              <!-- 左侧章节段落列表 -->
              <div class="speech-chapters">
                <div class="speech-chapters-header">
                  <h4>章节段落</h4>
                  <el-tag v-if="isLoadingChapters" type="info" size="small">加载中...</el-tag>
                </div>
                <div v-if="feedbackForm.videoUrl && speechChapters.length > 0" class="chapter-tip">
                  <el-alert
                    title="点击章节段落或对话记录可跳转到视频/音频对应时间位置（时间单位：毫秒）"
                    type="info"
                    :closable="false"
                    show-icon>
                  </el-alert>
                </div>
                <el-scrollbar height="400px">
                  <div 
                    v-for="chapter in speechChapters" 
                    :key="chapter.id" 
                    class="speech-chapter-item"
                    :class="{ 'active': currentChapter && currentChapter.id === chapter.id }"
                    @click="selectChapter(chapter)">
                    <div class="chapter-title">{{ chapter.chapterTitle }}</div>
                    <div class="chapter-time-container">
                      <div class="chapter-time">{{ formatMilliseconds(chapter.startTime) }} - {{ formatMilliseconds(chapter.endTime) }}</div>
                      <el-button 
                        type="primary" 
                        size="small" 
                        circle
                        class="jump-button"
                        @click.stop="selectChapter(chapter)">
                        <el-icon><VideoPlay /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  <el-empty v-if="!isLoadingChapters && speechChapters.length === 0" description="暂无章节段落"></el-empty>
                </el-scrollbar>
              </div>
              <!-- 右侧对话内容 -->
              <div class="speech-records">
                <div class="speech-records-header">
                  <h4>{{ currentChapter ? currentChapter.chapterTitle : '对话内容' }}</h4>
                  <el-tag v-if="isLoadingRecords" type="info" size="small">加载中...</el-tag>
                </div>
                <el-scrollbar height="400px">
                  <!-- 章节总结 -->
                  <div v-if="currentChapter && currentChapter.chapterSummary" class="chapter-summary">
                    <div class="summary-title">章节总结：</div>
                    <div class="summary-content">{{ currentChapter.chapterSummary }}</div>
                  </div>
                  <!-- 对话记录 -->
                  <div 
                    v-for="record in currentChapterRecords" 
                    :key="record.id" 
                    class="speech-record-item"
                    @click="jumpToTime(record.startTime)">
                    <div class="record-header">
                      <div class="record-speaker">
                        <el-tag size="small" :type="getSpeakerTagType(record.speakerId)">
                          {{ record.speakerName || '发言人0' }}
                        </el-tag>
                      </div>
                      <div class="record-time">
                        {{ formatMilliseconds(record.startTime) }} - {{ formatMilliseconds(record.endTime) }}
                        <el-button 
                          type="primary" 
                          size="small" 
                          circle
                          class="jump-button-small"
                          @click.stop="jumpToTime(record.startTime)">
                          <el-icon><VideoPlay /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div class="record-content">{{ record.content }}</div>
                  </div>
                  <el-empty 
                    v-if="!isLoadingRecords && (!currentChapter || currentChapterRecords.length === 0)" 
                    description="暂无对话内容">
                  </el-empty>
                </el-scrollbar>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="问答回顾" name="qa">
            <div class="qa-content">
              <div v-if="isLoadingTranscriptions" class="loading-container">
                <el-skeleton :rows="5" animated />
              </div>
              <div v-else-if="transcriptions.length === 0" class="empty-container">
                <el-empty description="暂无问答记录"></el-empty>
              </div>
              <div v-else class="qa-list">
                <el-timeline>
                  <el-timeline-item
                    v-for="(item, index) in transcriptions"
                    :key="index"
                    :timestamp="formatDateTime(item.createTime)"
                    placement="top"
                    :type="index % 2 === 0 ? 'primary' : 'success'">
                    <el-card class="qa-card">
                      <div class="question">
                        <div class="question-label">问题 {{ item.questionIndex + 1 }}:</div>
                        <div class="question-content">{{ item.question }}</div>
                      </div>
                      <div class="answer">
                        <div class="answer-label">回答:</div>
                        <div class="answer-content">{{ item.transcription }}</div>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <el-form
        ref="feedbackFormRef"
        :model="feedbackForm"
        label-width="100px">
        
        <el-form-item label="总体评分" prop="score">
          <el-slider
            v-model="feedbackForm.score"
            :min="0"
            :max="100"
            :step="1"
            show-input
            style="width: 100%">
          </el-slider>
        </el-form-item>
        
        <el-form-item label="面试结果" prop="result">
          <el-radio-group v-model="feedbackForm.result">
            <el-radio :label="1">面试通过</el-radio>
            <el-radio :label="0">面试未通过</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="面试反馈" prop="feedback">
          <el-input
            v-model="feedbackForm.feedback"
            type="textarea"
            :rows="3"
            placeholder="请输入面试整体反馈">
          </el-input>
        </el-form-item>
        
        <el-form-item label="优势表现" prop="strengths">
          <el-input
            v-model="feedbackForm.strengths"
            type="textarea"
            :rows="3"
            placeholder="请输入应聘者的优势表现">
          </el-input>
        </el-form-item>
        
        <el-form-item label="改进建议" prop="improvements">
          <el-input
            v-model="feedbackForm.improvements"
            type="textarea"
            :rows="3"
            placeholder="请输入应聘者的改进建议">
          </el-input>
        </el-form-item>

        <el-divider>预览</el-divider>
        
        <!-- 添加预览区域 -->
        <InterviewFeedbackDetail
          :score="feedbackForm.score"
          :feedback="feedbackForm.feedback"
          :strengths="feedbackForm.strengths"
          :improvements="feedbackForm.improvements"
          :result="feedbackForm.result"
        />
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="feedbackDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitFeedbackForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 面试详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="面试详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="media-container" v-if="detailData.videoUrl">
        <div class="media-header">
          <h3>媒体播放</h3>
        </div>
        <div v-if="detailData.videoUrl.endsWith('.mp4') || detailData.videoUrl.endsWith('.webm')">
          <video controls style="width: 100%; max-height: 400px;">
            <source :src="detailData.videoUrl" :type="'video/' + detailData.videoUrl.split('.').pop()">
            您的浏览器不支持视频播放
          </video>
        </div>
        <div v-else-if="detailData.videoUrl.endsWith('.mp3') || detailData.videoUrl.endsWith('.wav')">
          <audio controls style="width: 100%">
            <source :src="detailData.videoUrl" :type="'audio/' + detailData.videoUrl.split('.').pop()">
            您的浏览器不支持音频播放
          </audio>
        </div>
        <div v-else class="media-url">
          <p>媒体链接: <a :href="detailData.videoUrl" target="_blank">{{ detailData.videoUrl }}</a></p>
        </div>
      </div>
      <div class="speech-content-container">
        <div class="speech-header">
          <h3>语音转文字内容</h3>
        </div>
        <el-tabs v-model="activeTab" class="speech-tabs">
          <el-tab-pane
            v-if="detailData.type === 'formal'"
            label="章节速览"
            name="chapters"
          >
            <div class="speech-content">
              <div class="speech-chapters">
                <div class="speech-chapters-header">
                  <h4>章节段落</h4>
                  <el-tag v-if="isLoadingChapters" type="info" size="small">加载中...</el-tag>
                </div>
                <el-scrollbar height="400px">
                  <div 
                    v-for="chapter in speechChapters" 
                    :key="chapter.id" 
                    class="speech-chapter-item"
                    :class="{ 'active': currentChapter && currentChapter.id === chapter.id }"
                    @click="selectChapter(chapter)">
                    <div class="chapter-title">{{ chapter.chapterTitle }}</div>
                    <div class="chapter-time-container">
                      <div class="chapter-time">{{ formatMilliseconds(chapter.startTime) }} - {{ formatMilliseconds(chapter.endTime) }}</div>
                      <el-button 
                        type="primary" 
                        size="small" 
                        circle
                        class="jump-button"
                        @click.stop="selectChapter(chapter)">
                        <el-icon><VideoPlay /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  <el-empty v-if="!isLoadingChapters && speechChapters.length === 0" description="暂无章节段落"></el-empty>
                </el-scrollbar>
              </div>
              <div class="speech-records">
                <div class="speech-records-header">
                  <h4>{{ currentChapter ? currentChapter.chapterTitle : '对话内容' }}</h4>
                  <el-tag v-if="isLoadingRecords" type="info" size="small">加载中...</el-tag>
                </div>
                <el-scrollbar height="400px">
                  <div v-if="currentChapter && currentChapter.chapterSummary" class="chapter-summary">
                    <div class="summary-title">章节总结：</div>
                    <div class="summary-content">{{ currentChapter.chapterSummary }}</div>
                  </div>
                  <div 
                    v-for="record in currentChapterRecords" 
                    :key="record.id" 
                    class="speech-record-item"
                    @click="jumpToTime(record.startTime)">
                    <div class="record-header">
                      <div class="record-speaker">
                        <el-tag size="small" :type="getSpeakerTagType(record.speakerId)">
                          {{ record.speakerName || '发言人0' }}
                        </el-tag>
                      </div>
                      <div class="record-time">
                        {{ formatMilliseconds(record.startTime) }} - {{ formatMilliseconds(record.endTime) }}
                        <el-button 
                          type="primary" 
                          size="small" 
                          circle
                          class="jump-button-small"
                          @click.stop="jumpToTime(record.startTime)">
                          <el-icon><VideoPlay /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div class="record-content">{{ record.content }}</div>
                  </div>
                  <el-empty 
                    v-if="!isLoadingRecords && (!currentChapter || currentChapterRecords.length === 0)" 
                    description="暂无对话内容">
                  </el-empty>
                </el-scrollbar>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="问答回顾" name="qa">
            <div class="qa-content">
              <div v-if="isLoadingTranscriptions" class="loading-container">
                <el-skeleton :rows="5" animated />
              </div>
              <div v-else-if="transcriptions.length === 0" class="empty-container">
                <el-empty description="暂无问答记录"></el-empty>
              </div>
              <div v-else class="qa-list">
                <el-timeline>
                  <el-timeline-item
                    v-for="(item, index) in transcriptions"
                    :key="index"
                    :timestamp="formatDateTime(item.createTime)"
                    placement="top"
                    :type="index % 2 === 0 ? 'primary' : 'success'">
                    <el-card class="qa-card">
                      <div class="question">
                        <div class="question-label">问题 {{ item.questionIndex + 1 }}:</div>
                        <div class="question-content">{{ item.question }}</div>
                      </div>
                      <div class="answer">
                        <div class="answer-label">回答:</div>
                        <div class="answer-content">{{ item.transcription }}</div>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <el-form label-width="100px">
        <el-form-item label="总体评分">
          <el-slider v-model="detailData.score" :min="0" :max="100" :step="1" show-input style="width: 100%" disabled />
        </el-form-item>
        <el-form-item label="面试反馈">
          <el-input v-model="detailData.feedback" type="textarea" :rows="3" placeholder="请输入面试整体反馈" disabled />
        </el-form-item>
        <el-form-item label="优势表现">
          <el-input v-model="detailData.strengths" type="textarea" :rows="3" placeholder="请输入应聘者的优势表现" disabled />
        </el-form-item>
        <el-form-item label="改进建议">
          <el-input v-model="detailData.improvements" type="textarea" :rows="3" placeholder="请输入应聘者的改进建议" disabled />
        </el-form-item>
        <el-divider>预览</el-divider>
        <InterviewFeedbackDetail
          :score="detailData.score"
          :feedback="detailData.feedback"
          :strengths="detailData.strengths"
          :improvements="detailData.improvements"
          :result="detailData.result"
        />
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped>
.interview-manage-container {
  padding: 10px;
}

.filter-card,
.data-card {
  margin-bottom: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.filter-title {
  font-size: 16px;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.time-separator {
  padding: 0 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.media-container {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f8f8;
}

.media-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 10px;
}

.media-header h3 {
  margin: 0;
  color: #409eff;
}

.media-url {
  padding: 10px;
  background-color: #f2f6fc;
  border-radius: 4px;
}

.audio-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.audio-transcribe-container {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f8f8;
}

.speech-content-container {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f8f8;
}

.speech-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 10px;
}

.speech-header h3 {
  margin: 0;
  color: #409eff;
}

.speech-content {
  display: flex;
}

.speech-chapters {
  width: 200px;
  margin-right: 20px;
}

.speech-chapters-header {
  margin-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
}

.speech-chapters-header h4 {
  margin: 0;
  color: #409eff;
}

.speech-records {
  flex: 1;
}

.speech-records-header {
  margin-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
}

.speech-records-header h4 {
  margin: 0;
  color: #409eff;
}

.chapter-summary {
  margin-bottom: 10px;
}

.summary-title {
  margin: 0;
  color: #409eff;
}

.summary-content {
  margin: 5px 0;
}

.speech-record-item {
  margin-bottom: 5px;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.speech-record-item:hover {
  background-color: #f5f7fa;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-speaker {
  margin: 0;
}

.record-time {
  font-size: 12px;
  color: #606266;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-content {
  font-size: 14px;
  color: #303133;
  margin-top: 5px;
}

.jump-button-small {
  transform: scale(0.8);
}

.speech-chapter-item {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.speech-chapter-item:hover {
  background-color: #f5f7fa;
}

.speech-chapter-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.chapter-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.chapter-time-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-time {
  font-size: 12px;
}

.jump-button {
  margin-left: 10px;
}

:deep(.el-tag) {
  margin-right: 5px;
}

.chapter-tip {
  margin-bottom: 10px;
}

.qa-content {
  padding: 20px;
}

.loading-container {
  text-align: center;
  padding: 20px;
}

.empty-container {
  text-align: center;
  padding: 20px;
}

.qa-list {
  margin-top: 20px;
}

.qa-card {
  margin-bottom: 10px;
}

.question {
  margin-bottom: 5px;
}

.question-label {
  font-weight: bold;
}

.question-content {
  margin-left: 10px;
}

.answer {
  margin-top: 5px;
}

.answer-label {
  font-weight: bold;
}

.answer-content {
  margin-left: 10px;
}

.audio-player {
  margin-top: 10px;
}

</style> 