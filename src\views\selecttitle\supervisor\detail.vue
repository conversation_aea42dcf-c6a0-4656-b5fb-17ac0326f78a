<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>导师信息</span>
          <el-button @click="goBack">返回</el-button>
        </div>
      </template>
      <div class="supervisor-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="导师姓名">{{ supervisorInfo.supervisorName }}</el-descriptions-item>
          <el-descriptions-item label="所属院系">{{ supervisorInfo.department }}</el-descriptions-item>
          <el-descriptions-item label="职称">{{ supervisorInfo.title }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ supervisorInfo.email }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ supervisorInfo.phoneNumber }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>毕设题目列表</span>
        </div>
      </template>
      <div v-loading="loading">
        <el-table
          v-if="topicList.length > 0"
          :data="topicList"
          style="width: 100%"
          border>
          <el-table-column
            prop="nickname"
            label="选题学生"
            width="120">
          </el-table-column>
          <el-table-column
            prop="title"
            label="题目名称"
            min-width="200">
          </el-table-column>
          <el-table-column
            prop="description"
            label="题目描述"
            min-width="250">
          </el-table-column>
          <el-table-column
            prop="categoryName"
            label="类别"
            width="120">
          </el-table-column>
          <el-table-column
            prop="tag"
            label="标签"
            width="150">
          </el-table-column>
         
          <el-table-column
            prop="publishDate"
            label="发布时间"
            width="160">
          </el-table-column>
          <el-table-column
            label="状态"
            width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'" size="small">
                {{ scope.row.status === 1 ? '可选' : '已选' }}
              </el-tag>
            </template>
          </el-table-column>
        
        </el-table>
        <el-empty v-else description="暂无题目" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="SupervisorDetail">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getSupervisor } from "@/api/selecttitle/supervisor"

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const supervisorInfo = ref({})
const topicList = ref([])

/** 获取导师详情 */
function getDetail() {
  loading.value = true
  // 从路由params中获取导师ID
  const supervisorId = route.params.supervisorId
  if (!supervisorId) {
    proxy.$modal.msgError("获取导师ID失败")
    router.go(-1)
    return
  }

  getSupervisor(supervisorId).then(response => {
    if (response.code === 200) {
      console.log('获取到的数据:', response.data)
      supervisorInfo.value = response.data.supervisor
      topicList.value = response.data.topics || []
      console.log('处理后的题目列表:', topicList.value)
    } else {
      proxy.$modal.msgError(response.message || '获取导师详情失败')
    }
  }).catch(error => {
    console.error('获取导师详情错误:', error)
    proxy.$modal.msgError('获取导师详情失败')
  }).finally(() => {
    loading.value = false
  })
}

/** 返回按钮操作 */
function goBack() {
  router.go(-1)
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.supervisor-info {
  margin: 20px 0;
}
</style> 