import request from '@/utils/request'

// 查询题库管理列表
export function listQuestions(query) {
  return request({
    url: '/aqsystem/questions/list',
    method: 'get',
    params: query
  })
}


// 新增题库管理
export function addQuestions(data) {
  return request({
    url: '/aqsystem/questions',
    method: 'post',
    data: data
  })
}

// 修改题库管理
export function updateQuestions(data) {
  return request({
    url: '/aqsystem/questions',
    method: 'put',
    data: data
  })
}

// 删除题库管理
export function delQuestions(questionId) {
  return request({
    url: '/aqsystem/questions/' + questionId,
    method: 'delete'
  })
}
// 类目
export function getCategoryList() {
  return request({
    url: 'aqsystem/classification',
    method: 'get'
  })
}

// 考试题库导入 /aqsystem/questions/import
export function importQuestions(data) {
  return request({
    url: '/aqsystem/questions/import',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

