import request from '@/utils/request'

// 查询学生管理列表
export function listUsers(query) {
  return request({
    url: '/system/users/list',
    method: 'get',
    params: query
  })
}

// 查询学生管理详细
export function getUsers(userId) {
  return request({
    url: '/system/users/' + userId,
    method: 'get'
  })
}

// 新增学生管理
export function addUsers(data) {
  return request({
    url: '/system/users',
    method: 'post',
    data: data
  })
}

// 修改学生管理
export function updateUsers(data) {
  return request({
    url: '/system/users',
    method: 'put',
    data: data
  })
}

// 删除学生管理
export function delUsers(userId) {
  return request({
    url: '/system/users/' + userId,
    method: 'delete'
  })
}
