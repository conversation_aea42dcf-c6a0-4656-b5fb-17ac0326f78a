import{Z as b,_ as ne,B as ue,d as se,r as c,C as re,a5 as ie,e as d,G as N,c as de,o as w,J as v,f as t,a4 as D,l as a,h as o,m as T,n as y,j as C,D as P,i as k,t as I}from"./index-BJsoK47l.js";function me(m){return b({url:"/resume/questions/hrQuestionList",method:"post",data:m})}function pe(m){return b({url:"/resume/questions/hrQuestion/"+m,method:"get"})}function ce(m){return b({url:"/resume/questions/add",method:"post",data:m})}function fe(m){return b({url:"/resume/questions/update",method:"put",data:m})}function ge(m){return b({url:"/resume/questions/delete/"+m,method:"delete"})}const _e={class:"app-container"},qe={style:{"max-width":"400px","white-space":"pre-wrap"}},ye={class:"text-ellipsis"},he={class:"dialog-footer"},ve=ue({name:"Questions"}),we=Object.assign(ve,{setup(m){const{proxy:i}=se(),Q=c([]),f=c(!1),x=c(!0),V=c(!0),S=c([]),B=c(!0),K=c(!0),A=c(0),U=c(""),H=re({form:{},queryParams:{pageNum:1,pageSize:10,questionType:null,question:null,answer:null,createAt:null},rules:{questionType:[{required:!0,message:"问题类型不能为空",trigger:"change"}],question:[{required:!0,message:"题目内容不能为空",trigger:"blur"}],answer:[{required:!0,message:"题目答案不能为空",trigger:"blur"}],createAt:[{required:!0,message:"创建人不能为空",trigger:"blur"}]}}),{queryParams:s,form:r,rules:z}=ie(H);function g(){x.value=!0,me(s.value).then(u=>{u.code===0?(Q.value=u.data,A.value=u.data.length):i.$modal.msgError(u.message||"获取数据失败"),x.value=!1})}function F(){f.value=!1,R()}function R(){r.value={queId:null,questionType:"HR问题",question:null,answer:null,createAt:null,updateTime:null},i.resetForm("questionsRef")}function h(){s.value.pageNum=1,g()}function L(){i.resetForm("queryRef"),h()}function j(u){S.value=u.map(e=>e.queId),B.value=u.length!=1,K.value=!u.length}function G(){R(),f.value=!0,U.value="添加HR问题"}function J(u){R();const e=u.queId||S.value;pe(e).then(n=>{n.code===0?(r.value={queId:n.data.queId,questionType:n.data.questionType,question:n.data.question,answer:n.data.answer,createAt:n.data.createAt,updateTime:n.data.updateTime},f.value=!0,U.value="修改HR问题"):i.$modal.msgError(n.message||"获取数据失败")})}function O(){i.$refs.questionsRef.validate(u=>{u&&(r.value.queId!=null?fe(r.value).then(e=>{e.code===0?(i.$modal.msgSuccess("修改成功"),f.value=!1,g()):i.$modal.msgError(e.message||"修改失败")}):ce(r.value).then(e=>{e.code===0?(i.$modal.msgSuccess("新增成功"),f.value=!1,g()):i.$modal.msgError(e.message||"新增失败")}))})}function Z(u){const e=u.queId||S.value;i.$modal.confirm('是否确认删除HR问题编号为"'+e+'"的数据项？').then(function(){return ge(e)}).then(n=>{n.code===0?(g(),i.$modal.msgSuccess("删除成功")):i.$modal.msgError(n.message||"删除失败")}).catch(()=>{})}return g(),(u,e)=>{const n=d("el-input"),p=d("el-form-item"),_=d("el-button"),E=d("el-form"),M=d("el-col"),W=d("right-toolbar"),X=d("el-row"),q=d("el-table-column"),Y=d("el-tooltip"),ee=d("el-table"),te=d("pagination"),le=d("el-dialog"),$=N("hasPermi"),ae=N("loading");return w(),de("div",_e,[v(t(E,{model:a(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[t(p,{label:"问题类型",prop:"questionType"},{default:o(()=>[t(n,{modelValue:a(s).questionType,"onUpdate:modelValue":e[0]||(e[0]=l=>a(s).questionType=l),placeholder:"请输入问题类型",clearable:"",onKeyup:T(h,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"题目内容",prop:"question"},{default:o(()=>[t(n,{modelValue:a(s).question,"onUpdate:modelValue":e[1]||(e[1]=l=>a(s).question=l),placeholder:"请输入题目内容",clearable:"",onKeyup:T(h,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"题目答案",prop:"answer"},{default:o(()=>[t(n,{modelValue:a(s).answer,"onUpdate:modelValue":e[2]||(e[2]=l=>a(s).answer=l),placeholder:"请输入题目答案",clearable:"",onKeyup:T(h,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"创建人",prop:"createAt"},{default:o(()=>[t(n,{modelValue:a(s).createAt,"onUpdate:modelValue":e[3]||(e[3]=l=>a(s).createAt=l),placeholder:"请输入创建人",clearable:"",onKeyup:T(h,["enter"])},null,8,["modelValue"])]),_:1}),t(p,null,{default:o(()=>[t(_,{type:"primary",icon:"Search",onClick:h},{default:o(()=>[y("搜索")]),_:1}),t(_,{icon:"Refresh",onClick:L},{default:o(()=>[y("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[D,a(V)]]),t(X,{gutter:10,class:"mb8"},{default:o(()=>[t(M,{span:1.5},{default:o(()=>[v((w(),C(_,{type:"primary",plain:"",icon:"Plus",onClick:G},{default:o(()=>[y("新增")]),_:1})),[[$,["system:questions:add"]]])]),_:1}),t(W,{showSearch:a(V),"onUpdate:showSearch":e[4]||(e[4]=l=>P(V)?V.value=l:null),onQueryTable:g},null,8,["showSearch"])]),_:1}),v((w(),C(ee,{data:a(Q),onSelectionChange:j},{default:o(()=>[t(q,{type:"selection",width:"55",align:"center"}),t(q,{label:"问题类型",align:"center",prop:"questionType",width:"100"}),t(q,{label:"题目内容",align:"center",prop:"question",width:"290"}),t(q,{label:"题目答案",align:"center",prop:"answer"},{default:o(l=>[t(Y,{effect:"dark",placement:"top"},{content:o(()=>[k("div",qe,I(l.row.answer),1)]),default:o(()=>[k("div",ye,I(l.row.answer),1)]),_:2},1024)]),_:1}),t(q,{label:"创建人",align:"center",prop:"createAt"}),t(q,{label:"更新时间",align:"center",prop:"updateTime",width:"180"},{default:o(l=>[k("span",null,I(u.parseTime(l.row.updateTime,"{y}-{m}-{d}")),1)]),_:1}),t(q,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:o(l=>[v((w(),C(_,{link:"",type:"primary",icon:"Edit",onClick:oe=>J(l.row)},{default:o(()=>[y("修改")]),_:2},1032,["onClick"])),[[$,["system:questions:edit"]]]),v((w(),C(_,{link:"",type:"primary",icon:"Delete",onClick:oe=>Z(l.row)},{default:o(()=>[y("删除")]),_:2},1032,["onClick"])),[[$,["system:questions:remove"]]])]),_:1})]),_:1},8,["data"])),[[ae,a(x)]]),v(t(te,{total:a(A),page:a(s).pageNum,"onUpdate:page":e[5]||(e[5]=l=>a(s).pageNum=l),limit:a(s).pageSize,"onUpdate:limit":e[6]||(e[6]=l=>a(s).pageSize=l),onPagination:g},null,8,["total","page","limit"]),[[D,a(A)>0]]),t(le,{title:a(U),modelValue:a(f),"onUpdate:modelValue":e[11]||(e[11]=l=>P(f)?f.value=l:null),width:"500px","append-to-body":""},{footer:o(()=>[k("div",he,[t(_,{type:"primary",onClick:O},{default:o(()=>[y("确 定")]),_:1}),t(_,{onClick:F},{default:o(()=>[y("取 消")]),_:1})])]),default:o(()=>[t(E,{ref:"questionsRef",model:a(r),rules:a(z),"label-width":"80px"},{default:o(()=>[t(p,{label:"问题类型",prop:"questionType"},{default:o(()=>[t(n,{modelValue:a(r).questionType,"onUpdate:modelValue":e[7]||(e[7]=l=>a(r).questionType=l),placeholder:"请输入问题类型"},null,8,["modelValue"])]),_:1}),t(p,{label:"题目内容"},{default:o(()=>[t(n,{modelValue:a(r).question,"onUpdate:modelValue":e[8]||(e[8]=l=>a(r).question=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),t(p,{label:"题目答案",prop:"answer"},{default:o(()=>[t(n,{modelValue:a(r).answer,"onUpdate:modelValue":e[9]||(e[9]=l=>a(r).answer=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),t(p,{label:"创建人",prop:"createAt"},{default:o(()=>[t(n,{modelValue:a(r).createAt,"onUpdate:modelValue":e[10]||(e[10]=l=>a(r).createAt=l),placeholder:"请输入创建人"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Ve=ne(we,[["__scopeId","data-v-930c7e03"]]);export{Ve as default};
