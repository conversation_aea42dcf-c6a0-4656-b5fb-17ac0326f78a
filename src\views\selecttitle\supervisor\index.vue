<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="导师姓名" prop="supervisorName">
        <el-input
          v-model="queryParams.supervisorName"
          placeholder="请输入导师姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:supervisor:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:supervisor:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:supervisor:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:supervisor:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="supervisorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="导师ID" align="center" prop="supervisorId" />
      <el-table-column label="导师姓名" align="center" prop="supervisorName" />
      <el-table-column label="所属院系" align="center" prop="department" />
      <el-table-column label="职称" align="center" prop="title" />
      <el-table-column label="邮箱" align="center" prop="email" />
      <el-table-column label="电话" align="center" prop="phoneNumber" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['system:supervisor:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:supervisor:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:supervisor:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改导师信息，用于记录导师的基本资料对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="supervisorRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="导师姓名" prop="supervisorName">
          <el-input v-model="form.supervisorName" placeholder="请输入导师姓名" />
        </el-form-item>
        <el-form-item label="所属院系" prop="department">
          <el-input v-model="form.department" placeholder="请输入所属院系" />
        </el-form-item>
        <el-form-item label="职称" prop="title">
          <el-input v-model="form.title" placeholder="请输入职称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="电话" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入电话" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Supervisor">
import { listSupervisor, getSupervisor, delSupervisor, addSupervisor, updateSupervisor } from "@/api/selecttitle/supervisor"

const { proxy } = getCurrentInstance()

const supervisorList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const submitLoading = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    supervisorName: null,
    department: null,
    title: null,
    email: null,
    phoneNumber: null,
    editTime: null,
    isDelete: null
  },
  rules: {
    supervisorName: [
      { required: true, message: "导师姓名不能为空", trigger: "blur" }
    ],
    department: [
      { required: true, message: "所属院系不能为空", trigger: "change" }
    ],
    title: [
      { required: true, message: "职称，如教授、副教授等不能为空", trigger: "blur" }
    ],
    email: [
      { required: true, message: "导师邮箱，具有唯一性不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询导师信息列表 */
function getList() {
  loading.value = true
  listSupervisor(queryParams.value).then(response => {
    if (response.code === 200) {
      // 直接将返回的导师列表和总数赋值给对应的变量
      supervisorList.value = response.data.topics || [] // 确保是数组
      total.value = response.data.total || 0 // 确保是数字
    } else {
      proxy.$modal.msgError(response.message || '获取导师列表失败')
    }
    loading.value = false
  }).catch(error => {
    console.error('获取导师列表错误:', error)
    proxy.$modal.msgError('获取导师列表失败')
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    supervisorId: null,
    supervisorName: null,
    department: null,
    title: null,
    email: null,
    phoneNumber: null,
    createTime: null,
    editTime: null,
    isDelete: null
  }
  proxy.resetForm("supervisorRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.supervisorId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加导师信息，用于记录导师的基本资料"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _supervisorId = row.supervisorId || ids.value
  getSupervisor(_supervisorId).then(response => {
    if (response.code === 200) {
      form.value = {
        supervisorId: response.data.supervisor.supervisorId,
        supervisorName: response.data.supervisor.supervisorName,
        department: response.data.supervisor.department,
        title: response.data.supervisor.title,
        email: response.data.supervisor.email,
        phoneNumber: response.data.supervisor.phoneNumber,
        createTime: response.data.supervisor.createTime,
        editTime: response.data.supervisor.editTime,
        isDelete: response.data.supervisor.isDelete
      }
      open.value = true
      title.value = "修改导师信息"
    } else {
      proxy.$modal.msgError(response.message || '获取导师信息失败')
    }
  }).catch(error => {
    console.error('获取导师信息错误:', error)
    proxy.$modal.msgError('获取导师信息失败')
  })
}

/** 提交按钮 */
function submitForm() {
  if (submitLoading.value) return  // 如果正在提交，直接返回
  proxy.$refs["supervisorRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true  // 开始提交，设置loading状态
      const currentTime = new Date().toISOString().slice(0, 19)
      if (form.value.supervisorId != null) {
        // 修改操作
        const updateData = {
          ...form.value,
          editTime: currentTime
        }
        updateSupervisor(updateData).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        }).catch(error => {
          proxy.$modal.msgError(error.message || "修改失败")
        }).finally(() => {
          submitLoading.value = false  // 无论成功失败，都关闭loading状态
        })
      } else {
        // 新增操作
        const addData = {
          ...form.value,
          createTime: currentTime,
          editTime: currentTime,
          isDelete: 0
        }
        addSupervisor(addData).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        }).catch(error => {
          proxy.$modal.msgError(error.message || "新增失败")
        }).finally(() => {
          submitLoading.value = false  // 无论成功失败，都关闭loading状态
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _supervisorIds = row.supervisorId || ids.value
  proxy.$modal.confirm('是否确认删除导师信息编号为"' + _supervisorIds + '"的数据项？').then(function() {
    return delSupervisor(_supervisorIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/supervisor/export', {
    ...queryParams.value
  }, `supervisor_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  const supervisorId = row.supervisorId
  if (!supervisorId) {
    proxy.$modal.msgError("获取导师ID失败")
    return
  }
  
  // 直接跳转到详情页面
  proxy.$router.push({
    name: 'SupervisorDetail',  // 使用命名路由
    params: { 
      supervisorId: supervisorId // 使用params传递动态路径参数
    }
  })
}

getList()
</script>
