import{B as le,C as ye,bi as ft,f as c,w as N,F as ne,bs as _e,z as xe,r as te,b4 as W,H as Bt,bf as Xn,a6 as it,aC as Yn,bO as Ot,bb as Jn}from"./index-BJsoK47l.js";var ei=Object.defineProperty,ti=(n,e,t)=>e in n?ei(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,re=(n,e,t)=>(ti(n,typeof e!="symbol"?e+"":e,t),t),en=(n,e,t)=>{if(!e.has(n))throw TypeError("Cannot "+t)},a=(n,e,t)=>(en(n,e,"read from private field"),t?t.call(n):e.get(n)),B=(n,e,t)=>{if(e.has(n))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(n):e.set(n,t)},R=(n,e,t,i)=>(en(n,e,"write to private field"),e.set(n,t),t),Vt=(n,e,t,i)=>({set _(l){R(n,e,l)},get _(){return a(n,e,i)}}),F=(n,e,t)=>(en(n,e,"access private method"),t);const h="md-editor",ni="md-editor-v3",ii="https://at.alicdn.com/t/c/font_2605852_u82y61ve02.js",G="https://cdnjs.cloudflare.com/ajax/libs",li=`${G}/highlight.js/11.7.0/highlight.min.js`,un={main:`${G}/prettier/2.8.0/standalone.js`,markdown:`${G}/prettier/2.8.0/parser-markdown.js`},dn={css:`${G}/cropperjs/1.5.13/cropper.min.css`,js:`${G}/cropperjs/1.5.13/cropper.min.js`},ri=`${G}/screenfull.js/5.2.0/screenfull.min.js`,In=["bold","underline","italic","strikeThrough","-","title","sub","sup","quote","unorderedList","orderedList","task","-","codeRow","code","link","image","table","mermaid","katex","-","revoke","next","save","=","prettier","pageFullscreen","fullscreen","preview","htmlPreview","catalog","github"],On=["markdownTotal","=","scrollSwitch"],hn={"zh-CN":{toolbarTips:{bold:"加粗",underline:"下划线",italic:"斜体",strikeThrough:"删除线",title:"标题",sub:"下标",sup:"上标",quote:"引用",unorderedList:"无序列表",orderedList:"有序列表",task:"任务列表",codeRow:"行内代码",code:"块级代码",link:"链接",image:"图片",table:"表格",mermaid:"mermaid图",katex:"katex公式",revoke:"后退",next:"前进",save:"保存",prettier:"美化",pageFullscreen:"浏览器全屏",fullscreen:"屏幕全屏",preview:"预览",htmlPreview:"html代码预览",catalog:"目录",github:"源码地址"},titleItem:{h1:"一级标题",h2:"二级标题",h3:"三级标题",h4:"四级标题",h5:"五级标题",h6:"六级标题"},imgTitleItem:{link:"添加链接",upload:"上传图片",clip2upload:"裁剪上传"},linkModalTips:{linkTitle:"添加链接",imageTitle:"添加图片",descLabel:"链接描述：",descLabelPlaceHolder:"请输入描述...",urlLabel:"链接地址：",urlLabelPlaceHolder:"请输入链接...",buttonOK:"确定"},clipModalTips:{title:"裁剪图片上传",buttonUpload:"上传"},copyCode:{text:"复制代码",successTips:"已复制！",failTips:"复制失败！"},mermaid:{flow:"流程图",sequence:"时序图",gantt:"甘特图",class:"类图",state:"状态图",pie:"饼图",relationship:"关系图",journey:"旅程图"},katex:{inline:"行内公式",block:"块级公式"},footer:{markdownTotal:"字数",scrollAuto:"同步滚动"}},"en-US":{toolbarTips:{bold:"bold",underline:"underline",italic:"italic",strikeThrough:"strikeThrough",title:"title",sub:"subscript",sup:"superscript",quote:"quote",unorderedList:"unordered list",orderedList:"ordered list",task:"task list",codeRow:"inline code",code:"block-level code",link:"link",image:"image",table:"table",mermaid:"mermaid",katex:"formula",revoke:"revoke",next:"undo revoke",save:"save",prettier:"prettier",pageFullscreen:"fullscreen in page",fullscreen:"fullscreen",preview:"preview",htmlPreview:"html preview",catalog:"catalog",github:"source code"},titleItem:{h1:"Lv1 Heading",h2:"Lv2 Heading",h3:"Lv3 Heading",h4:"Lv4 Heading",h5:"Lv5 Heading",h6:"Lv6 Heading"},imgTitleItem:{link:"Add Img Link",upload:"Upload Img",clip2upload:"Clip Upload"},linkModalTips:{linkTitle:"Add Link",imageTitle:"Add Image",descLabel:"Desc:",descLabelPlaceHolder:"Enter a description...",urlLabel:"Link:",urlLabelPlaceHolder:"Enter a link...",buttonOK:"OK"},clipModalTips:{title:"Crop Image",buttonUpload:"Upload"},copyCode:{text:"Copy",successTips:"Copied!",failTips:"Copy failed!"},mermaid:{flow:"flow",sequence:"sequence",gantt:"gantt",class:"class",state:"state",pie:"pie",relationship:"relationship",journey:"journey"},katex:{inline:"inline",block:"block"},footer:{markdownTotal:"Word Count",scrollAuto:"Scroll Auto"}}},oi=`${G}/mermaid/9.4.0/mermaid.min.js`,pn={js:`${G}/KaTeX/0.16.3/katex.min.js`,css:`${G}/KaTeX/0.16.3/katex.min.css`},mn={a11y:{light:`${G}/highlight.js/11.7.0/styles/a11y-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/a11y-dark.min.css`},atom:{light:`${G}/highlight.js/11.7.0/styles/atom-one-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/atom-one-dark.min.css`},github:{light:`${G}/highlight.js/11.7.0/styles/github.min.css`,dark:`${G}/highlight.js/11.7.0/styles/github-dark.min.css`},gradient:{light:`${G}/highlight.js/11.7.0/styles/gradient-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/gradient-dark.min.css`},kimbie:{light:`${G}/highlight.js/11.7.0/styles/kimbie-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/kimbie-dark.min.css`},paraiso:{light:`${G}/highlight.js/11.7.0/styles/paraiso-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/paraiso-dark.min.css`},qtcreator:{light:`${G}/highlight.js/11.7.0/styles/qtcreator-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/qtcreator-dark.min.css`},stackoverflow:{light:`${G}/highlight.js/11.7.0/styles/stackoverflow-light.min.css`,dark:`${G}/highlight.js/11.7.0/styles/stackoverflow-dark.min.css`}},ie={markedRenderer:n=>n,markedExtensions:[],markedOptions:{},editorExtensions:{},editorConfig:{}},si=n=>{if(n)for(const e in n){const t=n[e];t&&(ie[e]=t)}};class ai{constructor(){re(this,"pools",{})}remove(e,t,i){const l=this.pools[e]&&this.pools[e][t];l&&(this.pools[e][t]=l.filter(r=>r!==i))}clear(e){this.pools[e]={}}on(e,t){return this.pools[e]||(this.pools[e]={}),this.pools[e][t.name]||(this.pools[e][t.name]=[]),this.pools[e][t.name].push(t.callback),this.pools[e][t.name].includes(t.callback)}emit(e,t,...i){this.pools[e]||(this.pools[e]={});const l=this.pools[e][t];l&&l.forEach(r=>{try{r(...i)}catch(s){console.error(`${t} monitor event exception！`,s)}})}}const x=new ai,Oe=(n,e=0,t=e)=>new Promise((i,l)=>{n.setSelectionRange?setTimeout(()=>{n.setSelectionRange(e,t),n.focus(),i(!0)},0):(console.error("Can not reset position!"),l())}),Kt=(n,e,t)=>{const{deviationStart:i=0,deviationEnd:l=0,direct:r=!1,select:s=!1}=t;let o="";if(n.selectionStart||n.selectionStart===0){const p=n.selectionStart,d=n.selectionEnd||0,{prefixVal:u=n.value.substring(0,p),subfixVal:f=n.value.substring(d,n.value.length)}=t;o=u+e+f,Oe(n,s?p+i:p+e.length+l,p+e.length+l)}else o+=e;return r&&(n.value=o),o},ci=(n,e={newWindow:!0,nofollow:!0})=>{const t=document.createElement("a");t.href=n,t.style.display="none",e.newWindow&&(t.target="_blank"),e.nofollow&&(t.rel="noopener noreferrer"),document.body.appendChild(t),t.click(),document.body.removeChild(t)},fn=(n,e)=>{const t=Wt(()=>{n.removeEventListener("scroll",i),n.addEventListener("scroll",i),e.removeEventListener("scroll",i),e.addEventListener("scroll",i)},50),i=l=>{const r=n.clientHeight,s=e.clientHeight,o=n.scrollHeight,p=e.scrollHeight,d=(o-r)/(p-s);l.target===n?(e.removeEventListener("scroll",i),e.scrollTo({top:n.scrollTop/d}),t()):(n.removeEventListener("scroll",i),n.scrollTo({top:e.scrollTop*d}),t())};return[()=>{t().finally(()=>{n.dispatchEvent(new Event("scroll"))})},()=>{n.removeEventListener("scroll",i),e.removeEventListener("scroll",i)}]},ui=(n,e="image.png")=>{const t=n.split(","),i=t[0].match(/:(.*?);/);if(i){const l=i[1],r=atob(t[1]);let s=r.length;const o=new Uint8Array(s);for(;s--;)o[s]=r.charCodeAt(s);return new File([o],e,{type:l})}return null},gn=n=>{if(!n)return n;const e=n.split(`
`),t=['<span rn-wrapper aria-hidden="true">'];return e.forEach(()=>{t.push("<span></span>")}),t.push("</span>"),`<span class="code-block">${n}</span>${t.join("")}`},Wt=(n,e=200)=>{let t=0;return(...i)=>new Promise(l=>{t&&(clearTimeout(t),l("cancel")),t=window.setTimeout(()=>{n.apply(void 0,i),t=0,l("done")},e)})},di=(n,e=200)=>{let t=0,i=null;return(...l)=>{const r=s=>{t===0&&(t=s),s-t>=e?(n.apply(void 0,i),i=null,t=0):window.requestAnimationFrame(r)};i===null&&window.requestAnimationFrame(r),i=l}},vn=(n,e="$")=>{const t=n.split(e);let i=e,l="";for(let r=1;r<t.length;r++)if(/\\$/.test(t[r]))i+=t[r]+"$",l+=t[r]+"$";else{i+=t[r]+e,l+=t[r];break}return[i,l]},hi=n=>{var e;return navigator.userAgent.indexOf("Firefox")>-1?n.value.substring(n.selectionStart,n.selectionEnd):((e=window.getSelection())==null?void 0:e.toString())||""},pi=(n,e)=>{const t=n==null?void 0:n.getBoundingClientRect();if(e===document.documentElement)return t.top-e.clientTop;const i=e==null?void 0:e.getBoundingClientRect();return t.top-i.top},mi=(n,e)=>{const t={...n};return e.forEach(i=>{Reflect.deleteProperty(t,i)}),t},fi=()=>`${Date.now().toString(36)}${Math.random().toString(36).substring(2)}`,kn=(n,e)=>{const t=i=>{const l=n.parentElement||document.body,r=l.offsetWidth,s=l.offsetHeight,{clientWidth:o}=document.documentElement,{clientHeight:p}=document.documentElement,d=i.offsetX,u=i.offsetY,f=m=>{let b=m.x+document.body.scrollLeft-document.body.clientLeft-d,$=m.y+document.body.scrollTop-document.body.clientTop-u;b=b<1?1:b<o-r-1?b:o-r-1,$=$<1?1:$<p-s-1?$:p-s-1,e?e(b,$):(l.style.left=`${b}px`,l.style.top=`${$}px`)};document.addEventListener("mousemove",f);const g=()=>{document.removeEventListener("mousemove",f),document.removeEventListener("mouseup",g)};document.addEventListener("mouseup",g)};return n.addEventListener("mousedown",t),()=>{n.removeEventListener("mousedown",t)}},we=(n,e="")=>{const t=document.getElementById(n.id),i=n.onload;n.onload=null;const l=function(r){typeof i=="function"&&i.bind(this)(r),n.removeEventListener("load",l)};t?e!==""&&(t.addEventListener("load",l),Reflect.get(window,e)&&t.dispatchEvent(new Event("load"))):(n.addEventListener("load",l),document.head.appendChild(n))},gi=Wt((n,e,t)=>{const i=document.getElementById(n);i&&i.setAttribute(e,t)},10),Dt="onSave",tn="changeCatalogVisible",Dn="changeFullscreen",bn="pageFullscreenChanged",yn="fullscreenChanged",Zt="previewChanged",wn="htmlPreviewChanged",xn="catalogVisibleChanged",Rn="textareaFocus",vi=(n,e)=>{const{editorId:t,noPrettier:i,previewOnly:l}=n,r=ye({buildFinished:!1,html:""}),s=o=>{if(o.target===document.querySelector(`#${n.editorId}-textarea`))if(x.emit(t,"selectTextChange"),o.ctrlKey||o.metaKey)switch(o.code){case"KeyS":{o.shiftKey?x.emit(t,"replace","strikeThrough"):(x.emit(t,Dt,n.modelValue),o.preventDefault());break}case"KeyB":{x.emit(t,"replace","bold"),o.preventDefault();break}case"KeyU":{o.shiftKey?(x.emit(t,"replace","unorderedList"),o.preventDefault()):(x.emit(t,"replace","underline"),o.preventDefault());break}case"KeyI":{o.shiftKey?(x.emit(t,"openModals","image"),o.preventDefault()):(x.emit(t,"replace","italic"),o.preventDefault());break}case"Digit1":{x.emit(t,"replace","h1"),o.preventDefault();break}case"Digit2":{x.emit(t,"replace","h2"),o.preventDefault();break}case"Digit3":{x.emit(t,"replace","h3"),o.preventDefault();break}case"Digit4":{x.emit(t,"replace","h4"),o.preventDefault();break}case"Digit5":{x.emit(t,"replace","h5"),o.preventDefault();break}case"Digit6":{x.emit(t,"replace","h6"),o.preventDefault();break}case"ArrowUp":{x.emit(t,"replace","sup"),o.preventDefault();break}case"ArrowDown":{x.emit(t,"replace","sub"),o.preventDefault();break}case"KeyQ":{if(o.key==="a"){o.target.select();return}x.emit(t,"replace","quote"),o.preventDefault();break}case"KeyA":if(o.key==="q"){x.emit(t,"replace","quote"),o.preventDefault();break}else return;case"KeyO":{x.emit(t,"replace","orderedList"),o.preventDefault();break}case"KeyC":{if(o.shiftKey)x.emit(t,"replace","code"),o.preventDefault();else if(o.altKey)x.emit(t,"replace","codeRow"),o.preventDefault();else{o.preventDefault(),x.emit(t,"replace","ctrlC");break}break}case"KeyL":{x.emit(t,"openModals","link"),o.preventDefault();break}case"KeyZ":{if(o.key==="w")return;o.shiftKey?(x.emit(t,"ctrlShiftZ"),o.preventDefault()):(x.emit(t,"ctrlZ"),o.preventDefault());break}case"KeyW":if(o.key==="z"){o.shiftKey?(x.emit(t,"ctrlShiftZ"),o.preventDefault()):(x.emit(t,"ctrlZ"),o.preventDefault());break}else return;case"KeyF":{o.shiftKey&&(i||(x.emit(t,"replace","prettier"),o.preventDefault()));break}case"KeyT":{o.altKey&&o.shiftKey&&(x.emit(t,"replace","table"),o.preventDefault());break}case"KeyX":{x.emit(t,"replace","ctrlX"),o.preventDefault();break}case"KeyD":{o.preventDefault(),x.emit(t,"replace","ctrlD");break}}else o.code==="Tab"&&(o.preventDefault(),o.shiftKey?x.emit(t,"replace","shiftTab"):x.emit(t,"replace","tab"))};N(()=>n.modelValue,()=>{r.buildFinished=!1}),ne(()=>{l||(window.addEventListener("keydown",s),x.on(t,{name:"buildFinished",callback(o){r.buildFinished=!0,r.html=o}}),x.on(t,{name:Dt,callback(){const o=new Promise(p=>{if(r.buildFinished)p(r.html);else{const d=u=>{p(u),x.remove(t,"buildFinished",d)};x.on(t,{name:"buildFinished",callback:d})}});n.onSave?n.onSave(n.modelValue,o):e.emit("onSave",n.modelValue,o)}}))}),ft(()=>{l||window.removeEventListener("keydown",s)})},ki=n=>{var e;const{editorId:t,previewOnly:i}=n,l=(e=ie==null?void 0:ie.editorExtensions)==null?void 0:e.highlight;_e("editorId",t),_e("tabWidth",n.tabWidth),_e("theme",xe(()=>n.theme)),_e("highlight",xe(()=>{const s={...mn,...l==null?void 0:l.css},o=n.codeStyleReverse&&n.codeStyleReverseList.includes(n.previewTheme)?"dark":n.theme;return{js:(l==null?void 0:l.js)||li,css:s[n.codeTheme]?s[n.codeTheme][o]:mn.atom[o]}})),_e("historyLength",n.historyLength),_e("previewOnly",i),_e("showCodeRowNumber",n.showCodeRowNumber);const r=xe(()=>{var s;const o={...hn,...(s=ie==null?void 0:ie.editorConfig)==null?void 0:s.languageUserDefined};return o[n.language]?o[n.language]:hn["zh-CN"]});_e("usedLanguageText",r),_e("previewTheme",xe(()=>n.previewTheme))},bi=n=>{var e,t,i,l,r,s;const{noPrettier:o,previewOnly:p,noIconfont:d,noUploadImg:u}=n,{editorExtensions:f}=ie,g=o||!!((t=(e=ie.editorExtensions)==null?void 0:e.prettier)!=null&&t.prettierInstance),m=o||!!((l=(i=ie.editorExtensions)==null?void 0:i.prettier)!=null&&l.parserMarkdownInstance),b=u||!!((s=(r=ie.editorExtensions)==null?void 0:r.cropper)!=null&&s.instance);ne(()=>{var $,v,k,w;const S=document.createElement("script");S.src=(f==null?void 0:f.iconfont)||ii,S.id=`${h}-icon`;const T=document.createElement("script"),O=document.createElement("script");T.src=(($=f==null?void 0:f.prettier)==null?void 0:$.standaloneJs)||un.main,T.id=`${h}-prettier`,O.src=((v=f==null?void 0:f.prettier)==null?void 0:v.parserMarkdownJs)||un.markdown,O.id=`${h}-prettierMD`;const j=document.createElement("link");j.rel="stylesheet",j.href=((k=f==null?void 0:f.cropper)==null?void 0:k.css)||dn.css,j.id=`${h}-cropperCss`;const E=document.createElement("script");E.src=((w=f==null?void 0:f.cropper)==null?void 0:w.js)||dn.js,E.id=`${h}-cropper`,d||we(S),p||(b||(we(j),we(E)),g||we(T),m||we(O))})},yi=(n,e)=>{const{editorId:t,previewOnly:i}=n,l=ye({pageFullscreen:n.pageFullscreen,fullscreen:!1,preview:n.preview,htmlPreview:n.preview?!1:n.htmlPreview}),r=(p,d)=>{l[p]=d===void 0?!l[p]:d,p==="preview"&&l.preview?l.htmlPreview=!1:p==="htmlPreview"&&l.htmlPreview&&(l.preview=!1)};let s="";const o=()=>{l.pageFullscreen||l.fullscreen?document.body.style.overflow="hidden":document.body.style.overflow=s};return N(()=>[l.pageFullscreen,l.fullscreen],o),ne(()=>{i||x.on(t,{name:"uploadImage",callback(p,d){const u=f=>{x.emit(t,"replace","image",{desc:"",urls:f}),d&&d()};n.onUploadImg?n.onUploadImg(p,u):e.emit("onUploadImg",p,u)}}),s=document.body.style.overflow,o()}),[l,r]},wi=n=>{const{editorId:e}=n,t=te(!1);ne(()=>{x.on(e,{name:tn,callback:l=>{l===void 0?t.value=!t.value:t.value=l}})});const i=xe(()=>!n.toolbarsExclude.includes("catalog")&&n.toolbars.includes("catalog"));return[t,i]},xi=(n,e,t,i,l)=>{const{editorId:r}=n;N(()=>i.pageFullscreen,o=>{x.emit(r,bn,o)}),N(()=>i.fullscreen,o=>{x.emit(r,yn,o)}),N(()=>i.preview,o=>{x.emit(r,Zt,o)}),N(()=>i.htmlPreview,o=>{x.emit(r,wn,o)}),N(t,o=>{x.emit(r,xn,o)});const s={on(o,p){switch(o){case"pageFullscreen":{x.on(r,{name:bn,callback(d){p(d)}});break}case"fullscreen":{x.on(r,{name:yn,callback(d){p(d)}});break}case"preview":{x.on(r,{name:Zt,callback(d){p(d)}});break}case"htmlPreview":{x.on(r,{name:wn,callback(d){p(d)}});break}case"catalog":{x.on(r,{name:xn,callback(d){p(d)}});break}}},togglePageFullscreen(o){l("pageFullscreen",o)},toggleFullscreen(o){x.emit(r,Dn,o)},togglePreview(o){l("preview",o)},toggleHtmlPreview(o){l("htmlPreview",o)},toggleCatalog(o){x.emit(r,tn,o)},triggerSave(){x.emit(r,Dt)},insert(o){x.emit(r,"replace","universal",{generate:o})},focus(){x.emit(r,Rn)}};e.expose(s)},$i=le({setup(){return()=>c("div",{class:`${h}-divider`},null)}}),$e=({instance:n,ctx:e,props:t={}},i="default")=>{const l=(n==null?void 0:n.$slots[i])||(e==null?void 0:e.slots[i]);return(l?l(n):"")||t[i]},Si=()=>({trigger:{type:String,default:"hover"},overlay:{type:[String,Object],default:""},visible:{type:Boolean,default:!1},onChange:{type:Function,default:()=>{}},relative:{type:String,default:"html"}}),Je=le({props:Si(),setup(n,e){const t=`${h}-dropdown-hidden`,i=ye({overlayClass:[t],overlayStyle:{},triggerHover:!1,overlayHover:!1}),l=te(),r=te(),s=()=>{var f;n.trigger==="hover"&&(i.triggerHover=!0);const g=l.value,m=r.value,b=g.getBoundingClientRect(),$=g.offsetTop,v=g.offsetLeft,k=b.height,w=b.width,S=((f=document.querySelector(n.relative))==null?void 0:f.scrollLeft)||0;i.overlayStyle={...i.overlayStyle,top:$+k+"px",left:v-m.offsetWidth/2+w/2-S+"px"},n.onChange(!0)},o=()=>{i.overlayHover=!0};N(()=>n.visible,f=>{f?i.overlayClass=i.overlayClass.filter(g=>g!==t):i.overlayClass.push(t)});const p=f=>{const g=l.value,m=r.value;!g.contains(f.target)&&!m.contains(f.target)&&n.onChange(!1)};let d=-1;const u=f=>{l.value===f.target?i.triggerHover=!1:i.overlayHover=!1,clearTimeout(d),d=window.setTimeout(()=>{!i.overlayHover&&!i.triggerHover&&n.onChange(!1)},10)};return ne(()=>{n.trigger==="click"?(l.value.addEventListener("click",s),document.addEventListener("click",p)):(l.value.addEventListener("mouseenter",s),l.value.addEventListener("mouseleave",u),r.value.addEventListener("mouseenter",o),r.value.addEventListener("mouseleave",u))}),ft(()=>{n.trigger==="click"?(l.value.removeEventListener("click",s),document.removeEventListener("click",p)):(l.value.removeEventListener("mouseenter",s),l.value.removeEventListener("mouseleave",u),r.value.removeEventListener("mouseenter",o),r.value.removeEventListener("mouseleave",u))}),()=>{const f=$e({ctx:e}),g=$e({props:n,ctx:e},"overlay"),m=Yn(f instanceof Array?f[0]:f,{ref:l}),b=c("div",{class:[`${h}-dropdown`,i.overlayClass],style:i.overlayStyle,ref:r},[c("div",{class:`${h}-dropdown-overlay`},[g instanceof Array?g[0]:g])]);return[m,b]}}}),Ci=()=>({title:{type:String,default:""},visible:{type:Boolean,default:!1},width:{type:String,default:"auto"},height:{type:String,default:"auto"},onClose:{type:Function,default:()=>{}},showAdjust:{type:Boolean,default:!1},isFullscreen:{type:Boolean,default:!1},onAdjust:{type:Function,default:()=>{}},class:{type:String}}),nn=le({props:Ci(),setup(n,e){const t=te(n.visible),i=te([`${h}-modal`]),l=te(),r=te();let s=()=>{};const o=ye({initPos:{left:"0px",top:"0px"},historyPos:{left:"0px",top:"0px"}}),p=xe(()=>n.isFullscreen?{width:"100%",height:"100%"}:{width:n.width,height:n.height});return ne(()=>{s=kn(r.value,(d,u)=>{o.initPos.left=d+"px",o.initPos.top=u+"px"})}),ft(()=>{s()}),N(()=>n.isFullscreen,d=>{d?s():s=kn(r.value,(u,f)=>{o.initPos.left=u+"px",o.initPos.top=f+"px"})}),N(()=>n.visible,d=>{d?(i.value.push("zoom-in"),t.value=d,it(()=>{const u=l.value.offsetWidth/2,f=l.value.offsetHeight/2,g=document.documentElement.clientWidth/2,m=document.documentElement.clientHeight/2;o.initPos.left=g-u+"px",o.initPos.top=m-f+"px"}),setTimeout(()=>{i.value=i.value.filter(u=>u!=="zoom-in")},140)):(i.value.push("zoom-out"),setTimeout(()=>{i.value=i.value.filter(u=>u!=="zoom-out"),t.value=d},130))}),()=>{const d=$e({ctx:e}),u=$e({props:n,ctx:e},"title");return c("div",{class:[n.class],style:{display:t.value?"block":"none"}},[c("div",{class:`${h}-modal-mask`,onClick:n.onClose},null),c("div",{class:i.value,style:{...o.initPos,...p.value},ref:l},[c("div",{class:`${h}-modal-header`,ref:r},[u||""]),c("div",{class:`${h}-modal-body`},[d]),c("div",{class:`${h}-modal-func`},[n.showAdjust&&c("div",{class:`${h}-modal-adjust`,onClick:f=>{f.stopPropagation(),n.isFullscreen?o.initPos=o.historyPos:(o.historyPos=o.initPos,o.initPos={left:"0",top:"0"}),n.onAdjust(!n.isFullscreen)}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":`#md-editor-icon-${n.isFullscreen?"suoxiao":"fangda"}`},null)])]),c("div",{class:`${h}-modal-close`,onClick:f=>{f.stopPropagation(),n.onClose&&n.onClose()}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-close"},null)])])])])])}}}),Ti=()=>({type:{type:String,default:"link"},visible:{type:Boolean,default:!1},onCancel:{type:Function,default:()=>{}},onOk:{type:Function,default:()=>{}}}),Ei=le({props:Ti(),setup(n){const e=W("usedLanguageText"),t=W("editorId"),i=xe(()=>{var r,s;switch(n.type){case"link":return(r=e.value.linkModalTips)==null?void 0:r.linkTitle;case"image":return(s=e.value.linkModalTips)==null?void 0:s.imageTitle;default:return""}}),l=ye({desc:"",url:""});return N(()=>n.visible,r=>{r||setTimeout(()=>{l.desc="",l.url=""},200)}),()=>c(nn,{title:i.value,visible:n.visible,onClose:n.onCancel},{default:()=>{var r,s,o,p,d;return[c("div",{class:`${h}-form-item`},[c("label",{class:`${h}-label`,for:`link-desc-${t}`},[(r=e.value.linkModalTips)==null?void 0:r.descLabel]),c("input",{placeholder:(s=e.value.linkModalTips)==null?void 0:s.descLabelPlaceHolder,class:`${h}-input`,id:`link-desc-${t}`,type:"text",value:l.desc,onChange:u=>{l.desc=u.target.value},autocomplete:"off"},null)]),c("div",{class:`${h}-form-item`},[c("label",{class:`${h}-label`,for:`link-url-${t}`},[(o=e.value.linkModalTips)==null?void 0:o.urlLabel]),c("input",{placeholder:(p=e.value.linkModalTips)==null?void 0:p.urlLabelPlaceHolder,class:`${h}-input`,id:`link-url-${t}`,type:"text",value:l.url,onChange:u=>{l.url=u.target.value},autocomplete:"off"},null)]),c("div",{class:`${h}-form-item`},[c("button",{class:[`${h}-btn`,`${h}-btn-row`],type:"button",onClick:()=>{n.onOk(l),l.desc="",l.url=""}},[(d=e.value.linkModalTips)==null?void 0:d.buttonOK])])]}})}}),zi=()=>({visible:{type:Boolean,default:!1},onCancel:{type:Function,default:()=>{}},onOk:{type:Function,default:()=>{}}}),Ai=le({props:zi(),setup(n){var e,t;const i=W("usedLanguageText"),l=W("editorId");let r=(t=(e=ie==null?void 0:ie.editorExtensions)==null?void 0:e.cropper)==null?void 0:t.instance;const s=te(),o=te(),p=te(),d=ye({cropperInited:!1,imgSelected:!1,imgSrc:"",isFullscreen:!1});let u=null;N(()=>n.visible,()=>{n.visible&&!d.cropperInited&&(r=r||window.Cropper,s.value.onchange=()=>{if(!r){x.emit(l,"errorCatcher",{name:"Cropper",message:"Cropper is undefined"});return}const g=s.value.files||[];if(d.imgSelected=!0,(g==null?void 0:g.length)>0){const m=new FileReader;m.onload=b=>{d.imgSrc=b.target.result,it(()=>{u=new r(o.value,{viewMode:2,preview:`.${h}-clip-preview-target`})})},m.readAsDataURL(g[0])}})}),N(()=>[d.imgSelected],()=>{p.value.style=""}),N(()=>d.isFullscreen,()=>{it(()=>{u==null||u.destroy(),p.value.style="",o.value&&(u=new r(o.value,{viewMode:2,preview:`.${h}-clip-preview-target`}))})});const f=()=>{u.clear(),u.destroy(),u=null,s.value.value="",d.imgSelected=!1};return()=>{var g;return c(nn,{class:`${h}-modal-clip`,title:(g=i.value.clipModalTips)==null?void 0:g.title,visible:n.visible,onClose:n.onCancel,showAdjust:!0,isFullscreen:d.isFullscreen,onAdjust:m=>{d.isFullscreen=m},width:"668px",height:"421px"},{default:()=>{var m,b;return[c("div",{class:`${h}-form-item ${h}-clip`},[c("div",{class:`${h}-clip-main`},[d.imgSelected?c("div",{class:`${h}-clip-cropper`},[c("img",{src:d.imgSrc,ref:o,style:{display:"none"},alt:""},null),c("div",{class:`${h}-clip-delete`,onClick:f},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-delete"},null)])])]):c("div",{class:`${h}-clip-upload`,onClick:()=>{s.value.click()}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-upload"},null)])])]),c("div",{class:`${h}-clip-preview`},[c("div",{class:`${h}-clip-preview-target`,ref:p},null)])]),c("div",{class:`${h}-form-item`},[c("button",{class:`${h}-btn`,type:"button",onClick:()=>{if(u){const $=u.getCroppedCanvas();x.emit(l,"uploadImage",[ui($.toDataURL("image/png"))],n.onOk),f()}}},[((m=i.value.clipModalTips)==null?void 0:m.buttonUpload)||((b=i.value.linkModalTips)==null?void 0:b.buttonOK)])]),c("input",{ref:s,accept:"image/*",type:"file",multiple:!1,style:{display:"none"}},null)]}})}}}),_i=()=>({type:{type:String,default:"link"},linkVisible:{type:Boolean,default:!1},clipVisible:{type:Boolean,default:!1},onCancel:{type:Function,default:()=>{}},onOk:{type:Function,default:()=>{}}}),Fi=le({props:_i(),setup(n){return()=>c(Bt,null,[c(Ei,{type:n.type,visible:n.linkVisible,onOk:n.onOk,onCancel:n.onCancel},null),c(Ai,{visible:n.clipVisible,onOk:n.onOk,onCancel:n.onCancel},null)])}}),Li=n=>{var e,t,i,l;const r=W("editorId");let s=(t=(e=ie.editorExtensions)==null?void 0:e.screenfull)==null?void 0:t.instance;const o=(l=(i=ie.editorExtensions)==null?void 0:i.screenfull)==null?void 0:l.js,p=te(!1),d=g=>{if(!s){x.emit(r,"errorCatcher",{name:"fullscreen",message:"fullscreen is undefined"});return}s.isEnabled?(p.value=!0,(g===void 0?!s.isFullscreen:g)?s.request():s.exit()):console.error("browser does not support screenfull!")},u=()=>{s&&s.isEnabled&&s.on("change",()=>{(p.value||n.setting.fullscreen)&&(p.value=!1,n.updateSetting("fullscreen"))})},f=()=>{s=window.screenfull,u()};return ne(()=>{if(u(),!s){const g=document.createElement("script");g.src=o||ri,g.onload=f,g.id=`${h}-screenfull`,we(g,"screenfull")}}),ne(()=>{x.on(r,{name:Dn,callback:d})}),{fullscreenHandler:d}},Ii=()=>({tableShape:{type:Array,default:()=>[6,4]},onSelected:{type:Function,default:()=>{}}}),Oi=le({name:"TableShape",props:Ii(),setup(n){const e=ye({x:-1,y:-1});return()=>c("div",{class:`${h}-table-shape`,onMouseleave:()=>{e.x=-1,e.y=-1}},[new Array(n.tableShape[1]).fill("").map((t,i)=>c("div",{class:`${h}-table-shape-row`,key:`table-shape-row-${i}`},[new Array(n.tableShape[0]).fill("").map((l,r)=>c("div",{class:`${h}-table-shape-col`,key:`table-shape-col-${r}`,onMouseenter:()=>{e.x=i,e.y=r},onClick:()=>{n.onSelected(e)}},[c("div",{class:[`${h}-table-shape-col-default`,i<=e.x&&r<=e.y&&`${h}-table-shape-col-include`]},null)]))]))])}}),Di=()=>({noPrettier:{type:Boolean},toolbars:{type:Array,default:()=>[]},toolbarsExclude:{type:Array,default:()=>[]},setting:{type:Object,default:()=>({})},screenfull:{type:Object,default:null},screenfullJs:{type:String,default:""},updateSetting:{type:Function,default:()=>{}},tableShape:{type:Array,default:()=>[6,4]},defToolbars:{type:Object},noUploadImg:{type:Boolean}}),Ri=le({name:"MDEditorToolbar",props:Di(),setup(n){const e=W("editorId"),t=W("usedLanguageText"),{fullscreenHandler:i}=Li(n),l=`${e}-toolbar-wrapper`,r=ye({title:!1,catalog:!1,image:!1,table:!1,mermaid:!1,katex:!1}),s=(m,b)=>{x.emit(e,"replace",m,b)},o=ye({type:"link",linkVisible:!1,clipVisible:!1}),p=te();ne(()=>{x.on(e,{name:"openModals",callback(m){o.type=m,o.linkVisible=!0}})});const d=xe(()=>{const m=n.toolbars.filter(k=>!n.toolbarsExclude.includes(k)),b=m.indexOf("="),$=b===-1?m:m.slice(0,b+1),v=b===-1?[]:m.slice(b,Number.MAX_SAFE_INTEGER);return[$,v]}),u=te(),f=()=>{x.emit(e,"uploadImage",Array.from(u.value.files||[])),u.value.value=""};ne(()=>{u.value.addEventListener("change",f)});const g=m=>{var b,$,v,k,w,S,T,O,j,E,y,J,U,A,D,P,M,C,L,q,Q,Z,me,Ue,Ne,fe,gt,vt,Ve,kt,bt,Ge,Qe,yt,wt,Xe,xt,$t,rt,on,sn,an,cn;if(In.includes(m))switch(m){case"-":return c($i,null,null);case"bold":return c("div",{class:`${h}-toolbar-item`,title:(b=t.value.toolbarTips)==null?void 0:b.bold,onClick:()=>{s("bold")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-bold"},null)])]);case"underline":return c("div",{class:`${h}-toolbar-item`,title:($=t.value.toolbarTips)==null?void 0:$.underline,onClick:()=>{s("underline")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-underline"},null)])]);case"italic":return c("div",{class:`${h}-toolbar-item`,title:(v=t.value.toolbarTips)==null?void 0:v.italic,onClick:()=>{s("italic")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-italic"},null)])]);case"strikeThrough":return c("div",{class:`${h}-toolbar-item`,title:(k=t.value.toolbarTips)==null?void 0:k.strikeThrough,onClick:()=>{s("strikeThrough")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-strike-through"},null)])]);case"title":return c(Je,{relative:`#${l}`,visible:r.title,onChange:K=>{r.title=K},overlay:c("ul",{class:`${h}-menu`,onClick:()=>{r.title=!1}},[c("li",{class:`${h}-menu-item`,onClick:()=>{s("h1")}},[(w=t.value.titleItem)==null?void 0:w.h1]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("h2")}},[(S=t.value.titleItem)==null?void 0:S.h2]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("h3")}},[(T=t.value.titleItem)==null?void 0:T.h3]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("h4")}},[(O=t.value.titleItem)==null?void 0:O.h4]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("h5")}},[(j=t.value.titleItem)==null?void 0:j.h5]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("h6")}},[(E=t.value.titleItem)==null?void 0:E.h6])])},{default:()=>{var K;return[c("div",{class:`${h}-toolbar-item`,title:(K=t.value.toolbarTips)==null?void 0:K.title},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-title"},null)])])]}});case"sub":return c("div",{class:`${h}-toolbar-item`,title:(y=t.value.toolbarTips)==null?void 0:y.sub,onClick:()=>{s("sub")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-sub"},null)])]);case"sup":return c("div",{class:`${h}-toolbar-item`,title:(J=t.value.toolbarTips)==null?void 0:J.sup,onClick:()=>{s("sup")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-sup"},null)])]);case"quote":return c("div",{class:`${h}-toolbar-item`,title:(U=t.value.toolbarTips)==null?void 0:U.quote,onClick:()=>{s("quote")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-quote"},null)])]);case"unorderedList":return c("div",{class:`${h}-toolbar-item`,title:(A=t.value.toolbarTips)==null?void 0:A.unorderedList,onClick:()=>{s("unorderedList")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-unordered-list"},null)])]);case"orderedList":return c("div",{class:`${h}-toolbar-item`,title:(D=t.value.toolbarTips)==null?void 0:D.orderedList,onClick:()=>{s("orderedList")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-ordered-list"},null)])]);case"task":return c("div",{class:`${h}-toolbar-item`,title:(P=t.value.toolbarTips)==null?void 0:P.task,onClick:()=>{s("task")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-task"},null)])]);case"codeRow":return c("div",{class:`${h}-toolbar-item`,title:(M=t.value.toolbarTips)==null?void 0:M.codeRow,onClick:()=>{s("codeRow")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-code-row"},null)])]);case"code":return c("div",{class:`${h}-toolbar-item`,title:(C=t.value.toolbarTips)==null?void 0:C.code,onClick:()=>{s("code")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-code"},null)])]);case"link":return c("div",{class:`${h}-toolbar-item`,title:(L=t.value.toolbarTips)==null?void 0:L.link,onClick:()=>{o.type="link",o.linkVisible=!0}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-link"},null)])]);case"image":return n.noUploadImg?c("div",{class:`${h}-toolbar-item`,title:(q=t.value.toolbarTips)==null?void 0:q.image,onClick:()=>{o.type="image",o.linkVisible=!0}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-image"},null)])]):c(Je,{relative:`#${l}`,visible:r.image,onChange:K=>{r.image=K},overlay:c("ul",{class:`${h}-menu`,onClick:()=>{r.title=!1}},[c("li",{class:`${h}-menu-item`,onClick:()=>{o.type="image",o.linkVisible=!0}},[(Q=t.value.imgTitleItem)==null?void 0:Q.link]),c("li",{class:`${h}-menu-item`,onClick:()=>{u.value.click()}},[(Z=t.value.imgTitleItem)==null?void 0:Z.upload]),c("li",{class:`${h}-menu-item`,onClick:()=>{o.clipVisible=!0}},[(me=t.value.imgTitleItem)==null?void 0:me.clip2upload])])},{default:()=>{var K;return[c("div",{class:`${h}-toolbar-item`,title:(K=t.value.toolbarTips)==null?void 0:K.image},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-image"},null)])])]}});case"table":return c(Je,{relative:`#${l}`,visible:r.table,onChange:K=>{r.table=K},key:"bar-table",overlay:c(Oi,{tableShape:n.tableShape,onSelected:K=>{s("table",{selectedShape:K})}},null)},{default:()=>{var K;return[c("div",{class:`${h}-toolbar-item`,title:(K=t.value.toolbarTips)==null?void 0:K.table},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-table"},null)])])]}});case"revoke":return c("div",{class:`${h}-toolbar-item`,title:(Ue=t.value.toolbarTips)==null?void 0:Ue.revoke,onClick:()=>{x.emit(e,"ctrlZ")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-revoke"},null)])]);case"next":return c("div",{class:`${h}-toolbar-item`,title:(Ne=t.value.toolbarTips)==null?void 0:Ne.next,onClick:()=>{x.emit(e,"ctrlShiftZ")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-next"},null)])]);case"save":return c("div",{class:`${h}-toolbar-item`,title:(fe=t.value.toolbarTips)==null?void 0:fe.save,onClick:()=>{x.emit(e,Dt)}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-baocun"},null)])]);case"prettier":return n.noPrettier?"":c("div",{class:`${h}-toolbar-item`,title:(gt=t.value.toolbarTips)==null?void 0:gt.prettier,onClick:()=>{s("prettier")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-prettier"},null)])]);case"pageFullscreen":return!n.setting.fullscreen&&c("div",{class:`${h}-toolbar-item`,title:(vt=t.value.toolbarTips)==null?void 0:vt.pageFullscreen,onClick:()=>{n.updateSetting("pageFullscreen")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":`#md-editor-icon-${n.setting.pageFullscreen?"suoxiao":"fangda"}`},null)])]);case"fullscreen":return c("div",{class:`${h}-toolbar-item`,title:(Ve=t.value.toolbarTips)==null?void 0:Ve.fullscreen,onClick:()=>{i()}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":`#md-editor-icon-${n.setting.fullscreen?"fullscreen-exit":"fullscreen"}`},null)])]);case"preview":return c("div",{class:`${h}-toolbar-item`,title:(kt=t.value.toolbarTips)==null?void 0:kt.preview,onClick:()=>{n.updateSetting("preview")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-preview"},null)])]);case"htmlPreview":return c("div",{class:`${h}-toolbar-item`,title:(bt=t.value.toolbarTips)==null?void 0:bt.htmlPreview,onClick:()=>{n.updateSetting("htmlPreview")}},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-coding"},null)])]);case"catalog":return c("div",{class:`${h}-toolbar-item`,title:(Ge=t.value.toolbarTips)==null?void 0:Ge.catalog,onClick:()=>{x.emit(e,tn)},key:"bar-catalog"},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-catalog"},null)])]);case"github":return c("div",{class:`${h}-toolbar-item`,title:(Qe=t.value.toolbarTips)==null?void 0:Qe.github,onClick:()=>ci("https://github.com/imzbf/md-editor-v3")},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-github"},null)])]);case"mermaid":return c(Je,{relative:`#${l}`,visible:r.mermaid,onChange:K=>{r.mermaid=K},overlay:c("ul",{class:`${h}-menu`,onClick:()=>{r.mermaid=!1}},[c("li",{class:`${h}-menu-item`,onClick:()=>{s("flow")}},[(yt=t.value.mermaid)==null?void 0:yt.flow]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("sequence")}},[(wt=t.value.mermaid)==null?void 0:wt.sequence]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("gantt")}},[(Xe=t.value.mermaid)==null?void 0:Xe.gantt]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("class")}},[(xt=t.value.mermaid)==null?void 0:xt.class]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("state")}},[($t=t.value.mermaid)==null?void 0:$t.state]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("pie")}},[(rt=t.value.mermaid)==null?void 0:rt.pie]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("relationship")}},[(on=t.value.mermaid)==null?void 0:on.relationship]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("journey")}},[(sn=t.value.mermaid)==null?void 0:sn.journey])]),key:"bar-mermaid"},{default:()=>{var K;return[c("div",{class:`${h}-toolbar-item`,title:(K=t.value.toolbarTips)==null?void 0:K.mermaid},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-mermaid"},null)])])]}});case"katex":return c(Je,{relative:`#${l}`,visible:r.katex,onChange:K=>{r.katex=K},overlay:c("ul",{class:`${h}-menu`,onClick:()=>{r.katex=!1}},[c("li",{class:`${h}-menu-item`,onClick:()=>{s("katexInline")}},[(an=t.value.katex)==null?void 0:an.inline]),c("li",{class:`${h}-menu-item`,onClick:()=>{s("katexBlock")}},[(cn=t.value.katex)==null?void 0:cn.block])]),key:"bar-katex"},{default:()=>{var K;return[c("div",{class:`${h}-toolbar-item`,title:(K=t.value.toolbarTips)==null?void 0:K.katex},[c("svg",{class:`${h}-icon`,"aria-hidden":"true"},[c("use",{"xlink:href":"#md-editor-icon-formula"},null)])])]}})}else return n.defToolbars instanceof Array?n.defToolbars[m]||"":n.defToolbars&&n.defToolbars.children instanceof Array&&n.defToolbars.children[m]||""};return()=>{const m=d.value[0].map($=>g($)),b=d.value[1].map($=>g($));return c(Bt,null,[n.toolbars.length>0&&c("div",{class:`${h}-toolbar-wrapper`,id:l},[c("div",{class:`${h}-toolbar`},[c("div",{class:`${h}-toolbar-left`,ref:p},[m]),c("div",{class:`${h}-toolbar-right`},[b])])]),c("input",{ref:u,accept:"image/*",type:"file",multiple:!0,style:{display:"none"}},null),c(Fi,{linkVisible:o.linkVisible,clipVisible:o.clipVisible,type:o.type,onCancel:()=>{o.linkVisible=!1,o.clipVisible=!1},onOk:$=>{$&&s(o.type,{desc:$.desc,url:$.url}),o.linkVisible=!1,o.clipVisible=!1}},null)])}}});function jn(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let lt=jn();function ji(n){lt=n}const Mn=/[&<>"']/,Mi=new RegExp(Mn.source,"g"),Hn=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Hi=new RegExp(Hn.source,"g"),Pi={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},$n=n=>Pi[n];function ae(n,e){if(e){if(Mn.test(n))return n.replace(Mi,$n)}else if(Hn.test(n))return n.replace(Hi,$n);return n}const Bi=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Pn(n){return n.replace(Bi,(e,t)=>(t=t.toLowerCase(),t==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const Wi=/(^|[^\[])\^/g;function V(n,e){n=typeof n=="string"?n:n.source,e=e||"";const t={replace:(i,l)=>(l=l.source||l,l=l.replace(Wi,"$1"),n=n.replace(i,l),t),getRegex:()=>new RegExp(n,e)};return t}const qi=/[^\w:]/g,Ui=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function Sn(n,e,t){if(n){let i;try{i=decodeURIComponent(Pn(t)).replace(qi,"").toLowerCase()}catch{return null}if(i.indexOf("javascript:")===0||i.indexOf("vbscript:")===0||i.indexOf("data:")===0)return null}e&&!Ui.test(t)&&(t=Zi(e,t));try{t=encodeURI(t).replace(/%25/g,"%")}catch{return null}return t}const St={},Ni=/^[^:]+:\/*[^/]*$/,Vi=/^([^:]+:)[\s\S]*$/,Ki=/^([^:]+:\/*[^/]*)[\s\S]*$/;function Zi(n,e){St[" "+n]||(Ni.test(n)?St[" "+n]=n+"/":St[" "+n]=Tt(n,"/",!0)),n=St[" "+n];const t=n.indexOf(":")===-1;return e.substring(0,2)==="//"?t?e:n.replace(Vi,"$1")+e:e.charAt(0)==="/"?t?e:n.replace(Ki,"$1")+e:n+e}const Rt={exec:function(){}};function Se(n){let e=1,t,i;for(;e<arguments.length;e++){t=arguments[e];for(i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n}function Cn(n,e){const t=n.replace(/\|/g,(r,s,o)=>{let p=!1,d=s;for(;--d>=0&&o[d]==="\\";)p=!p;return p?"|":" |"}),i=t.split(/ \|/);let l=0;if(i[0].trim()||i.shift(),i.length>0&&!i[i.length-1].trim()&&i.pop(),i.length>e)i.splice(e);else for(;i.length<e;)i.push("");for(;l<i.length;l++)i[l]=i[l].trim().replace(/\\\|/g,"|");return i}function Tt(n,e,t){const i=n.length;if(i===0)return"";let l=0;for(;l<i;){const r=n.charAt(i-l-1);if(r===e&&!t)l++;else if(r!==e&&t)l++;else break}return n.slice(0,i-l)}function Gi(n,e){if(n.indexOf(e[1])===-1)return-1;const t=n.length;let i=0,l=0;for(;l<t;l++)if(n[l]==="\\")l++;else if(n[l]===e[0])i++;else if(n[l]===e[1]&&(i--,i<0))return l;return-1}function Bn(n){n&&n.sanitize&&!n.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function Tn(n,e){if(e<1)return"";let t="";for(;e>1;)e&1&&(t+=n),e>>=1,n+=n;return t+n}function En(n,e,t,i){const l=e.href,r=e.title?ae(e.title):null,s=n[1].replace(/\\([\[\]])/g,"$1");if(n[0].charAt(0)!=="!"){i.state.inLink=!0;const o={type:"link",raw:t,href:l,title:r,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,o}return{type:"image",raw:t,href:l,title:r,text:ae(s)}}function Qi(n,e){const t=n.match(/^(\s+)(?:```)/);if(t===null)return e;const i=t[1];return e.split(`
`).map(l=>{const r=l.match(/^\s+/);if(r===null)return l;const[s]=r;return s.length>=i.length?l.slice(i.length):l}).join(`
`)}class ln{constructor(e){this.options=e||lt}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const i=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?i:Tt(i,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const i=t[0],l=Qi(i,t[3]||"");return{type:"code",raw:i,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:l}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let i=t[2].trim();if(/#$/.test(i)){const l=Tt(i,"#");(this.options.pedantic||!l||/ $/.test(l))&&(i=l.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:i,tokens:this.lexer.inline(i)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const i=t[0].replace(/^ *>[ \t]?/gm,""),l=this.lexer.state.top;this.lexer.state.top=!0;const r=this.lexer.blockTokens(i);return this.lexer.state.top=l,{type:"blockquote",raw:t[0],tokens:r,text:i}}}list(e){let t=this.rules.block.list.exec(e);if(t){let i,l,r,s,o,p,d,u,f,g,m,b,$=t[1].trim();const v=$.length>1,k={type:"list",raw:"",ordered:v,start:v?+$.slice(0,-1):"",loose:!1,items:[]};$=v?`\\d{1,9}\\${$.slice(-1)}`:`\\${$}`,this.options.pedantic&&($=v?$:"[*+-]");const w=new RegExp(`^( {0,3}${$})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;e&&(b=!1,!(!(t=w.exec(e))||this.rules.block.hr.test(e)));){if(i=t[0],e=e.substring(i.length),u=t[2].split(`
`,1)[0].replace(/^\t+/,T=>" ".repeat(3*T.length)),f=e.split(`
`,1)[0],this.options.pedantic?(s=2,m=u.trimLeft()):(s=t[2].search(/[^ ]/),s=s>4?1:s,m=u.slice(s),s+=t[1].length),p=!1,!u&&/^ *$/.test(f)&&(i+=f+`
`,e=e.substring(f.length+1),b=!0),!b){const T=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),O=new RegExp(`^ {0,${Math.min(3,s-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),j=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:\`\`\`|~~~)`),E=new RegExp(`^ {0,${Math.min(3,s-1)}}#`);for(;e&&(g=e.split(`
`,1)[0],f=g,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(j.test(f)||E.test(f)||T.test(f)||O.test(e)));){if(f.search(/[^ ]/)>=s||!f.trim())m+=`
`+f.slice(s);else{if(p||u.search(/[^ ]/)>=4||j.test(u)||E.test(u)||O.test(u))break;m+=`
`+f}!p&&!f.trim()&&(p=!0),i+=g+`
`,e=e.substring(g.length+1),u=f.slice(s)}}k.loose||(d?k.loose=!0:/\n *\n *$/.test(i)&&(d=!0)),this.options.gfm&&(l=/^\[[ xX]\] /.exec(m),l&&(r=l[0]!=="[ ] ",m=m.replace(/^\[[ xX]\] +/,""))),k.items.push({type:"list_item",raw:i,task:!!l,checked:r,loose:!1,text:m}),k.raw+=i}k.items[k.items.length-1].raw=i.trimRight(),k.items[k.items.length-1].text=m.trimRight(),k.raw=k.raw.trimRight();const S=k.items.length;for(o=0;o<S;o++)if(this.lexer.state.top=!1,k.items[o].tokens=this.lexer.blockTokens(k.items[o].text,[]),!k.loose){const T=k.items[o].tokens.filter(j=>j.type==="space"),O=T.length>0&&T.some(j=>/\n.*\n/.test(j.raw));k.loose=O}if(k.loose)for(o=0;o<S;o++)k.items[o].loose=!0;return k}}html(e){const t=this.rules.block.html.exec(e);if(t){const i={type:"html",raw:t[0],pre:!this.options.sanitizer&&(t[1]==="pre"||t[1]==="script"||t[1]==="style"),text:t[0]};if(this.options.sanitize){const l=this.options.sanitizer?this.options.sanitizer(t[0]):ae(t[0]);i.type="paragraph",i.text=l,i.tokens=this.lexer.inline(l)}return i}}def(e){const t=this.rules.block.def.exec(e);if(t){const i=t[1].toLowerCase().replace(/\s+/g," "),l=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:i,raw:t[0],href:l,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(t){const i={type:"table",header:Cn(t[1]).map(l=>({text:l})),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(i.header.length===i.align.length){i.raw=t[0];let l=i.align.length,r,s,o,p;for(r=0;r<l;r++)/^ *-+: *$/.test(i.align[r])?i.align[r]="right":/^ *:-+: *$/.test(i.align[r])?i.align[r]="center":/^ *:-+ *$/.test(i.align[r])?i.align[r]="left":i.align[r]=null;for(l=i.rows.length,r=0;r<l;r++)i.rows[r]=Cn(i.rows[r],i.header.length).map(d=>({text:d}));for(l=i.header.length,s=0;s<l;s++)i.header[s].tokens=this.lexer.inline(i.header[s].text);for(l=i.rows.length,s=0;s<l;s++)for(p=i.rows[s],o=0;o<p.length;o++)p[o].tokens=this.lexer.inline(p[o].text);return i}}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const i=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:i,tokens:this.lexer.inline(i)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:ae(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):ae(t[0]):t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const i=t[2].trim();if(!this.options.pedantic&&/^</.test(i)){if(!/>$/.test(i))return;const s=Tt(i.slice(0,-1),"\\");if((i.length-s.length)%2===0)return}else{const s=Gi(t[2],"()");if(s>-1){const o=(t[0].indexOf("!")===0?5:4)+t[1].length+s;t[2]=t[2].substring(0,s),t[0]=t[0].substring(0,o).trim(),t[3]=""}}let l=t[2],r="";if(this.options.pedantic){const s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(l);s&&(l=s[1],r=s[3])}else r=t[3]?t[3].slice(1,-1):"";return l=l.trim(),/^</.test(l)&&(this.options.pedantic&&!/>$/.test(i)?l=l.slice(1):l=l.slice(1,-1)),En(t,{href:l&&l.replace(this.rules.inline._escapes,"$1"),title:r&&r.replace(this.rules.inline._escapes,"$1")},t[0],this.lexer)}}reflink(e,t){let i;if((i=this.rules.inline.reflink.exec(e))||(i=this.rules.inline.nolink.exec(e))){let l=(i[2]||i[1]).replace(/\s+/g," ");if(l=t[l.toLowerCase()],!l){const r=i[0].charAt(0);return{type:"text",raw:r,text:r}}return En(i,l,i[0],this.lexer)}}emStrong(e,t,i=""){let l=this.rules.inline.emStrong.lDelim.exec(e);if(!l||l[3]&&i.match(/[\p{L}\p{N}]/u))return;const r=l[1]||l[2]||"";if(!r||r&&(i===""||this.rules.inline.punctuation.exec(i))){const s=l[0].length-1;let o,p,d=s,u=0;const f=l[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(f.lastIndex=0,t=t.slice(-1*e.length+s);(l=f.exec(t))!=null;){if(o=l[1]||l[2]||l[3]||l[4]||l[5]||l[6],!o)continue;if(p=o.length,l[3]||l[4]){d+=p;continue}else if((l[5]||l[6])&&s%3&&!((s+p)%3)){u+=p;continue}if(d-=p,d>0)continue;p=Math.min(p,p+d+u);const g=e.slice(0,s+l.index+(l[0].length-o.length)+p);if(Math.min(s,p)%2){const b=g.slice(1,-1);return{type:"em",raw:g,text:b,tokens:this.lexer.inlineTokens(b)}}const m=g.slice(2,-2);return{type:"strong",raw:g,text:m,tokens:this.lexer.inlineTokens(m)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let i=t[2].replace(/\n/g," ");const l=/[^ ]/.test(i),r=/^ /.test(i)&&/ $/.test(i);return l&&r&&(i=i.substring(1,i.length-1)),i=ae(i,!0),{type:"codespan",raw:t[0],text:i}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e,t){const i=this.rules.inline.autolink.exec(e);if(i){let l,r;return i[2]==="@"?(l=ae(this.options.mangle?t(i[1]):i[1]),r="mailto:"+l):(l=ae(i[1]),r=l),{type:"link",raw:i[0],text:l,href:r,tokens:[{type:"text",raw:l,text:l}]}}}url(e,t){let i;if(i=this.rules.inline.url.exec(e)){let l,r;if(i[2]==="@")l=ae(this.options.mangle?t(i[0]):i[0]),r="mailto:"+l;else{let s;do s=i[0],i[0]=this.rules.inline._backpedal.exec(i[0])[0];while(s!==i[0]);l=ae(i[0]),i[1]==="www."?r="http://"+i[0]:r=i[0]}return{type:"link",raw:i[0],text:l,href:r,tokens:[{type:"text",raw:l,text:l}]}}}inlineText(e,t){const i=this.rules.inline.text.exec(e);if(i){let l;return this.lexer.state.inRawBlock?l=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(i[0]):ae(i[0]):i[0]:l=ae(this.options.smartypants?t(i[0]):i[0]),{type:"text",raw:i[0],text:l}}}}const I={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:Rt,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};I._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;I._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;I.def=V(I.def).replace("label",I._label).replace("title",I._title).getRegex();I.bullet=/(?:[*+-]|\d{1,9}[.)])/;I.listItemStart=V(/^( *)(bull) */).replace("bull",I.bullet).getRegex();I.list=V(I.list).replace(/bull/g,I.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+I.def.source+")").getRegex();I._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";I._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;I.html=V(I.html,"i").replace("comment",I._comment).replace("tag",I._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();I.paragraph=V(I._paragraph).replace("hr",I.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",I._tag).getRegex();I.blockquote=V(I.blockquote).replace("paragraph",I.paragraph).getRegex();I.normal=Se({},I);I.gfm=Se({},I.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"});I.gfm.table=V(I.gfm.table).replace("hr",I.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",I._tag).getRegex();I.gfm.paragraph=V(I._paragraph).replace("hr",I.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",I.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",I._tag).getRegex();I.pedantic=Se({},I.normal,{html:V(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",I._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Rt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:V(I.normal._paragraph).replace("hr",I.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",I.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});const z={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:Rt,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:Rt,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};z._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";z.punctuation=V(z.punctuation).replace(/punctuation/g,z._punctuation).getRegex();z.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;z.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;z._comment=V(I._comment).replace("(?:-->|$)","-->").getRegex();z.emStrong.lDelim=V(z.emStrong.lDelim).replace(/punct/g,z._punctuation).getRegex();z.emStrong.rDelimAst=V(z.emStrong.rDelimAst,"g").replace(/punct/g,z._punctuation).getRegex();z.emStrong.rDelimUnd=V(z.emStrong.rDelimUnd,"g").replace(/punct/g,z._punctuation).getRegex();z._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;z._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;z._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;z.autolink=V(z.autolink).replace("scheme",z._scheme).replace("email",z._email).getRegex();z._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;z.tag=V(z.tag).replace("comment",z._comment).replace("attribute",z._attribute).getRegex();z._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;z._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;z._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;z.link=V(z.link).replace("label",z._label).replace("href",z._href).replace("title",z._title).getRegex();z.reflink=V(z.reflink).replace("label",z._label).replace("ref",I._label).getRegex();z.nolink=V(z.nolink).replace("ref",I._label).getRegex();z.reflinkSearch=V(z.reflinkSearch,"g").replace("reflink",z.reflink).replace("nolink",z.nolink).getRegex();z.normal=Se({},z);z.pedantic=Se({},z.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:V(/^!?\[(label)\]\((.*?)\)/).replace("label",z._label).getRegex(),reflink:V(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",z._label).getRegex()});z.gfm=Se({},z.normal,{escape:V(z.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/});z.gfm.url=V(z.gfm.url,"i").replace("email",z.gfm._extended_email).getRegex();z.breaks=Se({},z.gfm,{br:V(z.br).replace("{2,}","*").getRegex(),text:V(z.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});function Xi(n){return n.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function zn(n){let e="",t,i;const l=n.length;for(t=0;t<l;t++)i=n.charCodeAt(t),Math.random()>.5&&(i="x"+i.toString(16)),e+="&#"+i+";";return e}class Re{constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||lt,this.options.tokenizer=this.options.tokenizer||new ln,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:I.normal,inline:z.normal};this.options.pedantic?(t.block=I.pedantic,t.inline=z.pedantic):this.options.gfm&&(t.block=I.gfm,this.options.breaks?t.inline=z.breaks:t.inline=z.gfm),this.tokenizer.rules=t}static get rules(){return{block:I,inline:z}}static lex(e,t){return new Re(t).lex(e)}static lexInline(e,t){return new Re(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);let t;for(;t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(e,t=[]){this.options.pedantic?e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e=e.replace(/^( *)(\t+)/gm,(o,p,d)=>p+"    ".repeat(d.length));let i,l,r,s;for(;e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(o=>(i=o.call({lexer:this},e,t))?(e=e.substring(i.raw.length),t.push(i),!0):!1))){if(i=this.tokenizer.space(e)){e=e.substring(i.raw.length),i.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(i);continue}if(i=this.tokenizer.code(e)){e=e.substring(i.raw.length),l=t[t.length-1],l&&(l.type==="paragraph"||l.type==="text")?(l.raw+=`
`+i.raw,l.text+=`
`+i.text,this.inlineQueue[this.inlineQueue.length-1].src=l.text):t.push(i);continue}if(i=this.tokenizer.fences(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.heading(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.hr(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.blockquote(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.list(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.html(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.def(e)){e=e.substring(i.raw.length),l=t[t.length-1],l&&(l.type==="paragraph"||l.type==="text")?(l.raw+=`
`+i.raw,l.text+=`
`+i.raw,this.inlineQueue[this.inlineQueue.length-1].src=l.text):this.tokens.links[i.tag]||(this.tokens.links[i.tag]={href:i.href,title:i.title});continue}if(i=this.tokenizer.table(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.lheading(e)){e=e.substring(i.raw.length),t.push(i);continue}if(r=e,this.options.extensions&&this.options.extensions.startBlock){let o=1/0;const p=e.slice(1);let d;this.options.extensions.startBlock.forEach(function(u){d=u.call({lexer:this},p),typeof d=="number"&&d>=0&&(o=Math.min(o,d))}),o<1/0&&o>=0&&(r=e.substring(0,o+1))}if(this.state.top&&(i=this.tokenizer.paragraph(r))){l=t[t.length-1],s&&l.type==="paragraph"?(l.raw+=`
`+i.raw,l.text+=`
`+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=l.text):t.push(i),s=r.length!==e.length,e=e.substring(i.raw.length);continue}if(i=this.tokenizer.text(e)){e=e.substring(i.raw.length),l=t[t.length-1],l&&l.type==="text"?(l.raw+=`
`+i.raw,l.text+=`
`+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=l.text):t.push(i);continue}if(e){const o="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(o);break}else throw new Error(o)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let i,l,r,s=e,o,p,d;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(s))!=null;)u.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,o.index)+"["+Tn("a",o[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(s))!=null;)s=s.slice(0,o.index)+"["+Tn("a",o[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.escapedEmSt.exec(s))!=null;)s=s.slice(0,o.index+o[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;e;)if(p||(d=""),p=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(u=>(i=u.call({lexer:this},e,t))?(e=e.substring(i.raw.length),t.push(i),!0):!1))){if(i=this.tokenizer.escape(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.tag(e)){e=e.substring(i.raw.length),l=t[t.length-1],l&&i.type==="text"&&l.type==="text"?(l.raw+=i.raw,l.text+=i.text):t.push(i);continue}if(i=this.tokenizer.link(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(i.raw.length),l=t[t.length-1],l&&i.type==="text"&&l.type==="text"?(l.raw+=i.raw,l.text+=i.text):t.push(i);continue}if(i=this.tokenizer.emStrong(e,s,d)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.codespan(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.br(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.del(e)){e=e.substring(i.raw.length),t.push(i);continue}if(i=this.tokenizer.autolink(e,zn)){e=e.substring(i.raw.length),t.push(i);continue}if(!this.state.inLink&&(i=this.tokenizer.url(e,zn))){e=e.substring(i.raw.length),t.push(i);continue}if(r=e,this.options.extensions&&this.options.extensions.startInline){let u=1/0;const f=e.slice(1);let g;this.options.extensions.startInline.forEach(function(m){g=m.call({lexer:this},f),typeof g=="number"&&g>=0&&(u=Math.min(u,g))}),u<1/0&&u>=0&&(r=e.substring(0,u+1))}if(i=this.tokenizer.inlineText(r,Xi)){e=e.substring(i.raw.length),i.raw.slice(-1)!=="_"&&(d=i.raw.slice(-1)),p=!0,l=t[t.length-1],l&&l.type==="text"?(l.raw+=i.raw,l.text+=i.text):t.push(i);continue}if(e){const u="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return t}}class rn{constructor(e){this.options=e||lt}code(e,t,i){const l=(t||"").match(/\S*/)[0];if(this.options.highlight){const r=this.options.highlight(e,l);r!=null&&r!==e&&(i=!0,e=r)}return e=e.replace(/\n$/,"")+`
`,l?'<pre><code class="'+this.options.langPrefix+ae(l)+'">'+(i?e:ae(e,!0))+`</code></pre>
`:"<pre><code>"+(i?e:ae(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e){return e}heading(e,t,i,l){if(this.options.headerIds){const r=this.options.headerPrefix+l.slug(i);return`<h${t} id="${r}">${e}</h${t}>
`}return`<h${t}>${e}</h${t}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(e,t,i){const l=t?"ol":"ul",r=t&&i!==1?' start="'+i+'"':"";return"<"+l+r+`>
`+e+"</"+l+`>
`}listitem(e){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const i=t.header?"th":"td";return(t.align?`<${i} align="${t.align}">`:`<${i}>`)+e+`</${i}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return`<del>${e}</del>`}link(e,t,i){if(e=Sn(this.options.sanitize,this.options.baseUrl,e),e===null)return i;let l='<a href="'+e+'"';return t&&(l+=' title="'+t+'"'),l+=">"+i+"</a>",l}image(e,t,i){if(e=Sn(this.options.sanitize,this.options.baseUrl,e),e===null)return i;let l=`<img src="${e}" alt="${i}"`;return t&&(l+=` title="${t}"`),l+=this.options.xhtml?"/>":">",l}text(e){return e}}class Wn{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,i){return""+i}image(e,t,i){return""+i}br(){return""}}class qn{constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,t){let i=e,l=0;if(this.seen.hasOwnProperty(i)){l=this.seen[e];do l++,i=e+"-"+l;while(this.seen.hasOwnProperty(i))}return t||(this.seen[e]=l,this.seen[i]=0),i}slug(e,t={}){const i=this.serialize(e);return this.getNextSafeSlug(i,t.dryrun)}}class Ae{constructor(e){this.options=e||lt,this.options.renderer=this.options.renderer||new rn,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Wn,this.slugger=new qn}static parse(e,t){return new Ae(t).parse(e)}static parseInline(e,t){return new Ae(t).parseInline(e)}parse(e,t=!0){let i="",l,r,s,o,p,d,u,f,g,m,b,$,v,k,w,S,T,O,j;const E=e.length;for(l=0;l<E;l++){if(m=e[l],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[m.type]&&(j=this.options.extensions.renderers[m.type].call({parser:this},m),j!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(m.type))){i+=j||"";continue}switch(m.type){case"space":continue;case"hr":{i+=this.renderer.hr();continue}case"heading":{i+=this.renderer.heading(this.parseInline(m.tokens),m.depth,Pn(this.parseInline(m.tokens,this.textRenderer)),this.slugger);continue}case"code":{i+=this.renderer.code(m.text,m.lang,m.escaped);continue}case"table":{for(f="",u="",o=m.header.length,r=0;r<o;r++)u+=this.renderer.tablecell(this.parseInline(m.header[r].tokens),{header:!0,align:m.align[r]});for(f+=this.renderer.tablerow(u),g="",o=m.rows.length,r=0;r<o;r++){for(d=m.rows[r],u="",p=d.length,s=0;s<p;s++)u+=this.renderer.tablecell(this.parseInline(d[s].tokens),{header:!1,align:m.align[s]});g+=this.renderer.tablerow(u)}i+=this.renderer.table(f,g);continue}case"blockquote":{g=this.parse(m.tokens),i+=this.renderer.blockquote(g);continue}case"list":{for(b=m.ordered,$=m.start,v=m.loose,o=m.items.length,g="",r=0;r<o;r++)w=m.items[r],S=w.checked,T=w.task,k="",w.task&&(O=this.renderer.checkbox(S),v?w.tokens.length>0&&w.tokens[0].type==="paragraph"?(w.tokens[0].text=O+" "+w.tokens[0].text,w.tokens[0].tokens&&w.tokens[0].tokens.length>0&&w.tokens[0].tokens[0].type==="text"&&(w.tokens[0].tokens[0].text=O+" "+w.tokens[0].tokens[0].text)):w.tokens.unshift({type:"text",text:O}):k+=O),k+=this.parse(w.tokens,v),g+=this.renderer.listitem(k,T,S);i+=this.renderer.list(g,b,$);continue}case"html":{i+=this.renderer.html(m.text);continue}case"paragraph":{i+=this.renderer.paragraph(this.parseInline(m.tokens));continue}case"text":{for(g=m.tokens?this.parseInline(m.tokens):m.text;l+1<E&&e[l+1].type==="text";)m=e[++l],g+=`
`+(m.tokens?this.parseInline(m.tokens):m.text);i+=t?this.renderer.paragraph(g):g;continue}default:{const y='Token with "'+m.type+'" type was not found.';if(this.options.silent){console.error(y);return}else throw new Error(y)}}}return i}parseInline(e,t){t=t||this.renderer;let i="",l,r,s;const o=e.length;for(l=0;l<o;l++){if(r=e[l],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]&&(s=this.options.extensions.renderers[r.type].call({parser:this},r),s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type))){i+=s||"";continue}switch(r.type){case"escape":{i+=t.text(r.text);break}case"html":{i+=t.html(r.text);break}case"link":{i+=t.link(r.href,r.title,this.parseInline(r.tokens,t));break}case"image":{i+=t.image(r.href,r.title,r.text);break}case"strong":{i+=t.strong(this.parseInline(r.tokens,t));break}case"em":{i+=t.em(this.parseInline(r.tokens,t));break}case"codespan":{i+=t.codespan(r.text);break}case"br":{i+=t.br();break}case"del":{i+=t.del(this.parseInline(r.tokens,t));break}case"text":{i+=t.text(r.text);break}default:{const p='Token with "'+r.type+'" type was not found.';if(this.options.silent){console.error(p);return}else throw new Error(p)}}}return i}}function _(n,e,t){if(typeof n>"u"||n===null)throw new Error("marked(): input parameter is undefined or null");if(typeof n!="string")throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected");if(typeof e=="function"&&(t=e,e=null),e=Se({},_.defaults,e||{}),Bn(e),t){const l=e.highlight;let r;try{r=Re.lex(n,e)}catch(p){return t(p)}const s=function(p){let d;if(!p)try{e.walkTokens&&_.walkTokens(r,e.walkTokens),d=Ae.parse(r,e)}catch(u){p=u}return e.highlight=l,p?t(p):t(null,d)};if(!l||l.length<3||(delete e.highlight,!r.length))return s();let o=0;_.walkTokens(r,function(p){p.type==="code"&&(o++,setTimeout(()=>{l(p.text,p.lang,function(d,u){if(d)return s(d);u!=null&&u!==p.text&&(p.text=u,p.escaped=!0),o--,o===0&&s()})},0))}),o===0&&s();return}function i(l){if(l.message+=`
Please report this to https://github.com/markedjs/marked.`,e.silent)return"<p>An error occurred:</p><pre>"+ae(l.message+"",!0)+"</pre>";throw l}try{const l=Re.lex(n,e);if(e.walkTokens){if(e.async)return Promise.all(_.walkTokens(l,e.walkTokens)).then(()=>Ae.parse(l,e)).catch(i);_.walkTokens(l,e.walkTokens)}return Ae.parse(l,e)}catch(l){i(l)}}_.options=_.setOptions=function(n){return Se(_.defaults,n),ji(_.defaults),_};_.getDefaults=jn;_.defaults=lt;_.use=function(...n){const e=_.defaults.extensions||{renderers:{},childTokens:{}};n.forEach(t=>{const i=Se({},t);if(i.async=_.defaults.async||i.async,t.extensions&&(t.extensions.forEach(l=>{if(!l.name)throw new Error("extension name required");if(l.renderer){const r=e.renderers[l.name];r?e.renderers[l.name]=function(...s){let o=l.renderer.apply(this,s);return o===!1&&(o=r.apply(this,s)),o}:e.renderers[l.name]=l.renderer}if(l.tokenizer){if(!l.level||l.level!=="block"&&l.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");e[l.level]?e[l.level].unshift(l.tokenizer):e[l.level]=[l.tokenizer],l.start&&(l.level==="block"?e.startBlock?e.startBlock.push(l.start):e.startBlock=[l.start]:l.level==="inline"&&(e.startInline?e.startInline.push(l.start):e.startInline=[l.start]))}l.childTokens&&(e.childTokens[l.name]=l.childTokens)}),i.extensions=e),t.renderer){const l=_.defaults.renderer||new rn;for(const r in t.renderer){const s=l[r];l[r]=(...o)=>{let p=t.renderer[r].apply(l,o);return p===!1&&(p=s.apply(l,o)),p}}i.renderer=l}if(t.tokenizer){const l=_.defaults.tokenizer||new ln;for(const r in t.tokenizer){const s=l[r];l[r]=(...o)=>{let p=t.tokenizer[r].apply(l,o);return p===!1&&(p=s.apply(l,o)),p}}i.tokenizer=l}if(t.walkTokens){const l=_.defaults.walkTokens;i.walkTokens=function(r){let s=[];return s.push(t.walkTokens.call(this,r)),l&&(s=s.concat(l.call(this,r))),s}}_.setOptions(i)})};_.walkTokens=function(n,e){let t=[];for(const i of n)switch(t=t.concat(e.call(_,i)),i.type){case"table":{for(const l of i.header)t=t.concat(_.walkTokens(l.tokens,e));for(const l of i.rows)for(const r of l)t=t.concat(_.walkTokens(r.tokens,e));break}case"list":{t=t.concat(_.walkTokens(i.items,e));break}default:_.defaults.extensions&&_.defaults.extensions.childTokens&&_.defaults.extensions.childTokens[i.type]?_.defaults.extensions.childTokens[i.type].forEach(function(l){t=t.concat(_.walkTokens(i[l],e))}):i.tokens&&(t=t.concat(_.walkTokens(i.tokens,e)))}return t};_.parseInline=function(n,e){if(typeof n>"u"||n===null)throw new Error("marked.parseInline(): input parameter is undefined or null");if(typeof n!="string")throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected");e=Se({},_.defaults,e||{}),Bn(e);try{const t=Re.lexInline(n,e);return e.walkTokens&&_.walkTokens(t,e.walkTokens),Ae.parseInline(t,e)}catch(t){if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,e.silent)return"<p>An error occurred:</p><pre>"+ae(t.message+"",!0)+"</pre>";throw t}};_.Parser=Ae;_.parser=Ae.parse;_.Renderer=rn;_.TextRenderer=Wn;_.Lexer=Re;_.lexer=Re.lex;_.Tokenizer=ln;_.Slugger=qn;_.parse=_;_.options;_.setOptions;_.use;_.walkTokens;_.parseInline;Ae.parse;Re.lex;var Yi=function(){var n=document.getSelection();if(!n.rangeCount)return function(){};for(var e=document.activeElement,t=[],i=0;i<n.rangeCount;i++)t.push(n.getRangeAt(i));switch(e.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":e.blur();break;default:e=null;break}return n.removeAllRanges(),function(){n.type==="Caret"&&n.removeAllRanges(),n.rangeCount||t.forEach(function(l){n.addRange(l)}),e&&e.focus()}},Ji=Yi,An={"text/plain":"Text","text/html":"Url",default:"Text"},el="Copy to clipboard: #{key}, Enter";function tl(n){var e=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return n.replace(/#{\s*key\s*}/g,e)}function nl(n,e){var t,i,l,r,s,o,p=!1;e||(e={}),t=e.debug||!1;try{l=Ji(),r=document.createRange(),s=document.getSelection(),o=document.createElement("span"),o.textContent=n,o.ariaHidden="true",o.style.all="unset",o.style.position="fixed",o.style.top=0,o.style.clip="rect(0, 0, 0, 0)",o.style.whiteSpace="pre",o.style.webkitUserSelect="text",o.style.MozUserSelect="text",o.style.msUserSelect="text",o.style.userSelect="text",o.addEventListener("copy",function(u){if(u.stopPropagation(),e.format)if(u.preventDefault(),typeof u.clipboardData>"u"){t&&console.warn("unable to use e.clipboardData"),t&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var f=An[e.format]||An.default;window.clipboardData.setData(f,n)}else u.clipboardData.clearData(),u.clipboardData.setData(e.format,n);e.onCopy&&(u.preventDefault(),e.onCopy(u.clipboardData))}),document.body.appendChild(o),r.selectNodeContents(o),s.addRange(r);var d=document.execCommand("copy");if(!d)throw new Error("copy command was unsuccessful");p=!0}catch(u){t&&console.error("unable to copy using execCommand: ",u),t&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(e.format||"text",n),e.onCopy&&e.onCopy(window.clipboardData),p=!0}catch(f){t&&console.error("unable to copy using clipboardData: ",f),t&&console.error("falling back to prompt"),i=tl("message"in e?e.message:el),window.prompt(i,n)}}finally{s&&(typeof s.removeRange=="function"?s.removeRange(r):s.removeAllRanges()),o&&document.body.removeChild(o),l()}return p}var Et=nl;/*! medium-zoom 1.0.8 | MIT License | https://github.com/francoischalifour/medium-zoom */var Ke=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n},Ct=function(n){return n.tagName==="IMG"},il=function(n){return NodeList.prototype.isPrototypeOf(n)},zt=function(n){return n&&n.nodeType===1},_n=function(n){var e=n.currentSrc||n.src;return e.substr(-4).toLowerCase()===".svg"},Fn=function(n){try{return Array.isArray(n)?n.filter(Ct):il(n)?[].slice.call(n).filter(Ct):zt(n)?[n].filter(Ct):typeof n=="string"?[].slice.call(document.querySelectorAll(n)).filter(Ct):[]}catch{throw new TypeError(`The provided selector is invalid.
Expects a CSS selector, a Node element, a NodeList or an array.
See: https://github.com/francoischalifour/medium-zoom`)}},ll=function(n){var e=document.createElement("div");return e.classList.add("medium-zoom-overlay"),e.style.background=n,e},rl=function(n){var e=n.getBoundingClientRect(),t=e.top,i=e.left,l=e.width,r=e.height,s=n.cloneNode(),o=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,p=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;return s.removeAttribute("id"),s.style.position="absolute",s.style.top=t+o+"px",s.style.left=i+p+"px",s.style.width=l+"px",s.style.height=r+"px",s.style.transform="",s},Ye=function(n,e){var t=Ke({bubbles:!1,cancelable:!1,detail:void 0},e);if(typeof window.CustomEvent=="function")return new CustomEvent(n,t);var i=document.createEvent("CustomEvent");return i.initCustomEvent(n,t.bubbles,t.cancelable,t.detail),i},ol=function n(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=window.Promise||function(A){function D(){}A(D,D)},l=function(A){var D=A.target;if(D===J){b();return}S.indexOf(D)!==-1&&$({target:D})},r=function(){if(!(O||!y.original)){var A=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(j-A)>E.scrollOffset&&setTimeout(b,150)}},s=function(A){var D=A.key||A.keyCode;(D==="Escape"||D==="Esc"||D===27)&&b()},o=function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},D=A;if(A.background&&(J.style.background=A.background),A.container&&A.container instanceof Object&&(D.container=Ke({},E.container,A.container)),A.template){var P=zt(A.template)?A.template:document.querySelector(A.template);D.template=P}return E=Ke({},E,D),S.forEach(function(M){M.dispatchEvent(Ye("medium-zoom:update",{detail:{zoom:U}}))}),U},p=function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return n(Ke({},E,A))},d=function(){for(var A=arguments.length,D=Array(A),P=0;P<A;P++)D[P]=arguments[P];var M=D.reduce(function(C,L){return[].concat(C,Fn(L))},[]);return M.filter(function(C){return S.indexOf(C)===-1}).forEach(function(C){S.push(C),C.classList.add("medium-zoom-image")}),T.forEach(function(C){var L=C.type,q=C.listener,Q=C.options;M.forEach(function(Z){Z.addEventListener(L,q,Q)})}),U},u=function(){for(var A=arguments.length,D=Array(A),P=0;P<A;P++)D[P]=arguments[P];y.zoomed&&b();var M=D.length>0?D.reduce(function(C,L){return[].concat(C,Fn(L))},[]):S;return M.forEach(function(C){C.classList.remove("medium-zoom-image"),C.dispatchEvent(Ye("medium-zoom:detach",{detail:{zoom:U}}))}),S=S.filter(function(C){return M.indexOf(C)===-1}),U},f=function(A,D){var P=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return S.forEach(function(M){M.addEventListener("medium-zoom:"+A,D,P)}),T.push({type:"medium-zoom:"+A,listener:D,options:P}),U},g=function(A,D){var P=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return S.forEach(function(M){M.removeEventListener("medium-zoom:"+A,D,P)}),T=T.filter(function(M){return!(M.type==="medium-zoom:"+A&&M.listener.toString()===D.toString())}),U},m=function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},D=A.target,P=function(){var M={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},C=void 0,L=void 0;if(E.container)if(E.container instanceof Object)M=Ke({},M,E.container),C=M.width-M.left-M.right-E.margin*2,L=M.height-M.top-M.bottom-E.margin*2;else{var q=zt(E.container)?E.container:document.querySelector(E.container),Q=q.getBoundingClientRect(),Z=Q.width,me=Q.height,Ue=Q.left,Ne=Q.top;M=Ke({},M,{width:Z,height:me,left:Ue,top:Ne})}C=C||M.width-E.margin*2,L=L||M.height-E.margin*2;var fe=y.zoomedHd||y.original,gt=_n(fe)?C:fe.naturalWidth||C,vt=_n(fe)?L:fe.naturalHeight||L,Ve=fe.getBoundingClientRect(),kt=Ve.top,bt=Ve.left,Ge=Ve.width,Qe=Ve.height,yt=Math.min(Math.max(Ge,gt),C)/Ge,wt=Math.min(Math.max(Qe,vt),L)/Qe,Xe=Math.min(yt,wt),xt=(-bt+(C-Ge)/2+E.margin+M.left)/Xe,$t=(-kt+(L-Qe)/2+E.margin+M.top)/Xe,rt="scale("+Xe+") translate3d("+xt+"px, "+$t+"px, 0)";y.zoomed.style.transform=rt,y.zoomedHd&&(y.zoomedHd.style.transform=rt)};return new i(function(M){if(D&&S.indexOf(D)===-1){M(U);return}var C=function me(){O=!1,y.zoomed.removeEventListener("transitionend",me),y.original.dispatchEvent(Ye("medium-zoom:opened",{detail:{zoom:U}})),M(U)};if(y.zoomed){M(U);return}if(D)y.original=D;else if(S.length>0){var L=S;y.original=L[0]}else{M(U);return}if(y.original.dispatchEvent(Ye("medium-zoom:open",{detail:{zoom:U}})),j=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,O=!0,y.zoomed=rl(y.original),document.body.appendChild(J),E.template){var q=zt(E.template)?E.template:document.querySelector(E.template);y.template=document.createElement("div"),y.template.appendChild(q.content.cloneNode(!0)),document.body.appendChild(y.template)}if(y.original.parentElement&&y.original.parentElement.tagName==="PICTURE"&&y.original.currentSrc&&(y.zoomed.src=y.original.currentSrc),document.body.appendChild(y.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),y.original.classList.add("medium-zoom-image--hidden"),y.zoomed.classList.add("medium-zoom-image--opened"),y.zoomed.addEventListener("click",b),y.zoomed.addEventListener("transitionend",C),y.original.getAttribute("data-zoom-src")){y.zoomedHd=y.zoomed.cloneNode(),y.zoomedHd.removeAttribute("srcset"),y.zoomedHd.removeAttribute("sizes"),y.zoomedHd.removeAttribute("loading"),y.zoomedHd.src=y.zoomed.getAttribute("data-zoom-src"),y.zoomedHd.onerror=function(){clearInterval(Q),console.warn("Unable to reach the zoom image target "+y.zoomedHd.src),y.zoomedHd=null,P()};var Q=setInterval(function(){y.zoomedHd.complete&&(clearInterval(Q),y.zoomedHd.classList.add("medium-zoom-image--opened"),y.zoomedHd.addEventListener("click",b),document.body.appendChild(y.zoomedHd),P())},10)}else if(y.original.hasAttribute("srcset")){y.zoomedHd=y.zoomed.cloneNode(),y.zoomedHd.removeAttribute("sizes"),y.zoomedHd.removeAttribute("loading");var Z=y.zoomedHd.addEventListener("load",function(){y.zoomedHd.removeEventListener("load",Z),y.zoomedHd.classList.add("medium-zoom-image--opened"),y.zoomedHd.addEventListener("click",b),document.body.appendChild(y.zoomedHd),P()})}else P()})},b=function(){return new i(function(A){if(O||!y.original){A(U);return}var D=function P(){y.original.classList.remove("medium-zoom-image--hidden"),document.body.removeChild(y.zoomed),y.zoomedHd&&document.body.removeChild(y.zoomedHd),document.body.removeChild(J),y.zoomed.classList.remove("medium-zoom-image--opened"),y.template&&document.body.removeChild(y.template),O=!1,y.zoomed.removeEventListener("transitionend",P),y.original.dispatchEvent(Ye("medium-zoom:closed",{detail:{zoom:U}})),y.original=null,y.zoomed=null,y.zoomedHd=null,y.template=null,A(U)};O=!0,document.body.classList.remove("medium-zoom--opened"),y.zoomed.style.transform="",y.zoomedHd&&(y.zoomedHd.style.transform=""),y.template&&(y.template.style.transition="opacity 150ms",y.template.style.opacity=0),y.original.dispatchEvent(Ye("medium-zoom:close",{detail:{zoom:U}})),y.zoomed.addEventListener("transitionend",D)})},$=function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},D=A.target;return y.original?b():m({target:D})},v=function(){return E},k=function(){return S},w=function(){return y.original},S=[],T=[],O=!1,j=0,E=t,y={original:null,zoomed:null,zoomedHd:null,template:null};Object.prototype.toString.call(e)==="[object Object]"?E=e:(e||typeof e=="string")&&d(e),E=Ke({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},E);var J=ll(E.background);document.addEventListener("click",l),document.addEventListener("keyup",s),document.addEventListener("scroll",r),window.addEventListener("resize",b);var U={open:m,close:b,toggle:$,update:o,clone:p,attach:d,detach:u,on:f,off:g,getOptions:v,getImages:k,getZoomedImage:w};return U};function sl(n,e){e===void 0&&(e={});var t=e.insertAt;if(!(typeof document>"u")){var i=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.type="text/css",t==="top"&&i.firstChild?i.insertBefore(l,i.firstChild):i.appendChild(l),l.styleSheet?l.styleSheet.cssText=n:l.appendChild(document.createTextNode(n))}}var al=".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)!important}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}";sl(al);const cl=ol,ut=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Un=new Set,ul=(n,e,t,i)=>{typeof process=="object"&&process&&typeof process.emitWarning=="function"?process.emitWarning(n,e,t,i):console.error(`[${t}] ${e}: ${n}`)},dl=n=>!Un.has(n),We=n=>n&&n===Math.floor(n)&&n>0&&isFinite(n),Nn=n=>We(n)?n<=Math.pow(2,8)?Uint8Array:n<=Math.pow(2,16)?Uint16Array:n<=Math.pow(2,32)?Uint32Array:n<=Number.MAX_SAFE_INTEGER?At:null:null;class At extends Array{constructor(e){super(e),this.fill(0)}}var dt;const st=class{constructor(n,e){if(re(this,"heap"),re(this,"length"),!a(st,dt))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(n),this.length=0}static create(n){const e=Nn(n);if(!e)return[];R(st,dt,!0);const t=new st(n,e);return R(st,dt,!1),t}push(n){this.heap[this.length++]=n}pop(){return this.heap[--this.length]}};let Vn=st;dt=new WeakMap,B(Vn,dt,!1);var ze,ge,Ie,Ce,ht,oe,ve,se,Y,H,he,ke,de,ce,Ee,ue,He,Pe,Te,De,Ze,pe,_t,Gt,tt,Be,jt,be,Qt,Kn,nt,pt,Mt,Fe,je,Le,Me,Ft,qt,mt,Ht,at,Lt,X,ee,It,Ut,et,ct;const Zn=class{constructor(n){B(this,_t),B(this,Qt),B(this,Fe),B(this,Le),B(this,Ft),B(this,mt),B(this,at),B(this,X),B(this,It),B(this,et),B(this,ze,void 0),B(this,ge,void 0),B(this,Ie,void 0),B(this,Ce,void 0),B(this,ht,void 0),re(this,"ttl"),re(this,"ttlResolution"),re(this,"ttlAutopurge"),re(this,"updateAgeOnGet"),re(this,"updateAgeOnHas"),re(this,"allowStale"),re(this,"noDisposeOnSet"),re(this,"noUpdateTTL"),re(this,"maxEntrySize"),re(this,"sizeCalculation"),re(this,"noDeleteOnFetchRejection"),re(this,"noDeleteOnStaleGet"),re(this,"allowStaleOnFetchAbort"),re(this,"allowStaleOnFetchRejection"),re(this,"ignoreFetchAbort"),B(this,oe,void 0),B(this,ve,void 0),B(this,se,void 0),B(this,Y,void 0),B(this,H,void 0),B(this,he,void 0),B(this,ke,void 0),B(this,de,void 0),B(this,ce,void 0),B(this,Ee,void 0),B(this,ue,void 0),B(this,He,void 0),B(this,Pe,void 0),B(this,Te,void 0),B(this,De,void 0),B(this,Ze,void 0),B(this,pe,void 0),B(this,tt,()=>{}),B(this,Be,()=>{}),B(this,jt,()=>{}),B(this,be,()=>!1),B(this,nt,j=>{}),B(this,pt,(j,E,y)=>{}),B(this,Mt,(j,E,y,J)=>{if(y||J)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});const{max:e=0,ttl:t,ttlResolution:i=1,ttlAutopurge:l,updateAgeOnGet:r,updateAgeOnHas:s,allowStale:o,dispose:p,disposeAfter:d,noDisposeOnSet:u,noUpdateTTL:f,maxSize:g=0,maxEntrySize:m=0,sizeCalculation:b,fetchMethod:$,noDeleteOnFetchRejection:v,noDeleteOnStaleGet:k,allowStaleOnFetchRejection:w,allowStaleOnFetchAbort:S,ignoreFetchAbort:T}=n;if(e!==0&&!We(e))throw new TypeError("max option must be a nonnegative integer");const O=e?Nn(e):Array;if(!O)throw new Error("invalid max value: "+e);if(R(this,ze,e),R(this,ge,g),this.maxEntrySize=m||a(this,ge),this.sizeCalculation=b,this.sizeCalculation){if(!a(this,ge)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if($!==void 0&&typeof $!="function")throw new TypeError("fetchMethod must be a function if specified");if(R(this,ht,$),R(this,Ze,!!$),R(this,se,new Map),R(this,Y,new Array(e).fill(void 0)),R(this,H,new Array(e).fill(void 0)),R(this,he,new O(e)),R(this,ke,new O(e)),R(this,de,0),R(this,ce,0),R(this,Ee,Vn.create(e)),R(this,oe,0),R(this,ve,0),typeof p=="function"&&R(this,Ie,p),typeof d=="function"?(R(this,Ce,d),R(this,ue,[])):(R(this,Ce,void 0),R(this,ue,void 0)),R(this,De,!!a(this,Ie)),R(this,pe,!!a(this,Ce)),this.noDisposeOnSet=!!u,this.noUpdateTTL=!!f,this.noDeleteOnFetchRejection=!!v,this.allowStaleOnFetchRejection=!!w,this.allowStaleOnFetchAbort=!!S,this.ignoreFetchAbort=!!T,this.maxEntrySize!==0){if(a(this,ge)!==0&&!We(a(this,ge)))throw new TypeError("maxSize must be a positive integer if specified");if(!We(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");F(this,Qt,Kn).call(this)}if(this.allowStale=!!o,this.noDeleteOnStaleGet=!!k,this.updateAgeOnGet=!!r,this.updateAgeOnHas=!!s,this.ttlResolution=We(i)||i===0?i:1,this.ttlAutopurge=!!l,this.ttl=t||0,this.ttl){if(!We(this.ttl))throw new TypeError("ttl must be a positive integer if specified");F(this,_t,Gt).call(this)}if(a(this,ze)===0&&this.ttl===0&&a(this,ge)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!a(this,ze)&&!a(this,ge)){const j="LRU_CACHE_UNBOUNDED";dl(j)&&(Un.add(j),ul("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",j,Zn))}}static unsafeExposeInternals(n){return{starts:a(n,Pe),ttls:a(n,Te),sizes:a(n,He),keyMap:a(n,se),keyList:a(n,Y),valList:a(n,H),next:a(n,he),prev:a(n,ke),get head(){return a(n,de)},get tail(){return a(n,ce)},free:a(n,Ee),isBackgroundFetch:e=>{var t;return F(t=n,X,ee).call(t,e)},backgroundFetch:(e,t,i,l)=>{var r;return F(r=n,at,Lt).call(r,e,t,i,l)},moveToTail:e=>{var t;return F(t=n,et,ct).call(t,e)},indexes:e=>{var t;return F(t=n,Fe,je).call(t,e)},rindexes:e=>{var t;return F(t=n,Le,Me).call(t,e)},isStale:e=>{var t;return a(t=n,be).call(t,e)}}}get max(){return a(this,ze)}get maxSize(){return a(this,ge)}get calculatedSize(){return a(this,ve)}get size(){return a(this,oe)}get fetchMethod(){return a(this,ht)}get dispose(){return a(this,Ie)}get disposeAfter(){return a(this,Ce)}getRemainingTTL(n){return a(this,se).has(n)?1/0:0}*entries(){for(const n of F(this,Fe,je).call(this))a(this,H)[n]!==void 0&&a(this,Y)[n]!==void 0&&!F(this,X,ee).call(this,a(this,H)[n])&&(yield[a(this,Y)[n],a(this,H)[n]])}*rentries(){for(const n of F(this,Le,Me).call(this))a(this,H)[n]!==void 0&&a(this,Y)[n]!==void 0&&!F(this,X,ee).call(this,a(this,H)[n])&&(yield[a(this,Y)[n],a(this,H)[n]])}*keys(){for(const n of F(this,Fe,je).call(this)){const e=a(this,Y)[n];e!==void 0&&!F(this,X,ee).call(this,a(this,H)[n])&&(yield e)}}*rkeys(){for(const n of F(this,Le,Me).call(this)){const e=a(this,Y)[n];e!==void 0&&!F(this,X,ee).call(this,a(this,H)[n])&&(yield e)}}*values(){for(const n of F(this,Fe,je).call(this))a(this,H)[n]!==void 0&&!F(this,X,ee).call(this,a(this,H)[n])&&(yield a(this,H)[n])}*rvalues(){for(const n of F(this,Le,Me).call(this))a(this,H)[n]!==void 0&&!F(this,X,ee).call(this,a(this,H)[n])&&(yield a(this,H)[n])}[Symbol.iterator](){return this.entries()}find(n,e={}){for(const t of F(this,Fe,je).call(this)){const i=a(this,H)[t],l=F(this,X,ee).call(this,i)?i.__staleWhileFetching:i;if(l!==void 0&&n(l,a(this,Y)[t],this))return this.get(a(this,Y)[t],e)}}forEach(n,e=this){for(const t of F(this,Fe,je).call(this)){const i=a(this,H)[t],l=F(this,X,ee).call(this,i)?i.__staleWhileFetching:i;l!==void 0&&n.call(e,l,a(this,Y)[t],this)}}rforEach(n,e=this){for(const t of F(this,Le,Me).call(this)){const i=a(this,H)[t],l=F(this,X,ee).call(this,i)?i.__staleWhileFetching:i;l!==void 0&&n.call(e,l,a(this,Y)[t],this)}}purgeStale(){let n=!1;for(const e of F(this,Le,Me).call(this,{allowStale:!0}))a(this,be).call(this,e)&&(this.delete(a(this,Y)[e]),n=!0);return n}dump(){const n=[];for(const e of F(this,Fe,je).call(this,{allowStale:!0})){const t=a(this,Y)[e],i=a(this,H)[e],l=F(this,X,ee).call(this,i)?i.__staleWhileFetching:i;if(l===void 0||t===void 0)continue;const r={value:l};if(a(this,Te)&&a(this,Pe)){r.ttl=a(this,Te)[e];const s=ut.now()-a(this,Pe)[e];r.start=Math.floor(Date.now()-s)}a(this,He)&&(r.size=a(this,He)[e]),n.unshift([t,r])}return n}load(n){this.clear();for(const[e,t]of n){if(t.start){const i=Date.now()-t.start;t.start=ut.now()-i}this.set(e,t.value,t)}}set(n,e,t={}){var i,l,r;const{ttl:s=this.ttl,start:o,noDisposeOnSet:p=this.noDisposeOnSet,sizeCalculation:d=this.sizeCalculation,status:u}=t;let{noUpdateTTL:f=this.noUpdateTTL}=t;const g=a(this,Mt).call(this,n,e,t.size||0,d);if(this.maxEntrySize&&g>this.maxEntrySize)return u&&(u.set="miss",u.maxEntrySizeExceeded=!0),this.delete(n),this;let m=a(this,oe)===0?void 0:a(this,se).get(n);if(m===void 0)m=a(this,oe)===0?a(this,ce):a(this,Ee).length!==0?a(this,Ee).pop():a(this,oe)===a(this,ze)?F(this,mt,Ht).call(this,!1):a(this,oe),a(this,Y)[m]=n,a(this,H)[m]=e,a(this,se).set(n,m),a(this,he)[a(this,ce)]=m,a(this,ke)[m]=a(this,ce),R(this,ce,m),Vt(this,oe)._++,a(this,pt).call(this,m,g,u),u&&(u.set="add"),f=!1;else{F(this,et,ct).call(this,m);const b=a(this,H)[m];if(e!==b){if(a(this,Ze)&&F(this,X,ee).call(this,b)?b.__abortController.abort(new Error("replaced")):p||(a(this,De)&&((i=a(this,Ie))==null||i.call(this,b,n,"set")),a(this,pe)&&((l=a(this,ue))==null||l.push([b,n,"set"]))),a(this,nt).call(this,m),a(this,pt).call(this,m,g,u),a(this,H)[m]=e,u){u.set="replace";const $=b&&F(this,X,ee).call(this,b)?b.__staleWhileFetching:b;$!==void 0&&(u.oldValue=$)}}else u&&(u.set="update")}if(s!==0&&!a(this,Te)&&F(this,_t,Gt).call(this),a(this,Te)&&(f||a(this,jt).call(this,m,s,o),u&&a(this,Be).call(this,u,m)),!p&&a(this,pe)&&a(this,ue)){const b=a(this,ue);let $;for(;$=b==null?void 0:b.shift();)(r=a(this,Ce))==null||r.call(this,...$)}return this}pop(){var n;try{for(;a(this,oe);){const e=a(this,H)[a(this,de)];if(F(this,mt,Ht).call(this,!0),F(this,X,ee).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(a(this,pe)&&a(this,ue)){const e=a(this,ue);let t;for(;t=e==null?void 0:e.shift();)(n=a(this,Ce))==null||n.call(this,...t)}}}has(n,e={}){const{updateAgeOnHas:t=this.updateAgeOnHas,status:i}=e,l=a(this,se).get(n);if(l!==void 0)if(a(this,be).call(this,l))i&&(i.has="stale",a(this,Be).call(this,i,l));else return t&&a(this,tt).call(this,l),i&&(i.has="hit",a(this,Be).call(this,i,l)),!0;else i&&(i.has="miss");return!1}peek(n,e={}){const{allowStale:t=this.allowStale}=e,i=a(this,se).get(n);if(i!==void 0&&(t||!a(this,be).call(this,i))){const l=a(this,H)[i];return F(this,X,ee).call(this,l)?l.__staleWhileFetching:l}}async fetch(n,e={}){const{allowStale:t=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:l=this.noDeleteOnStaleGet,ttl:r=this.ttl,noDisposeOnSet:s=this.noDisposeOnSet,size:o=0,sizeCalculation:p=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:u=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:m=this.allowStaleOnFetchAbort,context:b,forceRefresh:$=!1,status:v,signal:k}=e;if(!a(this,Ze))return v&&(v.fetch="get"),this.get(n,{allowStale:t,updateAgeOnGet:i,noDeleteOnStaleGet:l,status:v});const w={allowStale:t,updateAgeOnGet:i,noDeleteOnStaleGet:l,ttl:r,noDisposeOnSet:s,size:o,sizeCalculation:p,noUpdateTTL:d,noDeleteOnFetchRejection:u,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:m,ignoreFetchAbort:g,status:v,signal:k};let S=a(this,se).get(n);if(S===void 0){v&&(v.fetch="miss");const T=F(this,at,Lt).call(this,n,S,w,b);return T.__returned=T}else{const T=a(this,H)[S];if(F(this,X,ee).call(this,T)){const y=t&&T.__staleWhileFetching!==void 0;return v&&(v.fetch="inflight",y&&(v.returnedStale=!0)),y?T.__staleWhileFetching:T.__returned=T}const O=a(this,be).call(this,S);if(!$&&!O)return v&&(v.fetch="hit"),F(this,et,ct).call(this,S),i&&a(this,tt).call(this,S),v&&a(this,Be).call(this,v,S),T;const j=F(this,at,Lt).call(this,n,S,w,b),E=j.__staleWhileFetching!==void 0&&t;return v&&(v.fetch=O?"stale":"refresh",E&&O&&(v.returnedStale=!0)),E?j.__staleWhileFetching:j.__returned=j}}get(n,e={}){const{allowStale:t=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:l=this.noDeleteOnStaleGet,status:r}=e,s=a(this,se).get(n);if(s!==void 0){const o=a(this,H)[s],p=F(this,X,ee).call(this,o);return r&&a(this,Be).call(this,r,s),a(this,be).call(this,s)?(r&&(r.get="stale"),p?(r&&t&&o.__staleWhileFetching!==void 0&&(r.returnedStale=!0),t?o.__staleWhileFetching:void 0):(l||this.delete(n),r&&t&&(r.returnedStale=!0),t?o:void 0)):(r&&(r.get="hit"),p?o.__staleWhileFetching:(F(this,et,ct).call(this,s),i&&a(this,tt).call(this,s),o))}else r&&(r.get="miss")}delete(n){var e,t,i,l;let r=!1;if(a(this,oe)!==0){const s=a(this,se).get(n);if(s!==void 0)if(r=!0,a(this,oe)===1)this.clear();else{a(this,nt).call(this,s);const o=a(this,H)[s];F(this,X,ee).call(this,o)?o.__abortController.abort(new Error("deleted")):(a(this,De)||a(this,pe))&&(a(this,De)&&((e=a(this,Ie))==null||e.call(this,o,n,"delete")),a(this,pe)&&((t=a(this,ue))==null||t.push([o,n,"delete"]))),a(this,se).delete(n),a(this,Y)[s]=void 0,a(this,H)[s]=void 0,s===a(this,ce)?R(this,ce,a(this,ke)[s]):s===a(this,de)?R(this,de,a(this,he)[s]):(a(this,he)[a(this,ke)[s]]=a(this,he)[s],a(this,ke)[a(this,he)[s]]=a(this,ke)[s]),Vt(this,oe)._--,a(this,Ee).push(s)}}if(a(this,pe)&&(i=a(this,ue))!=null&&i.length){const s=a(this,ue);let o;for(;o=s==null?void 0:s.shift();)(l=a(this,Ce))==null||l.call(this,...o)}return r}clear(){var n,e,t;for(const i of F(this,Le,Me).call(this,{allowStale:!0})){const l=a(this,H)[i];if(F(this,X,ee).call(this,l))l.__abortController.abort(new Error("deleted"));else{const r=a(this,Y)[i];a(this,De)&&((n=a(this,Ie))==null||n.call(this,l,r,"delete")),a(this,pe)&&((e=a(this,ue))==null||e.push([l,r,"delete"]))}}if(a(this,se).clear(),a(this,H).fill(void 0),a(this,Y).fill(void 0),a(this,Te)&&a(this,Pe)&&(a(this,Te).fill(0),a(this,Pe).fill(0)),a(this,He)&&a(this,He).fill(0),R(this,de,0),R(this,ce,0),a(this,Ee).length=0,R(this,ve,0),R(this,oe,0),a(this,pe)&&a(this,ue)){const i=a(this,ue);let l;for(;l=i==null?void 0:i.shift();)(t=a(this,Ce))==null||t.call(this,...l)}}};let hl=Zn;ze=new WeakMap,ge=new WeakMap,Ie=new WeakMap,Ce=new WeakMap,ht=new WeakMap,oe=new WeakMap,ve=new WeakMap,se=new WeakMap,Y=new WeakMap,H=new WeakMap,he=new WeakMap,ke=new WeakMap,de=new WeakMap,ce=new WeakMap,Ee=new WeakMap,ue=new WeakMap,He=new WeakMap,Pe=new WeakMap,Te=new WeakMap,De=new WeakMap,Ze=new WeakMap,pe=new WeakMap,_t=new WeakSet,Gt=function(){const n=new At(a(this,ze)),e=new At(a(this,ze));R(this,Te,n),R(this,Pe,e),R(this,jt,(l,r,s=ut.now())=>{if(e[l]=r!==0?s:0,n[l]=r,r!==0&&this.ttlAutopurge){const o=setTimeout(()=>{a(this,be).call(this,l)&&this.delete(a(this,Y)[l])},r+1);o.unref&&o.unref()}}),R(this,tt,l=>{e[l]=n[l]!==0?ut.now():0}),R(this,Be,(l,r)=>{if(n[r]){const s=n[r],o=e[r];l.ttl=s,l.start=o,l.now=t||i(),l.remainingTTL=l.now+s-o}});let t=0;const i=()=>{const l=ut.now();if(this.ttlResolution>0){t=l;const r=setTimeout(()=>t=0,this.ttlResolution);r.unref&&r.unref()}return l};this.getRemainingTTL=l=>{const r=a(this,se).get(l);return r===void 0?0:n[r]===0||e[r]===0?1/0:e[r]+n[r]-(t||i())},R(this,be,l=>n[l]!==0&&e[l]!==0&&(t||i())-e[l]>n[l])},tt=new WeakMap,Be=new WeakMap,jt=new WeakMap,be=new WeakMap,Qt=new WeakSet,Kn=function(){const n=new At(a(this,ze));R(this,ve,0),R(this,He,n),R(this,nt,e=>{R(this,ve,a(this,ve)-n[e]),n[e]=0}),R(this,Mt,(e,t,i,l)=>{if(F(this,X,ee).call(this,t))return 0;if(!We(i))if(l){if(typeof l!="function")throw new TypeError("sizeCalculation must be a function");if(i=l(t,e),!We(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return i}),R(this,pt,(e,t,i)=>{if(n[e]=t,a(this,ge)){const l=a(this,ge)-n[e];for(;a(this,ve)>l;)F(this,mt,Ht).call(this,!0)}R(this,ve,a(this,ve)+n[e]),i&&(i.entrySize=t,i.totalCalculatedSize=a(this,ve))})},nt=new WeakMap,pt=new WeakMap,Mt=new WeakMap,Fe=new WeakSet,je=function*({allowStale:n=this.allowStale}={}){if(a(this,oe))for(let e=a(this,ce);!(!F(this,Ft,qt).call(this,e)||((n||!a(this,be).call(this,e))&&(yield e),e===a(this,de)));)e=a(this,ke)[e]},Le=new WeakSet,Me=function*({allowStale:n=this.allowStale}={}){if(a(this,oe))for(let e=a(this,de);!(!F(this,Ft,qt).call(this,e)||((n||!a(this,be).call(this,e))&&(yield e),e===a(this,ce)));)e=a(this,he)[e]},Ft=new WeakSet,qt=function(n){return n!==void 0&&a(this,se).get(a(this,Y)[n])===n},mt=new WeakSet,Ht=function(n){var e,t;const i=a(this,de),l=a(this,Y)[i],r=a(this,H)[i];return a(this,Ze)&&F(this,X,ee).call(this,r)?r.__abortController.abort(new Error("evicted")):(a(this,De)||a(this,pe))&&(a(this,De)&&((e=a(this,Ie))==null||e.call(this,r,l,"evict")),a(this,pe)&&((t=a(this,ue))==null||t.push([r,l,"evict"]))),a(this,nt).call(this,i),n&&(a(this,Y)[i]=void 0,a(this,H)[i]=void 0,a(this,Ee).push(i)),a(this,oe)===1?(R(this,de,R(this,ce,0)),a(this,Ee).length=0):R(this,de,a(this,he)[i]),a(this,se).delete(l),Vt(this,oe)._--,i},at=new WeakSet,Lt=function(n,e,t,i){const l=e===void 0?void 0:a(this,H)[e];if(F(this,X,ee).call(this,l))return l;const r=new AbortController,{signal:s}=t;s==null||s.addEventListener("abort",()=>r.abort(s.reason),{signal:r.signal});const o={signal:r.signal,options:t,context:i},p=(b,$=!1)=>{const{aborted:v}=r.signal,k=t.ignoreFetchAbort&&b!==void 0;if(t.status&&(v&&!$?(t.status.fetchAborted=!0,t.status.fetchError=r.signal.reason,k&&(t.status.fetchAbortIgnored=!0)):t.status.fetchResolved=!0),v&&!k&&!$)return u(r.signal.reason);const w=g;return a(this,H)[e]===g&&(b===void 0?w.__staleWhileFetching?a(this,H)[e]=w.__staleWhileFetching:this.delete(n):(t.status&&(t.status.fetchUpdated=!0),this.set(n,b,o.options))),b},d=b=>(t.status&&(t.status.fetchRejected=!0,t.status.fetchError=b),u(b)),u=b=>{const{aborted:$}=r.signal,v=$&&t.allowStaleOnFetchAbort,k=v||t.allowStaleOnFetchRejection,w=k||t.noDeleteOnFetchRejection,S=g;if(a(this,H)[e]===g&&(!w||S.__staleWhileFetching===void 0?this.delete(n):v||(a(this,H)[e]=S.__staleWhileFetching)),k)return t.status&&S.__staleWhileFetching!==void 0&&(t.status.returnedStale=!0),S.__staleWhileFetching;if(S.__returned===S)throw b},f=(b,$)=>{var v;const k=(v=a(this,ht))==null?void 0:v.call(this,n,l,o);k&&k instanceof Promise&&k.then(w=>b(w),$),r.signal.addEventListener("abort",()=>{(!t.ignoreFetchAbort||t.allowStaleOnFetchAbort)&&(b(),t.allowStaleOnFetchAbort&&(b=w=>p(w,!0)))})};t.status&&(t.status.fetchDispatched=!0);const g=new Promise(f).then(p,d),m=Object.assign(g,{__abortController:r,__staleWhileFetching:l,__returned:void 0});return e===void 0?(this.set(n,m,{...o.options,status:void 0}),e=a(this,se).get(n)):a(this,H)[e]=m,m},X=new WeakSet,ee=function(n){if(!a(this,Ze))return!1;const e=n;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof AbortController},It=new WeakSet,Ut=function(n,e){a(this,ke)[e]=n,a(this,he)[n]=e},et=new WeakSet,ct=function(n){n!==a(this,ce)&&(n===a(this,de)?R(this,de,a(this,he)[n]):F(this,It,Ut).call(this,a(this,ke)[n],a(this,he)[n]),F(this,It,Ut).call(this,a(this,ce),n),R(this,ce,n))};const pl=hl,ot=n=>{const e=n.value,t=e.substring(0,n.selectionStart),i=e.substring(n.selectionEnd,e.length),l=t.lastIndexOf(`
`),r=t.substring(0,l+1),s=i.indexOf(`
`),o=i.substring(s===-1?i.length:s,i.length),p=t.substring(l+1,t.length),d=i.substring(0,s);return{prefixStr:t,subfixStr:i,prefixStrEndRow:r,subfixStrEndRow:o,prefixSupply:p,subfixSupply:d}},ml=(n,e="",t,i)=>{var l,r,s,o,p,d;let u="",f=0,g=0,m=!1,b,$;const v=(l=ie.editorConfig)==null?void 0:l.mermaidTemplate;if(/^h[1-6]{1}$/.test(n)){const k=n.replace(/^h(\d)/,(w,S)=>new Array(Number(S)).fill("#",0,S).join(""));u=`${k} ${e}`,f=k.length+1}else if(n==="prettier"){const k=window.prettier||((s=(r=ie.editorExtensions)==null?void 0:r.prettier)==null?void 0:s.prettierInstance),w=[((o=window.prettierPlugins)==null?void 0:o.markdown)||((d=(p=ie.editorExtensions)==null?void 0:p.prettier)==null?void 0:d.parserMarkdownInstance)];return!k||w[0]===void 0?(x.emit(i.editorId,"errorCatcher",{name:"prettier",message:"prettier is undefined"}),t.value):k.format(t.value,{parser:"markdown",plugins:w})}else switch(n){case"bold":{u=`**${e}**`,f=2,g=-2;break}case"underline":{u=`<u>${e}</u>`,f=3,g=-4;break}case"italic":{u=`*${e}*`,f=1,g=-1;break}case"strikeThrough":{u=`~~${e}~~`,f=2,g=-2;break}case"sub":{u=`<sub>${e}</sub>`,f=5,g=-6;break}case"sup":{u=`<sup>${e}</sup>`,f=5,g=-6;break}case"codeRow":{u="`"+e+"`",f=1,g=-1;break}case"quote":{u=`> ${e}`,f=2;break}case"orderedList":{u=`1. ${e}`,f=3;break}case"unorderedList":{u=`- ${e}`,f=2;break}case"task":{u=`- [ ] ${e}`,f=6,m=!0;break}case"code":{const k=i.text||e||"",w=i.mode||"language";u=`\`\`\`${w}
${k}
\`\`\`
`,f=3,g=3+w.length-u.length,m=!0;break}case"table":{u="|";const{selectedShape:k={x:1,y:1}}=i,{x:w,y:S}=k;for(let T=0;T<=S;T++)u+=" col |";u+=`
|`;for(let T=0;T<=S;T++)u+=" - |";for(let T=0;T<=w;T++){u+=`
|`;for(let O=0;O<=S;O++)u+=" content |"}f=2,g=5-u.length,m=!0;break}case"link":{const{desc:k,url:w}=i;u=`[${k}](${w})`;break}case"image":{const{desc:k,url:w,urls:S}=i;S instanceof Array?u=S.reduce((T,O)=>T+`![${k}](${O})
`,""):u=`![${k}](${w})
`;break}case"tab":{const{tabWidth:k=2}=i,w=new Array(k).fill(" ").join("");if(e==="")u=w;else if(/\n/.test(e)){const{prefixStr:S,subfixStr:T,prefixSupply:O,subfixSupply:j}=ot(t);u=`${O}${e}${j}`.split(`
`).map(E=>`${w}${E}`).join(`
`),b=S.substring(0,S.length-O.length),$=T.substring(j.length,T.length),m=!0,f=k,g=-O.length-j.length}else{const S=t.value.substring(0,t.selectionStart);/\n$/.test(S)||S===""?(u=`${w}${e}`,m=!0):u=w}break}case"shiftTab":{const{tabWidth:k=2}=i,{prefixStr:w,prefixStrEndRow:S,subfixStrEndRow:T,prefixSupply:O,subfixSupply:j}=ot(t),E=new RegExp(`^\\s{${k}}`),y=(J=!1,U=!1)=>{const A=`${O}${e}${j}`;if(E.test(A)){const D=w.length-(U?0:k),P=J?D+e.length-k:D;return Oe(t,D,P),`${S}${A.replace(E,"")}${T}`}else if(/^\s/.test(A)){const D=A.replace(/^\s/,""),P=A.length-D.length,M=t.selectionStart-(U?0:P),C=J?M+e.length-P:M;return Oe(t,M,C),`${S}${D}${T}`}else u=e};if(e===""){const J=y();if(J)return J}else if(/\n/.test(e)){const J=`${O}${e}${j}`.split(`
`);let[U,A]=[0,0];const D=J.map((P,M)=>{if(E.test(P))return M===0&&(U=k),A+=k,P.replace(E,"");if(/^\s/.test(P)){const C=P.replace(/^\s/,"");return A+=P.length-C.length,C}return P}).join(`
`);return Oe(t,t.selectionStart-U,t.selectionEnd-A),`${S}${D}${T}`}else{const J=y(!0,!0);if(J)return J}break}case"ctrlC":{const{prefixSupply:k,subfixSupply:w}=ot(t);return Et(e===""?`${k}${w}`:e),t.value}case"ctrlX":{const{prefixStrEndRow:k,subfixStrEndRow:w,prefixStr:S,subfixStr:T,prefixSupply:O,subfixSupply:j}=ot(t);return e===""?(Et(`${O}${j}`),Oe(t,k.length),`${k}${w.replace(/^\n/,"")}`):(Et(e),Oe(t,S.length),`${S}${T}`)}case"ctrlD":{const{prefixStrEndRow:k,subfixStrEndRow:w}=ot(t);return Oe(t,k.length),`${k}${w.replace(/^\n/,"")}`}case"flow":{u=`\`\`\`mermaid
${(v==null?void 0:v.flow)||`flowchart TD 
  Start --> Stop`}
\`\`\`
`,f=2;break}case"sequence":{u=`\`\`\`mermaid
${(v==null?void 0:v.sequence)||`sequenceDiagram
  A->>B: hello!
  B-->>A: hi!
  A-)B: bye!`}
\`\`\`
`,f=2;break}case"gantt":{u=`\`\`\`mermaid
${(v==null?void 0:v.gantt)||`gantt
title A Gantt Diagram
dateFormat  YYYY-MM-DD
section Section
A task  :a1, 2014-01-01, 30d
Another task  :after a1, 20d`}
\`\`\`
`,f=2;break}case"class":{u=`\`\`\`mermaid
${(v==null?void 0:v.class)||`classDiagram
  class Animal
  Vehicle <|-- Car`}
\`\`\`
`,f=2;break}case"state":{u=`\`\`\`mermaid
${(v==null?void 0:v.state)||`stateDiagram-v2
  s1 --> s2`}
\`\`\`
`,f=2;break}case"pie":{u=`\`\`\`mermaid
${(v==null?void 0:v.pie)||`pie title Pets adopted by volunteers
  "Dogs" : 386
  "Cats" : 85
  "Rats" : 15`}
\`\`\`
`,f=2;break}case"relationship":{u=`\`\`\`mermaid
${(v==null?void 0:v.relationship)||`erDiagram
  CAR ||--o{ NAMED-DRIVER : allows
  PERSON ||--o{ NAMED-DRIVER : is`}
\`\`\`
`,f=2;break}case"journey":{u=`\`\`\`mermaid
${(v==null?void 0:v.journey)||`journey
  title My working day
  section Go to work
    Make tea: 5: Me
    Go upstairs: 3: Me
    Do work: 1: Me, Cat
  section Go home
    Go downstairs: 5: Me
    Sit down: 5: Me`}
\`\`\`
`,f=2;break}case"katexInline":{u="$$",f=1,g=-1;break}case"katexBlock":{u=`$$

$$
`,f=1,g=-4;break}case"universal":{const{generate:k}=i,w=k(e);u=w.targetValue,m=w.select,f=w.deviationStart,g=w.deviationEnd}}return Kt(t,u,{deviationStart:f,deviationEnd:g,select:m,prefixVal:b,subfixVal:$})},Ln={block(n,e){return{name:"KaTexBlockExtension",level:"block",start:t=>{var i;return(i=t.match(/\n\$\$/))==null?void 0:i.index},tokenizer(t){if(/^\$\$/.test(t)&&t.split("$$").length>2){const i=vn(t,"$$");return{type:"KaTexBlockExtension",raw:i[0],text:i[1].trim(),tokens:[]}}},renderer(t){const i=e||typeof window<"u"&&window.katex;if(i){const l=i.renderToString(t.text,{throwOnError:!1,displayMode:!0});return`<p class="${n}-katex-block" data-processed>${l}</p>`}else return`<p class="${n}-katex-block">${t.text}</p>`}}},inline(n,e){return{name:"KaTexInlineExtension",level:"inline",start:t=>{var i;return(i=t.match(/\$[^\n]*/))==null?void 0:i.index},tokenizer(t){if(/^\$[^\n]*\$/.test(t)){const i=vn(t);return{type:"KaTexInlineExtension",raw:i[0],text:i[1].trim(),tokens:[]}}},renderer(t){const i=e||typeof window<"u"&&window.katex;if(i){const l=i.renderToString(t.text,{throwOnError:!1});return`<span class="${n}-katex-inline" data-processed>${l}</span>`}else return`<span class="${n}-katex-inline">${t.text}</span>`}}}},Gn=["abstract","attention","bug","caution","danger","error","example","failure","hint","info","note","question","quote","success","tip","warning"],Nt=new RegExp(`^!!!\\s*(${Gn.join("|")})\\s*(.*)$`),fl=/^!!!\s*$/,gl={name:"alert",level:"block",start(n){var e;return(e=n.match(new RegExp(`(^|[\\r\\n])!!!\\s*(${Gn.join("|")})\\s*(.*)`)))==null?void 0:e.index},tokenizer(n){const e=n.split(/\n/);if(Nt.test(e[0])){const t={x:-1,y:-1},i=[];for(let l=0,r=e.length;l<r;l++)Nt.test(e[l])?t.x=l:fl.test(e[l])&&(t.y=l,t.x>=0&&(i.push({...t}),t.x=-1,t.y=-1));if(i.length){const l=i[0],[r,s,o]=Nt.exec(e[l.x])||[],p=e.slice(l.x+1,l.y).join(`
`),d={type:"alert",raw:e.slice(l.x,l.y+1).join(`
`),icon:s,title:o,text:p,titleTokens:[],tokens:[],childTokens:["title","text"]};return this.lexer.inlineTokens(d.title,d.titleTokens),this.lexer.blockTokens(d.text,d.tokens),d}}},renderer(n){return`<div class="md-editor-alert md-editor-alert-${n.icon}">
     <p class="md-editor-alert-title">${this.parser.parseInline(n.titleTokens,null)}</p>
     ${this.parser.parse(n.tokens)}
     </div>`}},vl=typeof window>"u",kl=(n,e,t)=>{var i,l;const r=W("previewOnly"),s=W("historyLength"),o=W("editorId");if(r)return;let p=-1;const d={list:[{content:n.value,startPos:((i=e.value)==null?void 0:i.selectionStart)||0,endPos:((l=e.value)==null?void 0:l.selectionEnd)||0}],userUpdated:!0,curr:0},u=[0,0];let f=u;const g=$=>{var v,k;const w=((v=e.value)==null?void 0:v.selectionStart)||0,S=((k=e.value)==null?void 0:k.selectionEnd)||0;d.list[d.curr].startPos=w,d.list[d.curr].endPos=S,d.userUpdated=!1,d.curr=$;const T=d.list[d.curr];f=[T.startPos,T.endPos],n.onChange(T.content),Oe(e.value,T.startPos,T.endPos).then(()=>{x.emit(o,"selectTextChange")})},m=$=>{var v,k;clearTimeout(p);const w=((v=e.value)==null?void 0:v.selectionStart)||0,S=((k=e.value)==null?void 0:k.selectionEnd)||0;p=setTimeout(()=>{if(d.userUpdated){d.curr<d.list.length-1&&(d.list=d.list.slice(0,d.curr+1)),d.list.length>s&&d.list.shift();const T=d.list.pop()||{startPos:0,endPos:0,content:$};T.startPos=f[0],T.endPos=f[1],f=u,Array.prototype.push.call(d.list,T,{content:$,startPos:w,endPos:S}),d.curr=d.list.length-1}else d.userUpdated=!0},150)},b=$=>{var v,k;(f===u||$)&&(f=[(v=e.value)==null?void 0:v.selectionStart,(k=e.value)==null?void 0:k.selectionEnd])};N([Ot(n,"value"),t],()=>{t.value&&m(n.value)}),N(()=>n.value,()=>{x.emit(o,"selectTextChange")},{flush:"post"}),ne(()=>{x.on(o,{name:"ctrlZ",callback(){g(d.curr-1<0?0:d.curr-1)}}),x.on(o,{name:"ctrlShiftZ",callback(){g(d.curr+1===d.list.length?d.curr:d.curr+1)}}),x.on(o,{name:"saveHistoryPos",callback:b})})},bl=(n,e)=>{var t;const i=(t=ie.editorExtensions)==null?void 0:t.katex,l=i==null?void 0:i.instance,r=te(!!l);return n.noKatex||e.use({extensions:[Ln.inline(h,l),Ln.block(h,l)]}),ne(()=>{if(!n.noKatex&&!l){const s=document.createElement("script");s.src=(i==null?void 0:i.js)||pn.js,s.onload=()=>{r.value=!0},s.id=`${h}-katex`;const o=document.createElement("link");o.rel="stylesheet",o.href=(i==null?void 0:i.css)||pn.css,o.id=`${h}-katexCss`,we(s,"katex"),we(o)}}),r},yl=n=>{var e,t;const{markedRenderer:i,markedExtensions:l,markedOptions:r,editorExtensions:s,editorConfig:o}=ie,p=W("showCodeRowNumber"),d=W("editorId"),u=W("highlight"),f=W("previewOnly"),g=W("theme"),m=(e=s==null?void 0:s.highlight)==null?void 0:e.instance,b=(t=s==null?void 0:s.mermaid)==null?void 0:t.instance,$=te(!!m),v=te([]);let k=[],w=[];const S=new pl({max:1e3,ttl:6e5}),T=$l(n),O=bl(n,_),j=xe(()=>(n.noMermaid||T.mermaidInited)&&$.value&&(n.noKatex||O.value));let E=new _.Renderer;E.image=(C,L,q)=>`<span class="figure"><img src="${C}" title="${L||""}" alt="${q||""}" zoom><span class="figcaption">${q||""}</span></span>`,E.listitem=(C,L)=>L?`<li class="li-task">${C}</li>`:`<li>${C}</li>`,i instanceof Function&&(E=i(E));const y=E.code;E.code=(C,L,q)=>{if(!n.noMermaid&&L==="mermaid"){const Q=fi();try{if(vl)return`<p class="${h}-mermaid">${C}</p>`;{const me=S.get(C);if(me)return`<p class="${h}-mermaid" data-processed>${me}</p>`;const Ue=b||window.mermaid;if(T.mermaidInited){const Ne=(Ue.renderAsync||Ue.render)(Q,C);Ne.then(fe=>{S.set(C,typeof fe=="string"?fe:fe.svg)}),k.push(Ne)}else return`<p class="${h}-mermaid">${C}</p>`}const Z=`<script type="text/tmplate">${Q}<\/script>`;return w.push(Z),Z}catch(Z){return`<p class="${h}-mermaid-error">Error: ${(Z==null?void 0:Z.message)||""}</p>`}}return y.call(E,C,L,q).replace(/^<pre><code\sclass="language-([^>]*)">/,'<pre><code class="language-$1" language="$1">')};const J=E.heading,U=J!==new _.Renderer().heading;E.heading=(C,L,q,Q)=>{v.value.push({text:q,level:L});const Z=n.markedHeadingId(q,L,v.value.length);return U?J.call(E,C,L,q,Q,v.value.length,Z):C!==q?`<h${L} id="${Z}">${C}</h${L}>`:`<h${L} id="${Z}"><a href="#${Z}">${q}</a></h${L}>`},m&&_.setOptions({highlight:(C,L)=>{let q;const Q=m.getLanguage(L);return L&&Q?q=m.highlight(C,{language:L,ignoreIllegals:!0}).value:q=m.highlightAuto(C).value,p?gn(q.trim()):`<span class="code-block">${q.trim()}</span>`}}),_.setOptions({breaks:!0,...r}),l instanceof Array&&l.length>0&&_.use({extensions:l}),_.use({extensions:[gl]});const A=te(n.sanitize(_(n.value||"",{renderer:E}))),D=async C=>{let L=n.sanitize(_(C,{renderer:E}));const q=[...k],Q=[...w];return w=[],k=[],(await Promise.allSettled(q)).forEach((Z,me)=>{Z.status==="fulfilled"?L=L.replace(Q[me],`<p class="${h}-mermaid" data-processed>${typeof Z.value=="string"?Z.value:Z.value.svg}</p>`):L=L.replace(Q[me],`<p class="${h}-mermaid-error">${Z.reason||""}</p>`)}),L},P=Wt(async()=>{v.value=[];const C=await D(n.value||"");A.value=C,x.emit(d,"buildFinished",A.value),n.onHtmlChanged(A.value)},(o==null?void 0:o.renderDelay)!==void 0?o==null?void 0:o.renderDelay:f?0:500);N([j,Ot(T,"reRender"),Ot(n,"value")],P),N([g],()=>{S.clear()});const M=()=>{_.setOptions({highlight:(C,L)=>{let q;const Q=window.hljs.getLanguage(L);return L&&Q?q=window.hljs.highlight(C,{language:L,ignoreIllegals:!0}).value:q=window.hljs.highlightAuto(C).value,p?gn(q.trim()):`<span class="code-block">${q.trim()}</span>`}}),$.value=!0};return N(()=>v.value,C=>{n.onGetCatalog(C),x.emit(d,"catalogChanged",C)}),ne(()=>{if(!m){const C=document.createElement("script");C.src=u.value.js,C.onload=M,C.id=`${h}-hljs`,we(C,"hljs");const L=document.createElement("link");L.rel="stylesheet",L.href=u.value.css,L.id=`${h}-hlCss`,we(L)}}),N(()=>u.value.css,C=>{gi(`${h}-hlCss`,"href",C)}),ne(()=>{x.on(d,{name:"pushCatalog",callback(){x.emit(d,"catalogChanged",v.value)}})}),A},wl=(n,e,t,i,l)=>{const r=W("previewOnly"),s=W("usedLanguageText"),o=W("editorId");let p=()=>{},d=()=>{};const u=()=>{document.querySelectorAll(`#${o}-preview pre`).forEach(m=>{var b,$;let v=-1;(b=m.querySelector(".copy-button"))==null||b.remove();const k=(($=s.value.copyCode)==null?void 0:$.text)||"复制代码",w=document.createElement("span");w.setAttribute("class","copy-button"),w.dataset.tips=k,w.innerHTML=`<svg class="${h}-icon" aria-hidden="true"><use xlink:href="#${h}-icon-copy"></use></svg>`,w.addEventListener("click",()=>{var S,T;clearTimeout(v);const O=m.querySelector("code").innerText,j=Et(n.formatCopiedText(O)),E=((S=s.value.copyCode)==null?void 0:S.successTips)||"已复制！",y=((T=s.value.copyCode)==null?void 0:T.failTips)||"已复制！";w.dataset.tips=j?E:y,v=window.setTimeout(()=>{w.dataset.tips=k},1500)}),m.appendChild(w)})},f=()=>{it(()=>{n.setting.preview&&!r&&n.scrollAuto&&(p(),d()),u()})},g=m=>{m&&!r&&it(()=>{p(),[d,p]=fn(t.value,i.value||l.value),d(),u()})};N(()=>e.value,f),N(()=>s.value,u),N(()=>n.setting.preview,g),N(()=>n.setting.htmlPreview,g),N(()=>n.scrollAuto,m=>{m?d():p()}),ne(()=>{u(),!r&&(i.value||l.value)&&([d,p]=fn(t.value,i.value||l.value)),n.scrollAuto&&d()})},xl=(n,e)=>{const t=W("previewOnly"),i=W("tabWidth"),l=W("editorId"),r=te("");ne(()=>{var s;t||((s=e.value)==null||s.addEventListener("keypress",o=>{var p,d,u;if(o.key==="Enter"){const f=(p=e.value)==null?void 0:p.selectionStart,g=(d=e.value)==null?void 0:d.value.substring(0,f),m=(u=e.value)==null?void 0:u.value.substring(f),b=g==null?void 0:g.lastIndexOf(`
`),$=g==null?void 0:g.substring(b+1,f);if(/^\d+\.\s|^-\s/.test($))if(o.cancelBubble=!0,o.preventDefault(),o.stopPropagation(),/^(\d+\.|-)\s+(\[[x\s]\]\s+)?$/.test($)){const v=g==null?void 0:g.replace(/(\d+\.|-)\s+(\[[x\s]\]\s+)?$/,"");n.onChange(v+m),Oe(e.value,v==null?void 0:v.length)}else if(/^-\s+.+/.test($)){const v=/^-\s+\[[x\s]\]/.test($)?`
- [ ] `:`
- `;n.onChange(Kt(e.value,v,{}))}else{const v=$==null?void 0:$.match(/\d+(?=\.)/),k=v&&Number(v[0])+1||1,w=/^\d\.\s+\[[x\s]\]/.test($)?`
${k}. [ ] `:`
${k}. `;n.onChange(Kt(e.value,w,{}))}}}),x.on(l,{name:"replace",callback(o,p={}){n.onChange(ml(o,r.value,e.value,{...p,tabWidth:i,editorId:l}))}}),x.on(l,{name:"selectTextChange",callback(){r.value=hi(e.value)}}))}),N(()=>n.value,()=>{r.value=""})},$l=n=>{const e=W("theme"),{editorExtensions:t}=ie,i=t==null?void 0:t.mermaid,l=ye({reRender:!1,mermaidInited:!!(i!=null&&i.instance)}),r=()=>{const s=(i==null?void 0:i.instance)||window.mermaid;!n.noMermaid&&s&&(s.initialize({startOnLoad:!1,theme:e.value==="dark"?"dark":"default"}),l.reRender=!l.reRender)};return N(()=>e.value,r),ne(()=>{if(!n.noMermaid)if(i!=null&&i.instance)r(),l.mermaidInited=!0;else{const s=document.createElement("script");s.id=`${h}-mermaid`;const o=(i==null?void 0:i.js)||oi;/\.mjs/.test(o)?(s.setAttribute("type","module"),s.innerHTML=`import mermaid from "${o}";window.mermaid=mermaid;document.getElementById('${h}-mermaid').dispatchEvent(new Event('load'));`):s.src=o,s.onload=()=>{r(),l.mermaidInited=!0},we(s,"mermaid")}}),l},Sl=(n,e)=>{const t=W("editorId"),i=W("previewOnly"),l=r=>{if(r.clipboardData){if(r.clipboardData.files.length>0){const{files:s}=r.clipboardData;x.emit(t,"uploadImage",Array.from(s).filter(o=>/image\/.*/.test(o.type))),r.preventDefault()}if(n.autoDetectCode&&r.clipboardData.types.includes("vscode-editor-data")){const s=JSON.parse(r.clipboardData.getData("vscode-editor-data"));x.emit(t,"replace","code",{mode:s.mode,text:r.clipboardData.getData("text/plain")}),r.preventDefault()}}};ne(()=>{i||e.value.addEventListener("paste",l)}),ft(()=>{i||e.value.removeEventListener("paste",l)})},Cl=(n,e)=>{const t=W("editorId"),i=Wt(()=>{const l=document.querySelectorAll(`#${t}-preview img[zoom]`);l.length!==0&&cl(l,{background:"#00000073"})});ne(i),N([e,Ot(n.setting,"preview")],i)},Tl=n=>{const e=W("editorId");ne(()=>{x.on(e,{name:Rn,callback(){var t;(t=n.value)==null||t.focus()}})})},El=()=>({value:{type:String,default:""},onChange:{type:Function,default:()=>{}},setting:{type:Object,default:()=>({})},onHtmlChanged:{type:Function,default:()=>{}},onGetCatalog:{type:Function,default:()=>{}},markedHeadingId:{type:Function,default:()=>""},noMermaid:{type:Boolean,default:!1},sanitize:{type:Function,default:n=>n},placeholder:{type:String,default:""},noKatex:{type:Boolean,default:!1},scrollAuto:{type:Boolean},formatCopiedText:{type:Function,default:n=>n},autofocus:{type:Boolean},disabled:{type:Boolean},readonly:{type:Boolean},maxlength:{type:Number},autoDetectCode:{type:Boolean},onBlur:{type:Function,default:()=>{}},onFocus:{type:Function,default:()=>{}}}),zl=le({name:"MDEditorContent",props:El(),setup(n){const e=te(!0),t=W("previewOnly"),i=W("showCodeRowNumber"),l=W("previewTheme"),r=W("editorId"),s=te(),o=te(),p=te(),d=yl(n);return wl(n,d,s,o,p),xl(n,s),kl(n,s,e),Sl(n,s),Cl(n,d),Tl(s),()=>{const u=mi(n,["formatCopiedText","markedHeadingId","noKatex","noMermaid","onChange","onGetCatalog","onHtmlChanged","sanitize","scrollAuto","setting","autoDetectCode","onBlur","onFocus"]);return c(Bt,null,[c("div",{class:`${h}-content`},[!t&&c("div",{class:`${h}-input-wrapper`},[c("textarea",Xn(u,{id:`${r}-textarea`,class:[n.setting.preview||n.setting.htmlPreview?"":"textarea-only"],ref:s,onBlur:f=>{x.emit(r,"selectTextChange"),n.onBlur(f)},onFocus:n.onFocus,onKeydown:()=>{x.emit(r,"saveHistoryPos",!0)},onCompositionstart:()=>{e.value=!1},onInput:f=>{n.onChange(f.target.value)},onCompositionend:()=>{e.value=!0}}),null)]),n.setting.preview&&c("div",{id:`${r}-preview-wrapper`,class:`${h}-preview-wrapper`,ref:o,key:"content-preview-wrapper"},[c("article",{id:`${r}-preview`,class:[`${h}-preview`,`${l==null?void 0:l.value}-theme`,i&&`${h}-scrn`],innerHTML:d.value},null)]),n.setting.htmlPreview&&c("div",{class:`${h}-preview-wrapper`,ref:p,key:"html-preview-wrapper"},[c("div",{class:`${h}-html`},[d.value])])])])}}}),Al=le({props:{modelValue:{type:String,default:""}},setup(n){const e=W("usedLanguageText");return()=>{var t,i;return c("div",{class:`${h}-footer-item`},[c("label",{class:`${h}-footer-label`},[`${(t=e.value.footer)==null?void 0:t.markdownTotal}:`]),c("span",null,[((i=n.modelValue)==null?void 0:i.length)||0])])}}}),_l=()=>({checked:{type:Boolean,default:!1},onChange:{type:Function,default:()=>{}}}),Fl=le({props:_l(),setup(n){return()=>c("div",{class:[`${h}-checkbox`,n.checked&&`${h}-checkbox-checked`],onClick:()=>{n.onChange(!n.checked)}},null)}}),Ll=()=>({scrollAuto:{type:Boolean},onScrollAutoChange:{type:Function,default:()=>{}}}),Il=le({props:Ll(),setup(n){const e=W("usedLanguageText");return()=>{var t;return c("div",{class:`${h}-footer-item`},[c("label",{class:`${h}-footer-label`,onClick:()=>{n.onScrollAutoChange(!n.scrollAuto)}},[(t=e.value.footer)==null?void 0:t.scrollAuto]),c(Fl,{checked:n.scrollAuto,onChange:n.onScrollAutoChange},null)])}}}),Ol=()=>({modelValue:{type:String,default:""},footers:{type:Array,default:[]},scrollAuto:{type:Boolean},onScrollAutoChange:{type:Function,default:()=>{}},defFooters:{type:Object}}),Dl=le({name:"MDEditorFooter",props:Ol(),setup(n){const e=xe(()=>{const i=n.footers.indexOf("="),l=i===-1?n.footers:n.footers.slice(0,i),r=i===-1?[]:n.footers.slice(i,Number.MAX_SAFE_INTEGER);return[l,r]}),t=i=>{if(On.includes(i))switch(i){case"markdownTotal":return c(Al,{modelValue:n.modelValue},null);case"scrollSwitch":return c(Il,{scrollAuto:n.scrollAuto,onScrollAutoChange:n.onScrollAutoChange},null)}else return n.defFooters instanceof Array?n.defFooters[i]||"":n.defFooters&&n.defFooters.children instanceof Array&&n.defFooters.children[i]||""};return()=>{const i=e.value[0].map(r=>t(r)),l=e.value[1].map(r=>t(r));return c("div",{class:`${h}-footer`},[c("div",{class:`${h}-footer-left`},[i]),c("div",{class:`${h}-footer-right`},[l])])}}}),Rl=()=>({tocItem:{type:Object,default:()=>({})},markedHeadingId:{type:Function,default:()=>{}},scrollElement:{type:[String,Object],default:""},onClick:{type:Function,default:()=>{}},scrollElementOffsetTop:{type:Number,default:0}}),Qn=le({props:Rl(),setup(n){return()=>{const{tocItem:e,markedHeadingId:t,scrollElement:i,onClick:l,scrollElementOffsetTop:r}=n;return c("div",{class:[`${h}-catalog-link`,e.active&&`${h}-catalog-active`],onClick:s=>{l(s,e),s.stopPropagation();const o=t(e.text,e.level,e.index),p=document.getElementById(o),d=i instanceof Element?i:document.querySelector(i);if(p&&d){let u=p.offsetParent,f=p.offsetTop;if(d.contains(u))for(;u&&d!=u;)f+=u==null?void 0:u.offsetTop,u=u==null?void 0:u.offsetParent;d==null||d.scrollTo({top:f-r,behavior:"smooth"})}}},[c("span",{title:e.text},[e.text]),c("div",{class:`${h}-catalog-wrapper`},[e.children&&e.children.map(s=>c(Qn,{markedHeadingId:t,key:`${e.text}-link-${s.level}-${s.text}`,tocItem:s,scrollElement:i,onClick:l,scrollElementOffsetTop:r},null))])])}}}),jl=()=>({editorId:{type:String},class:{type:String,default:""},markedHeadingId:{type:Function,default:n=>n},scrollElement:{type:[String,Object]},theme:{type:String,default:"light"},offsetTop:{type:Number,default:20},scrollElementOffsetTop:{type:Number,default:0},onClick:{type:Function}}),Pt=le({name:"MdCatalog",props:jl(),emits:["onClick"],setup(n,e){const t=n.editorId,i=ye({list:[],show:!1,scrollElement:n.scrollElement||`#${t}-preview-wrapper`}),l=xe(()=>{const s=[];return i.list.forEach(({text:o,level:p,active:d},u)=>{const f={level:p,text:o,index:u+1,active:!!d};if(s.length===0)s.push(f);else{let g=s[s.length-1];if(f.level>g.level)for(let m=g.level+1;m<=6;m++){const{children:b}=g;if(!b){g.children=[f];break}if(g=b[b.length-1],f.level<=g.level){b.push(f);break}}else s.push(f)}}),s});ne(()=>{x.on(t,{name:"catalogChanged",callback:s=>{i.list=s.map((o,p)=>p===0?{...o,active:!0}:{...o})}}),x.emit(t,"pushCatalog")});const r=()=>{var s;const o=i.scrollElement instanceof HTMLElement?i.scrollElement:document.querySelector(i.scrollElement);(s=o===document.documentElement?window:o)==null||s.addEventListener("scroll",di(()=>{if(i.list.length===0)return!1;const{activeHead:p}=i.list.reduce((d,u,f)=>{const g=document.getElementById(n.markedHeadingId(u.text,u.level,f+1));if(g instanceof HTMLElement){const m=pi(g,o);if(m<n.offsetTop&&m>d.minTop)return{activeHead:u,minTop:m}}return d},{activeHead:i.list[0],minTop:Number.MIN_SAFE_INTEGER});i.list=i.list.map(d=>d===p?{...d,active:!0}:{...d,active:!1})}))};return ne(()=>{r(),x.on(t,{name:Zt,callback(s){s&&it(r)}})}),()=>c("div",{class:`${h}-catalog${n.theme==="dark"?"-dark":""} ${n.class}`},[l.value.map(s=>c(Qn,{markedHeadingId:n.markedHeadingId,tocItem:s,key:`link-${s.level}-${s.text}`,scrollElement:i.scrollElement,onClick:(o,p)=>{n.onClick?n.onClick(o,p):e.emit("onClick",o,p)},scrollElementOffsetTop:n.scrollElementOffsetTop},null))])}}),Ml=n=>n,Hl={modelValue:{type:String,default:""},theme:{type:String,default:"light"},class:{type:String,default:""},historyLength:{type:Number,default:10},onChange:{type:Function},onSave:{type:Function},onUploadImg:{type:Function},pageFullscreen:{type:Boolean,default:!1},preview:{type:Boolean,default:!0},htmlPreview:{type:Boolean,default:!1},previewOnly:{type:Boolean,default:!1},language:{type:String,default:"zh-CN"},toolbars:{type:Array,default:In},toolbarsExclude:{type:Array,default:[]},noPrettier:{type:Boolean,default:!1},onHtmlChanged:{type:Function},onGetCatalog:{type:Function},editorId:{type:String,default:ni},tabWidth:{type:Number,default:2},showCodeRowNumber:{type:Boolean,default:!1},previewTheme:{type:String,default:"default"},style:{type:Object,default:()=>({})},markedHeadingId:{type:Function,default:Ml},tableShape:{type:Array,default:()=>[6,4]},noMermaid:{type:Boolean,default:!1},sanitize:{type:Function,default:n=>n},placeholder:{type:String,default:""},noKatex:{type:Boolean,default:!1},defToolbars:{type:[String,Object]},onError:{type:Function},codeTheme:{type:String,default:"atom"},footers:{type:Array,default:On},scrollAuto:{type:Boolean,default:!0},defFooters:{type:[String,Object]},noIconfont:{type:Boolean},formatCopiedText:{type:Function,default:n=>n},noUploadImg:{type:Boolean},codeStyleReverse:{type:Boolean,default:!0},codeStyleReverseList:{type:Array,default:["default","mk-cute"]},autoFocus:{type:Boolean},disabled:{type:Boolean},readOnly:{type:Boolean},maxLength:{type:Number},autoDetectCode:{type:Boolean},onBlur:{type:Function},onFocus:{type:Function}},Pl=["onChange","onSave","onUploadImg","onHtmlChanged","onGetCatalog","onError","update:modelValue","onBlur","onFocus"],qe=le({name:"MdEditorV3",props:Hl,emits:Pl,setup(n,e){const{editorId:t,previewOnly:i,noKatex:l,noMermaid:r,noPrettier:s,noUploadImg:o}=n,p=ye({scrollAuto:n.scrollAuto});vi(n,e),ki(n),bi(n);const[d,u]=yi(n,e),[f,g]=wi(n);return ft(()=>{x.clear(t)}),xi(n,e,f,d,u),()=>{var m;const b=$e({props:n,ctx:e},"defToolbars"),$=$e({props:n,ctx:e},"defFooters");return c("div",{id:t,class:[h,n.class,n.theme==="dark"&&`${h}-dark`,d.fullscreen||d.pageFullscreen?`${h}-fullscreen`:"",i&&`${h}-previewOnly`],style:n.style},[!i&&c(Ri,{noPrettier:s,toolbars:n.toolbars,toolbarsExclude:n.toolbarsExclude,setting:d,updateSetting:u,tableShape:n.tableShape,defToolbars:b,noUploadImg:o},null),c(zl,{value:n.modelValue,setting:d,markedHeadingId:n.markedHeadingId,noMermaid:r,sanitize:n.sanitize,placeholder:n.placeholder,noKatex:l,scrollAuto:p.scrollAuto,formatCopiedText:n.formatCopiedText,autofocus:n.autoFocus,disabled:n.disabled,readonly:n.readOnly,maxlength:n.maxLength,autoDetectCode:n.autoDetectCode,onChange:v=>{x.emit(t,"saveHistoryPos"),n.onChange?n.onChange(v):(e.emit("update:modelValue",v),e.emit("onChange",v))},onHtmlChanged:v=>{n.onHtmlChanged?n.onHtmlChanged(v):e.emit("onHtmlChanged",v)},onGetCatalog:v=>{n.onGetCatalog?n.onGetCatalog(v):e.emit("onGetCatalog",v)},onBlur:v=>{n.onBlur?n.onBlur(v):e.emit("onBlur",v)},onFocus:v=>{n.onFocus?n.onFocus(v):e.emit("onFocus",v)}},null),!i&&((m=n.footers)==null?void 0:m.length)>0&&c(Dl,{modelValue:n.modelValue,footers:n.footers,defFooters:$,scrollAuto:p.scrollAuto,onScrollAutoChange:v=>p.scrollAuto=v},null),g.value&&!i&&c(Pt,{theme:n.theme,style:{display:f.value?"block":"none"},class:`${h}-catalog-editor`,editorId:t,markedHeadingId:n.markedHeadingId},null)])}}}),Bl=()=>({title:{type:String,default:""},trigger:{type:[String,Object]},onClick:{type:Function}}),Xt=le({name:"NormalToolbar",props:Bl(),emits:["onClick"],setup(n,e){return()=>{const t=$e({props:n,ctx:e},"trigger");return c("div",{class:`${h}-toolbar-item`,title:n.title,onClick:i=>{n.onClick instanceof Function?n.onClick(i):e.emit("onClick",i)}},[t])}}}),Wl=()=>({title:{type:String,default:""},visible:{type:Boolean},trigger:{type:[String,Object]},onChange:{type:Function},overlay:{type:[String,Object]}}),Yt=le({name:"DropdownToolbar",props:Wl(),emits:["onChange"],setup(n,e){const t=W("editorId");return()=>{const i=$e({props:n,ctx:e},"trigger"),l=$e({props:n,ctx:e},"overlay");return c(Je,{relative:`#${t}-toolbar-wrapper`,visible:n.visible,onChange:r=>{n.onChange instanceof Function?n.onChange(r):e.emit("onChange",r)},overlay:l},{default:()=>[c("div",{class:`${h}-toolbar-item`,title:n.title||""},[i])]})}}});function ql(n){return typeof n=="function"||Object.prototype.toString.call(n)==="[object Object]"&&!Jn(n)}const Ul=()=>({title:{type:String,default:""},modalTitle:{type:String,default:""},visible:{type:Boolean},width:{type:String,default:"auto"},height:{type:String,default:"auto"},trigger:{type:[String,Object]},onClick:{type:Function},onClose:{type:Function},showAdjust:{type:Boolean,default:!1},isFullscreen:{type:Boolean,default:!1},onAdjust:{type:Function}}),Jt=le({name:"ModalToolbar",props:Ul(),emits:["onClick","onClose","onAdjust"],setup(n,e){return()=>{const t=$e({props:n,ctx:e},"trigger"),i=$e({props:n,ctx:e},"default");return c(Bt,null,[c("div",{class:`${h}-toolbar-item`,title:n.title,onClick:()=>{n.onClick instanceof Function?n.onClick():e.emit("onClick")}},[t]),c(nn,{width:n.width,height:n.height,title:n.modalTitle,visible:n.visible,onClose:()=>{n.onClose instanceof Function?n.onClose():e.emit("onClose")},showAdjust:n.showAdjust,isFullscreen:n.isFullscreen,onAdjust:l=>{n.onAdjust instanceof Function?n.onAdjust(l):e.emit("onAdjust",l)}},ql(i)?i:{default:()=>[i]})])}}});qe.install=n=>(n.component(qe.name,qe),n.component(Xt.name,Xt),n.component(Yt.name,Yt),n.component(Pt.name,Pt),n.component(Jt.name,Jt),n);qe.NormalToolbar=Xt;qe.DropdownToolbar=Yt;qe.MdCatalog=Pt;qe.ModalToolbar=Jt;qe.config=si;export{qe as W};
