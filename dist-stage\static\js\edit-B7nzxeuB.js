import{_ as ye,u as ge,a as he,r as u,C as ve,w as be,z as we,F as Se,e as d,c as w,i,f as n,t as g,h as l,n as _,o as p,H as j,I as P,j as S,p as H,L as K,M as ke,k as Ce,l as Y,N as qe,K as c,v as Ie,x as Te,E as Ne}from"./index-BJsoK47l.js";import{d as Ve}from"./vuedraggable.umd-eTiRusPO.js";import{a as xe,g as Qe,b as Je,u as Oe,c as De}from"./questionnaire-D6WvR6WF.js";import{g as Le}from"./questions-BTIDeXeU.js";import"./sortable.esm-BzewootV.js";const h=k=>(Ie("data-v-0fddb92e"),k=k(),Te(),k),Me={class:"app-container"},$e={class:"page-header"},ze={class:"page-title"},Be={class:"header-actions"},Ee=h(()=>i("div",{class:"card-header"},[i("span",null,"基本信息")],-1)),Ue=h(()=>i("div",{class:"card-header"},[i("span",null,"题目管理")],-1)),Fe={class:"question-bank"},Ae={class:"question-bank-header"},Re=h(()=>i("h3",null,"题库",-1)),je={class:"question-bank-actions"},Pe={class:"question-list"},He={class:"question-item"},Ke=["onClick"],Ye={class:"question-item-header"},Ge={class:"question-type"},We={class:"question-id"},Xe={class:"question-content"},Ze={class:"selected-questions"},et=h(()=>i("div",{class:"selected-questions-header"},[i("h3",null,"已选试题")],-1)),tt={key:0,class:"empty-state"},st=h(()=>i("p",null,"还没有选择任何试题",-1)),ot=h(()=>i("p",{class:"hint-text"},"从左侧题库中点击题目添加",-1)),nt={class:"selected-question-item"},at={class:"selected-question-content"},lt={class:"selected-question-title"},it={class:"selected-questions-actions"},rt={__name:"edit",setup(k){const M=ge(),T=he(),$=u(null),N=u(JSON.parse(JSON.stringify(!1))),s=ve({id:void 0,title:"",type:void 0,lableId:void 0,classId:1,startTime:"",endTime:"",duration:5,description:"",questions:[],settings:{allowAnonymous:!0,allowModify:!1,showProgress:!0,showStatistics:!0}}),G={title:[{required:!0,message:"请输入问卷名称",trigger:"blur"}],type:[{required:!0,message:"请选择问卷类型",trigger:"change"}],lableId:[{required:!0,message:"请选择问卷类型",trigger:"change"}],startTime:[{required:!0,message:"请选择开始时间",trigger:"change"}],endTime:[{required:!0,message:"请选择结束时间",trigger:"change"}]},z=u(JSON.parse(JSON.stringify([]))),W=async()=>{try{const t=await xe();t&&t.code===200&&t.rows?z.value=t.rows:c.error("获取问卷类型失败")}catch{c.error("获取问卷类型失败")}},X=u(JSON.parse(JSON.stringify([]))),Z=async()=>{try{const t=await Le();X.value=t.map(e=>({value:e.id,label:e.name}))}catch(t){console.error("获取学科分类失败:",t),c.error("获取学科分类失败")}},B=u(JSON.parse(JSON.stringify(""))),ee=u(JSON.parse(JSON.stringify([]))),E=t=>{if(!t)return"";const e=new Date(t),a=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),m=String(e.getDate()).padStart(2,"0"),y=String(e.getHours()).padStart(2,"0"),O=String(e.getMinutes()).padStart(2,"0"),D=String(e.getSeconds()).padStart(2,"0");return`${a}-${r}-${m} ${y}:${O}:${D}`},V={单选题:{type:"success",color:"#67C23A",bgColor:"#f0f9eb"},多选题:{type:"primary",color:"#409EFF",bgColor:"#ecf5ff"},填空题:{type:"warning",color:"#E6A23C",bgColor:"#fdf6ec"},简答题:{type:"info",color:"#909399",bgColor:"#f4f4f5"},不定项:{type:"danger",color:"#F56C6C",bgColor:"#fef0f0"}};function te(t){var e;return((e=V[t])==null?void 0:e.type)||"info"}function v(t){return V[t]||V.简答题}function se(t){const e=ee.value.find(a=>a.value===t);return(e==null?void 0:e.label)||t}const C=u(""),x=u(JSON.parse(JSON.stringify([]))),b=u(!1),f=u(new Set),q=u({pageNum:1,pageSize:10,topicClassification:"",content:""}),Q=async()=>{try{b.value=!0;const t=await Je(q.value),e=Array.from(f.value);x.value=(t.rows||[]).map(a=>{const r={...a,questionsId:String(a.questionsId)},m=e.includes(r.content);return r})}catch{c.error("获取题库列表失败")}finally{b.value=!1}};be([B,C],()=>{q.value.topicClassification=B.value,q.value.content=C.value,Q()},{immediate:!0});const oe=we(()=>x.value);function ne(t){if(f.value.has(t.content)){f.value.delete(t.content);const e=s.questions.findIndex(a=>a.content===t.content);e!==-1&&s.questions.splice(e,1),c.success("移除题目成功")}else{f.value.add(t.content);const e={questionsId:String(t.questionsId),title:t.content,type:t.type,category:t.labelName,content:t.content,options:t.options?JSON.parse(t.options):null,answer:t.answer,analysis:t.analysis,cueword:t.cueword,required:t.required===1};s.questions.push(e),c.success("添加题目成功")}}function J(t){return f.value.has(t.content)}function ae(t){const e=t.content,a=s.questions.findIndex(r=>r.content===e);a!==-1&&(s.questions.splice(a,1),f.value.delete(e),c.success("删除题目成功"))}function le(){if(s.questions.length===0){c.info("暂无已选题目");return}Ne.confirm("确定要清空所有已选题目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{s.questions=[],f.value.clear(),c.success("已清空所有题目")}).catch(()=>{})}function ie(){if(s.questions.length===0){c.info("暂无已选题目");return}const t=[...s.questions];for(let e=t.length-1;e>0;e--){const a=Math.floor(Math.random()*(e+1));[t[e],t[a]]=[t[a],t[e]]}s.questions=t,c.success("题目顺序已随机打乱")}function re(t){q.value.questionType=t,Q()}const ce=async()=>{const t=M.query.questionnaireId;if(t){N.value=!0;try{const e=await Qe(t);if(e.code===200&&e.data){const a=e.data;s.id=a.questionnaireId,s.title=a.title,s.type=a.lableId,s.description=a.description,s.startTime=a.startTime,s.endTime=a.endTime,s.duration=a.duration||5,s.classId=a.classId,a.questionsList&&a.questionsList.length>0&&(s.questions=a.questionsList.map(r=>({questionsId:String(r.questionsId),title:r.content,type:r.type,category:r.labelName,content:r.content,options:r.options?JSON.parse(r.options):null,answer:r.answer,analysis:r.analysis,cueword:r.cueword,required:r.required===1})),f.value=new Set(s.questions.map(r=>r.content)),await Q())}}catch{c.error("获取问卷详情失败")}}},de=async()=>{try{await $.value.validate();const t={questionnaireId:s.id,title:s.title,lableId:s.type,description:s.description,classId:s.classId,startTime:E(s.startTime),endTime:E(s.endTime),duration:s.duration,questions:s.questions.map(e=>({questionsId:e.questionsId,content:e.content,image:null,cueword:e.cueword||null,type:e.type,labelId:2,points:1,options:e.options?JSON.stringify(e.options):null,answer:e.answer,analysis:e.analysis,randomly:0,required:e.required?1:0}))};if(N.value){const e=await Oe(t);e.code===200?(c.success("修改成功"),T.push("/aqsystem/questionnaires/questionnaires")):c.error(e.msg||"保存失败")}else{const e=await De(t);e.code===200?(c.success("新增成功"),T.push("/aqsystem/questionnaires/questionnaires")):c.error(e.msg||"保存失败")}}catch(t){c.error(t.message||"保存失败")}},ue=()=>{T.push("/aqsystem/questionnaires")};return Se(()=>{Z(),W(),M.query.questionnaireId&&ce()}),(t,e)=>{const a=d("el-button"),r=d("el-input"),m=d("el-form-item"),y=d("el-col"),O=d("el-option"),D=d("el-select"),L=d("el-row"),U=d("el-date-picker"),pe=d("el-form"),F=d("el-card"),me=d("el-empty"),A=d("el-skeleton-item"),_e=d("el-skeleton"),I=d("el-tag"),fe=d("el-icon");return p(),w("div",Me,[i("div",$e,[i("h1",ze,g(N.value?"编辑问卷":"创建问卷"),1),i("div",Be,[n(a,{type:"primary",icon:"Check",onClick:de},{default:l(()=>[_("保存")]),_:1}),n(a,{icon:"Close",onClick:ue},{default:l(()=>[_("取消")]),_:1})])]),n(F,{class:"box-card",shadow:"hover"},{header:l(()=>[Ee]),default:l(()=>[n(pe,{ref_key:"formRef",ref:$,model:s,rules:G,"label-width":"100px"},{default:l(()=>[n(L,{gutter:20},{default:l(()=>[n(y,{span:12},{default:l(()=>[n(m,{label:"问卷名称",prop:"title"},{default:l(()=>[n(r,{modelValue:s.title,"onUpdate:modelValue":e[0]||(e[0]=o=>s.title=o),placeholder:"请输入问卷名称"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{span:12},{default:l(()=>[n(m,{label:"问卷类型",prop:"type"},{default:l(()=>[n(D,{modelValue:s.type,"onUpdate:modelValue":e[1]||(e[1]=o=>s.type=o),placeholder:"请选择问卷类型",class:"full-width"},{default:l(()=>[(p(!0),w(j,null,P(z.value,o=>(p(),S(O,{key:o.labelId,label:o.labelName,value:o.labelId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(L,{gutter:20},{default:l(()=>[n(y,{span:12},{default:l(()=>[n(m,{label:"开始时间",prop:"startTime"},{default:l(()=>[n(U,{modelValue:s.startTime,"onUpdate:modelValue":e[2]||(e[2]=o=>s.startTime=o),type:"datetime",placeholder:"选择开始时间",class:"full-width"},null,8,["modelValue"])]),_:1})]),_:1}),n(y,{span:12},{default:l(()=>[n(m,{label:"结束时间",prop:"endTime"},{default:l(()=>[n(U,{modelValue:s.endTime,"onUpdate:modelValue":e[3]||(e[3]=o=>s.endTime=o),type:"datetime",placeholder:"选择结束时间",class:"full-width"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(m,{label:"问卷说明",prop:"description"},{default:l(()=>[n(r,{modelValue:s.description,"onUpdate:modelValue":e[4]||(e[4]=o=>s.description=o),type:"textarea",rows:4,placeholder:"请输入问卷说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),n(F,{class:"box-card",shadow:"hover"},{header:l(()=>[Ue]),default:l(()=>[n(L,{gutter:20},{default:l(()=>[n(y,{span:16},{default:l(()=>[i("div",Fe,[i("div",Ae,[Re,i("div",je,[n(r,{modelValue:C.value,"onUpdate:modelValue":e[5]||(e[5]=o=>C.value=o),placeholder:"搜索题目...","prefix-icon":"Search",clearable:"",class:"search-input"},null,8,["modelValue"])])]),i("div",Pe,[!b.value&&x.value.length===0?(p(),S(me,{key:0,description:"暂无题目"})):b.value?(p(),S(_e,{key:1,loading:b.value,animated:"",count:3},{template:l(()=>[i("div",He,[n(A,{variant:"text",style:{width:"30%"}}),n(A,{variant:"text",style:{width:"100%"}})])]),_:1},8,["loading"])):(p(!0),w(j,{key:2},P(oe.value,o=>(p(),w("div",{key:o.questionsId,class:ke(["question-item",{selected:J(o)}]),style:K({borderLeft:`4px solid ${v(o.type).color}`,backgroundColor:J(o)?v(o.type).bgColor:"white"}),onClick:H(R=>ne(o),["stop"])},[i("div",Ye,[i("div",Ge,[n(I,{type:te(o.type),size:"small",class:"clickable-tag",style:K({backgroundColor:v(o.type).bgColor,color:v(o.type).color,borderColor:v(o.type).color}),onClick:H(R=>re(o.type),["stop"])},{default:l(()=>[_(g(o.type),1)]),_:2},1032,["type","style","onClick"]),n(I,{size:"small",type:"info",class:"topic-tag"},{default:l(()=>[_(g(o.labelName),1)]),_:2},1024),i("span",We,"ID: "+g(o.questionsId),1)])]),i("div",Xe,[_(g(o.content)+" ",1),J(o)?(p(),S(I,{key:0,type:"success",size:"small",class:"selected-tag"},{default:l(()=>[_("已选择")]),_:1})):Ce("",!0)])],14,Ke))),128))])])]),_:1}),n(y,{span:8},{default:l(()=>[i("div",Ze,[et,s.questions.length===0?(p(),w("div",tt,[n(fe,{size:48},{default:l(()=>[n(Y(qe))]),_:1}),st,ot])):(p(),S(Y(Ve),{key:1,modelValue:s.questions,"onUpdate:modelValue":e[6]||(e[6]=o=>s.questions=o),"item-key":"questionsId",class:"selected-questions-list"},{item:l(({element:o})=>[i("div",nt,[i("div",at,[n(I,{size:"small",class:"question-type-tag"},{default:l(()=>[_(g(se(o.type)),1)]),_:2},1024),n(a,{type:"danger",link:"",icon:"Delete",class:"delete-btn",onClick:R=>ae(o)},null,8,["onClick"])]),i("div",lt,g(o.content),1)])]),_:1},8,["modelValue"])),i("div",it,[n(a,{type:"danger",plain:"",onClick:le},{default:l(()=>[_(" 清空 ")]),_:1}),n(a,{type:"primary",plain:"",onClick:ie},{default:l(()=>[_(" 随机排序 ")]),_:1})])])]),_:1})]),_:1})]),_:1})])}}},_t=ye(rt,[["__scopeId","data-v-0fddb92e"]]);export{_t as default};
