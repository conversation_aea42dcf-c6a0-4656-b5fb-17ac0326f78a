<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">问卷管理4</h1>
      <el-button type="primary" icon="Plus" class="create-btn" @click="handleCreate">创建问卷</el-button>
    </div>

    <!-- 搜索和筛选区域 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <div class="form-row">
          <el-form-item label="问卷名称">
            <el-input
              v-model="queryParams.title"
              placeholder="搜索问卷名称..."
              clearable
              prefix-icon="Search"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="问卷类型">
            <el-select v-model="queryParams.labelId" placeholder="所有类型" clearable class="filter-select">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="问卷状态">
            <el-select v-model="queryParams.status" placeholder="所有状态" clearable class="filter-select">
              <el-option
                v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item class="search-buttons">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 问卷列表 -->
    <el-card class="box-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="questionnaireList"
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column prop="title" label="问卷名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="description" label="问卷说明" min-width="200" show-overflow-tooltip />
        <el-table-column prop="lableName" label="问卷类型" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.lableName === '收集问卷' ? 'success' : 'warning'">
              {{ scope.row.lableName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180" align="center" />
        <el-table-column prop="endTime" label="结束时间" width="180" align="center" />
        <el-table-column label="操作" width="220" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >编辑</el-button>
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 添加或修改问卷对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body class="questionnaire-dialog">
      <el-form ref="questionnaireFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="问卷名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入问卷名称" />
        </el-form-item>
        <el-form-item label="问卷类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择问卷类型" class="full-width">
            <el-option
              v-for="dict in typeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="问卷时间" prop="timeRange">
          <el-date-picker
            v-model="form.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="full-width"
          />
        </el-form-item>
        <el-form-item label="问卷说明" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入问卷说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 复制问卷对话框 -->
    <el-dialog title="复制问卷" v-model="copyOpen" width="500px" append-to-body class="questionnaire-dialog">
      <el-form ref="copyFormRef" :model="copyForm" :rules="copyRules" label-width="100px">
        <el-form-item label="新问卷名称" prop="newTitle">
          <el-input v-model="copyForm.newTitle" placeholder="请输入新问卷名称" />
        </el-form-item>
        <el-form-item label="复制题目">
          <el-checkbox v-model="copyForm.copyQuestions">复制原问卷的所有题目</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCopy">取 消</el-button>
          <el-button type="primary" @click="submitCopyForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { listQuestionnaire,deleteQuestionnaire,listLabel } from '@/api/aqsystem/questionnaire' 

const router = useRouter()

// 遮罩层
const loading = ref(false)
// 总条数
const total = ref(0)
// 问卷表格数据
const questionnaireList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 是否显示复制弹出层
const copyOpen = ref(false)
// 表单参数
const form = ref({})
// 复制表单参数
const copyForm = ref({
  newTitle: '',
  copyQuestions: true,
  originalId: null
})
// 表单校验
const rules = ref({
  title: [
    { required: true, message: "问卷名称不能为空", trigger: "blur" }
  ],
  type: [
    { required: true, message: "请选择问卷类型", trigger: "change" }
  ],
  timeRange: [
    { required: true, message: "请选择问卷时间", trigger: "change" }
  ]
})
// 复制表单校验
const copyRules = ref({
  newTitle: [
    { required: true, message: "新问卷名称不能为空", trigger: "blur" }
  ]
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  status: undefined,
  labelId: undefined
})

// 类型选项
const typeOptions = ref([])

// 状态选项
const statusOptions = [
  { value: "进行中", label: "进行中" },
  { value: "未开始", label: "未开始" },
  { value: "已结束", label: "已结束" }
]

/** 获取状态样式 */
function getStatusClass(status) {
  switch (status) {
    case 'active':
      return 'status-active'
    case 'draft':
      return 'status-draft'
    case 'closed':
      return 'status-closed'
    default:
      return ''
  }
}

/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case '进行中':
      return 'success'
    case '未开始':
      return 'info'
    case '已结束':
      return 'danger'
    default:
      return ''
  }
}

/** 获取问卷类型列表 */
function getTypeOptions() {
  listLabel().then(response => {
    if (response.code === 200) {
      typeOptions.value = response.rows.map(item => ({
        value: item.labelId,
        label: item.labelName
      }))
    } else {
      ElMessage.error(response.msg || '获取问卷类型失败')
    }
  }).catch(error => {
    console.error('获取问卷类型失败:', error)
    ElMessage.error('获取问卷类型失败')
  })
}

/** 查询问卷列表 */
function getList() {
  loading.value = true
  listQuestionnaire(queryParams).then(response => {
    questionnaireList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.title = undefined
  queryParams.labelId = undefined
  queryParams.status = undefined
  queryParams.classId = undefined
  handleQuery()
}

/** 创建问卷 */
const handleCreate = () => {
  router.push('/aqsystem/questionnaires/edit')
}

/** 修改按钮操作 */
function handleUpdate(row) {
  router.push({
    path: '/aqsystem/questionnaires/edit',
    query: { questionnaireId: row.questionnaireId }
  })
}

/** 复制按钮操作 */
function handleCopy(row) {
  if (row.type !== 'collection') {
    ElMessage.warning('只有收集问卷可以复制！')
    return
  }
  copyForm.value = {
    newTitle: row.title + ' (副本)',
    copyQuestions: true,
    originalId: row.id
  }
  copyOpen.value = true
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: '/aqsystem/questionnaires/view',
    query: {
      questionnaireId: row.questionnaireId
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  ElMessageBox.confirm('是否确认删除问卷名称为"' + row.title + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true
    deleteQuestionnaire(row.questionnaireId).then(response => {
      if (response.code === 200) {
        ElMessage.success("删除成功")
        getList() // 重新加载列表
      } else {
        ElMessage.error(response.msg || "删除失败")
      }
    }).catch(error => {
      console.error('删除问卷失败:', error)
      ElMessage.error("删除失败")
    }).finally(() => {
      loading.value = false
    })
  }).catch(() => {
    // 用户取消删除操作
  })
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    title: undefined,
    type: undefined,
    description: undefined,
    timeRange: undefined
  }
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 取消复制按钮 */
function cancelCopy() {
  copyOpen.value = false
  copyForm.value = {
    newTitle: '',
    copyQuestions: true,
    originalId: null
  }
}

/** 提交按钮 */
function submitForm() {
  // 模拟保存操作
  ElMessage.success("保存成功")
  open.value = false
  getList()
}

/** 提交复制表单 */
function submitCopyForm() {
  // 模拟复制操作
  const originalQuestionnaire = questionnaireList.value.find(item => item.id === copyForm.value.originalId)
  if (originalQuestionnaire) {
    const newQuestionnaire = {
      ...originalQuestionnaire,
      id: questionnaireList.value.length + 1,
      title: copyForm.value.newTitle,
      createTime: new Date().toISOString().split('T')[0],
      participantCount: 0
    }
    questionnaireList.value.push(newQuestionnaire)
  }
  ElMessage.success("复制成功")
  copyOpen.value = false
  getList()
}

/** 分页大小改变 */
function handleSizeChange(val) {
  queryParams.pageSize = val
  getList()
}

/** 页码改变 */
function handleCurrentChange(val) {
  queryParams.pageNum = val
  getList()
}

onMounted(() => {
  getList()
  getTypeOptions()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.create-btn {
  padding: 10px 20px;
  font-weight: 500;
}

.filter-container {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.form-row .el-form-item {
  margin-bottom: 0;
  flex: 1;
}

.filter-select {
  width: 100%;
}

.search-buttons {
  margin-left: auto;
  margin-right: auto;
}

.box-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.questionnaire-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
}

.full-width {
  width: 100%;
}

.dialog-footer {
  text-align: right;
  padding-top: 10px;
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-buttons {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .filter-select {
    width: 100%;
  }
}
</style>
