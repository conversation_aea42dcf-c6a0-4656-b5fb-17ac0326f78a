<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入技能名称/熟练度/描述"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:skill:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['system:skill:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:skill:remove']">删除</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:skill:export']">导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mergedSkillList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="技能ID" align="center" prop="skiId" /> -->
      <el-table-column label="技能名称" align="center" prop="name" width="160" />
      <el-table-column label="熟练度" align="center" prop="proficiency"  width="160"/>
      <el-table-column label="技能描述" align="center" prop="description" width="300"/>
      <el-table-column label="详细说明" align="center" width="300">
        <template #default="scope">
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 400px; white-space: pre-wrap;">
                <h4>项目内容详情</h4>
                <div>{{ scope.row.textList.map((item, index) => `${index + 1}. ${item}`).join('\n') }}</div>
              </div>
            </template>
            <div class="text-ellipsis">{{ scope.row.textList[0] || '暂无详细说明' }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:skill:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:skill:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改技能对话框 -->
    <el-dialog :title="title" v-model="open" width="1080px" append-to-body>
      <el-form ref="skillRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="技能名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入技能名称" />
        </el-form-item>
        <el-form-item label="熟练度" prop="proficiency">
          <el-select v-model="form.proficiency" placeholder="请选择熟练度">
            <el-option label="精通" value="精通" />
            <el-option label="熟练" value="熟练" />
            <el-option label="良好" value="良好" />
          </el-select>
        </el-form-item>
        <el-form-item label="技能描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入技能描述" />
        </el-form-item>
        <el-form-item label="详细说明" prop="text">
          <div class="detail-container">
            
            <div class="detail-input" style="margin-top: 20px;">
              <MdEditor
                v-model="currentText"
                language="zh-CN"
                preview-theme="default"
                code-theme="github"
                style="height: 200px"
              />
              <el-button type="primary" @click="handleAddText" style="margin-top: 10px">添加说明</el-button>
            </div>
          </div>
          <div class="detail-list" v-if="tempSegments.length > 0">
              <h4>已添加的内容：</h4>
              <div v-for="(item, index) in tempSegments" :key="index" class="detail-item">
                <div class="detail-index">{{ index + 1 }}.</div>
                <div class="detail-content">
                  <div :style="{ fontWeight: item.isBold ? 'bold' : 'normal' }">{{item.text}}</div>
                </div>
                <div class="detail-actions">
                  <el-button link type="primary" @click="handleEditText(item, index)">编辑</el-button>
                  <el-button link type="danger" @click="handleRemoveText(index)">删除</el-button>
                </div>
              </div>
            </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Skill">
import MdEditor from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import { listSkill, getSkill, delSkill, addSkill, updateSkill } from "@/api/interview/skill"

const { proxy } = getCurrentInstance()

const skillList = ref([])
const mergedSkillList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(JSON.parse(JSON.stringify(true)))
const multiple = ref(JSON.parse(JSON.stringify(true)))
const total = ref(JSON.parse(JSON.stringify(0)))
const title = ref(JSON.parse(JSON.stringify("")))
const currentText = ref(JSON.parse(JSON.stringify('')))

const data = reactive({
  form: {},
  tempSegments: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: null
  },
  rules: {
    name: [
      { required: true, message: "技能名称不能为空", trigger: "blur" }
    ],
    proficiency: [
      { required: true, message: "熟练度不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "技能描述不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules, tempSegments } = toRefs(data)

/** 查询技能列表 */
function getList() {
  loading.value = true
  listSkill(queryParams.value).then(response => {
    skillList.value = response.data.records
    mergedSkillList.value = skillList.value.map(item => ({
      ...item,
      textList: item.segments ? item.segments.map(segment => segment.text) : []
    }))
    total.value = response.data.total || response.data.records.length
    queryParams.value.pageNum = response.data.current
    queryParams.value.pageSize = response.data.size
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    skiId: null,
    name: null,
    proficiency: null,
    description: null,
    segments: []
  }
  tempSegments.value = []
  currentText.value = ''
  proxy.resetForm("skillRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.skiId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加技能"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _skiId = row.skiId || ids.value
  getSkill(_skiId).then(response => {
    if (response.code === 0) {
      // 设置基本表单数据
      form.value = {
        skiId: response.data.skiId,
        name: response.data.name,
        proficiency: response.data.proficiency,
        description: response.data.description
      }
      
      // 设置详细说明数据
      if (response.data.segments && response.data.segments.length > 0) {
        tempSegments.value = response.data.segments.map(item => ({
          text: item.text || '',
          segmentOrder: item.segmentOrder || 0,
          isBold: item.isBold || false
        }))
      }
      
      open.value = true
      title.value = "修改技能"
    } else {
      proxy.$modal.msgError(response.message || '获取技能信息失败')
    }
  }).catch(error => {
    console.error('获取技能信息错误:', error)
    proxy.$modal.msgError('获取技能信息失败')
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["skillRef"].validate(valid => {
    if (valid) {
      form.value.segments = [...tempSegments.value]
      
      if (form.value.skiId != null) {
        updateSkill(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addSkill(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _skiIds = row.skiId || ids.value
  proxy.$modal.confirm('是否确认删除技能编号为"' + _skiIds + '"的数据项？').then(() => {
    return delSkill(_skiIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/skill/export', {
    ...queryParams.value
  }, `skill_${new Date().getTime()}.xlsx`)
}

// 添加详细说明
function handleAddText() {
  if (!currentText.value.trim()) {
    proxy.$modal.msgError("详细说明不能为空")
    return
  }
  
  tempSegments.value.push({
    text: currentText.value,
    isBold: false,
    segmentOrder: tempSegments.value.length + 1
  })
  
  currentText.value = ''
}

// 删除详细说明
function handleRemoveText(index) {
  proxy.$modal.confirm('是否确认删除该条详细说明？').then(() => {
    tempSegments.value.splice(index, 1)
    // 重新排序
    tempSegments.value.forEach((item, idx) => {
      item.segmentOrder = idx + 1
    })
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

// 编辑详细说明
function handleEditText(item, index) {
  currentText.value = item.text
  handleRemoveText(index) // 删除原有的，编辑后会作为新的添加
}

getList()
</script>

<style scoped>
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.detail-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-input {
  width: 100%;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.detail-index {
  margin-right: 15px;
  font-weight: bold;
  color: #409EFF;
}

.detail-content {
  flex: 1;
  margin-right: 10px;
}

.detail-actions {
  display: flex;
  gap: 15px;
}
</style>
