import{_ as A,z as f,c as t,o as i,i as e,k as r,t as l,n as y,H as k,I as T,v as R,x as P}from"./index-BJsoK47l.js";const d=o=>(R("data-v-80047352"),o=o(),P(),o),O={class:"resume-template template-5"},w={class:"resume-container"},D={class:"header"},$={class:"header-info"},x={class:"name"},F={class:"contact-info"},V={key:0,class:"info-item"},B=d(()=>e("i",{class:"icon"},"📞",-1)),X={key:1,class:"info-item"},J=d(()=>e("i",{class:"icon"},"📧",-1)),U={key:2,class:"info-item"},W=d(()=>e("i",{class:"icon"},"🏠",-1)),q={key:3,class:"info-item"},z=d(()=>e("i",{class:"icon"},"🎂",-1)),K={key:0,class:"avatar-container"},Y=["src"],G={key:0,class:"section education-section"},Q=d(()=>e("div",{class:"section-title"},[e("h2",null,"教育背景 / EDUCATIONAL EXPERIENCE")],-1)),Z={class:"section-content"},ee={class:"item-header"},se={class:"item-header-left"},te={class:"edu-school"},ie={class:"edu-major"},oe={class:"item-time"},ne={key:0,class:"edu-courses"},ce=["innerHTML"],re={key:1,class:"section work-section"},le=d(()=>e("div",{class:"section-title"},[e("h2",null,"实习经历 / INTERNSHIP EXPERIENCE")],-1)),ae={class:"section-content work-content"},de={class:"item-header"},ue={class:"item-header-left"},me={class:"work-time"},he={class:"work-company"},_e={class:"work-position"},ve={key:0,class:"work-description"},fe=["innerHTML"],ye={key:2,class:"section projects-section"},ke=d(()=>e("div",{class:"section-title"},[e("h2",null,"项目经历 / PROJECT EXPERIENCE")],-1)),Te={class:"section-content"},Ee={class:"item-header"},pe={class:"item-header-left"},Le={class:"project-time"},ge={key:0,class:"project-role"},He={class:"project-title"},Me={key:0,class:"project-description"},be=["innerHTML"],je={key:3,class:"section practices-section"},Ie=d(()=>e("div",{class:"section-title"},[e("h2",null,"练手项目 / PRACTICE PROJECT")],-1)),Ne={class:"section-content"},Se={class:"item-header"},Ce={class:"item-header-left"},Ae={class:"project-time"},Re={key:0,class:"project-role"},Pe={class:"project-title"},Oe={key:0,class:"project-description"},we=["innerHTML"],De={key:4,class:"section skills-section"},$e=d(()=>e("div",{class:"section-title"},[e("h2",null,"专业技能 / PROFESSIONAL SKILLS")],-1)),xe={class:"section-content"},Fe=["innerHTML"],Ve={key:5,class:"section certificates-section"},Be=d(()=>e("div",{class:"section-title"},[e("h2",null,"证书奖项 / CERTIFICATES AND AWARDS")],-1)),Xe={class:"section-content"},Je=["innerHTML"],Ue=["innerHTML"],We={key:6,class:"section interests-section"},qe=d(()=>e("div",{class:"section-title"},[e("h2",null,"兴趣爱好 / INTERESTS")],-1)),ze={class:"section-content"},Ke=["innerHTML"],Ye=["innerHTML"],Ge={key:7,class:"section evaluation-section"},Qe=d(()=>e("div",{class:"section-title"},[e("h2",null,"个人总结 / SELF EVALUATION")],-1)),Ze={class:"section-content"},es=["innerHTML"],ss=["innerHTML"],ts={__name:"Template5",props:{resume:{type:Object,required:!0}},setup(o){const a=o,_=s=>{if(!s)return"";try{const n=new Date(s);if(isNaN(n.getTime())){const v=s.match(/^(\d{4})[-\.\/年]?(\d{1,2})/);return v&&v[1]&&v[2]?`${v[1]}.${v[2].padStart(2,"0")}`:s}const m=n.getFullYear(),E=String(n.getMonth()+1).padStart(2,"0");return`${m}.${E}`}catch(n){return console.error("Error formatting date:",n),s}},H=f(()=>{var s,n;return((n=(s=a.resume.modules)==null?void 0:s.education)==null?void 0:n.length)>0}),M=f(()=>{var s,n;return((n=(s=a.resume.modules)==null?void 0:s.work)==null?void 0:n.length)>0}),b=f(()=>{var s,n;return((n=(s=a.resume.modules)==null?void 0:s.projects)==null?void 0:n.length)>0}),j=f(()=>{var n;const s=(n=a.resume.modules)==null?void 0:n.skills;return!s||!Array.isArray(s)?!1:s.some(m=>m&&typeof m=="object"&&m.description&&m.description.trim()!=="")}),I=f(()=>{var s;return(s=a.resume.modules)!=null&&s.certificates?typeof a.resume.modules.certificates=="string"?a.resume.modules.certificates.trim()!=="":typeof a.resume.modules.certificates=="object"&&a.resume.modules.certificates.certificateName&&a.resume.modules.certificates.certificateName.trim()!=="":!1}),N=f(()=>{var n;if(!((n=a.resume.modules)!=null&&n.selfEvaluation))return!1;const s=a.resume.modules.selfEvaluation;return typeof s=="string"?s.trim()!=="":typeof s=="object"&&s.description&&s.description.trim()!==""}),S=f(()=>{var s,n;return((n=(s=a.resume.modules)==null?void 0:s.practices)==null?void 0:n.length)>0}),C=f(()=>{var s;return(s=a.resume.modules)!=null&&s.interests?typeof a.resume.modules.interests=="string"?a.resume.modules.interests.trim()!=="":typeof a.resume.modules.interests=="object"&&a.resume.modules.interests.description&&a.resume.modules.interests.description.trim()!=="":!1}),u=s=>{if(!s||typeof s!="string")return"";let n=s;return n=n.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),n=n.replace(/\*(.*?)\*/g,"<em>$1</em>"),n=n.replace(/\n/g,"<br/>"),n};return(s,n)=>{var m,E,v,p,L,g;return i(),t("div",O,[e("div",w,[e("div",D,[e("div",$,[e("div",x,l(((m=o.resume.modules.basic)==null?void 0:m.name)||"未填写姓名"),1),e("div",F,[(E=o.resume.modules.basic)!=null&&E.phone?(i(),t("span",V,[B,y(" "+l(o.resume.modules.basic.phone),1)])):r("",!0),(v=o.resume.modules.basic)!=null&&v.email?(i(),t("span",X,[J,y(" "+l(o.resume.modules.basic.email),1)])):r("",!0),(p=o.resume.modules.basic)!=null&&p.address?(i(),t("span",U,[W,y(" "+l(o.resume.modules.basic.address),1)])):r("",!0),(L=o.resume.modules.basic)!=null&&L.age?(i(),t("span",q,[z,y(" 年龄："+l(o.resume.modules.basic.age)+"岁",1)])):r("",!0)])]),(g=o.resume.modules.basic)!=null&&g.avatar?(i(),t("div",K,[e("img",{class:"avatar",src:o.resume.modules.basic.avatar,alt:"头像"},null,8,Y)])):r("",!0)]),H.value?(i(),t("div",G,[Q,e("div",Z,[(i(!0),t(k,null,T(o.resume.modules.education,(c,h)=>(i(),t("div",{key:h,class:"education-item"},[e("div",ee,[e("div",se,[e("span",te,l(c.school)+"（"+l(c.degree)+"）",1),e("span",ie,l(c.major),1),e("span",oe,l(_(c.time[0]))+" - "+l(_(c.time[1])||"至今"),1)])]),c.courses?(i(),t("div",ne,[y(" 主修课程： "),e("div",{innerHTML:u(c.courses)},null,8,ce)])):r("",!0)]))),128))])])):r("",!0),M.value?(i(),t("div",re,[le,e("div",ae,[(i(!0),t(k,null,T(o.resume.modules.work,(c,h)=>(i(),t("div",{key:h,class:"work-item"},[e("div",de,[e("div",ue,[e("span",me,l(_(c.time[0]))+" - "+l(_(c.time[1])||"至今"),1),e("span",he,l(c.company),1),e("span",_e,l(c.position),1)])]),c.description?(i(),t("div",ve,[y(" 工作职责： "),e("div",{innerHTML:u(c.description)},null,8,fe)])):r("",!0)]))),128))])])):r("",!0),b.value?(i(),t("div",ye,[ke,e("div",Te,[(i(!0),t(k,null,T(o.resume.modules.projects,(c,h)=>(i(),t("div",{key:h,class:"project-item"},[e("div",Ee,[e("div",pe,[e("span",Le,l(_(c.time[0]))+" - "+l(_(c.time[1])||"至今"),1),c.role?(i(),t("span",ge,l(c.role),1)):r("",!0),e("span",He,l(c.name),1)])]),c.description?(i(),t("div",Me,[y(" 项目描述： "),e("div",{innerHTML:u(c.description)},null,8,be)])):r("",!0)]))),128))])])):r("",!0),S.value?(i(),t("div",je,[Ie,e("div",Ne,[(i(!0),t(k,null,T(o.resume.modules.practices,(c,h)=>(i(),t("div",{key:h,class:"project-item"},[e("div",Se,[e("div",Ce,[e("span",Ae,l(_(c.time[0]))+" - "+l(_(c.time[1])||"至今"),1),c.role?(i(),t("span",Re,l(c.role),1)):r("",!0),e("span",Pe,l(c.name),1)])]),c.description?(i(),t("div",Oe,[y(" 项目描述： "),e("div",{innerHTML:u(c.description)},null,8,we)])):r("",!0)]))),128))])])):r("",!0),j.value?(i(),t("div",De,[$e,e("div",xe,[(i(!0),t(k,null,T(o.resume.modules.skills,(c,h)=>(i(),t("div",{key:h,class:"skill-item-description"},[e("div",{class:"skill-description-body",innerHTML:u(c.description)},null,8,Fe)]))),128))])])):r("",!0),I.value?(i(),t("div",Ve,[Be,e("div",Xe,[typeof o.resume.modules.certificates=="string"?(i(),t("div",{key:0,innerHTML:u(o.resume.modules.certificates)},null,8,Je)):typeof o.resume.modules.certificates=="object"?(i(),t("div",{key:1,innerHTML:u(o.resume.modules.certificates.certificateName||"")},null,8,Ue)):r("",!0)])])):r("",!0),C.value?(i(),t("div",We,[qe,e("div",ze,[typeof o.resume.modules.interests=="string"?(i(),t("div",{key:0,innerHTML:u(o.resume.modules.interests)},null,8,Ke)):typeof o.resume.modules.interests=="object"?(i(),t("div",{key:1,innerHTML:u(o.resume.modules.interests.description||"")},null,8,Ye)):r("",!0)])])):r("",!0),N.value?(i(),t("div",Ge,[Qe,e("div",Ze,[typeof o.resume.modules.selfEvaluation=="string"?(i(),t("div",{key:0,innerHTML:u(o.resume.modules.selfEvaluation)},null,8,es)):typeof o.resume.modules.selfEvaluation=="object"?(i(),t("div",{key:1,innerHTML:u(o.resume.modules.selfEvaluation.description||"")},null,8,ss)):r("",!0)])])):r("",!0)])])}}},os=A(ts,[["__scopeId","data-v-80047352"]]);export{os as default};
