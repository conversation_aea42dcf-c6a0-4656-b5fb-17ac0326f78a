import{Z as C,_ as _e,B as ge,d as ve,r as p,C as he,a5 as ke,e as r,G as Q,c as N,o as S,J as x,f as e,a4 as W,l as n,h as l,m as ye,n as g,j as $,D,i as c,t as I,k as Se,H as be,I as we,L as xe,v as Ce,x as Ve}from"./index-BJsoK47l.js";import{W as Ne}from"./style-CI2cvTa4.js";function $e(s){return C({url:"/skill/page/list",method:"get",params:s})}function Ie(s){return C({url:"/skill/"+s,method:"get"})}function Oe(s){return C({url:"/skill/add",method:"post",data:s})}function Je(s){return C({url:`/skill/${s.skiId}`,method:"put",data:s})}function Ue(s){return C({url:"/skill/"+s,method:"delete"})}const A=s=>(Ce("data-v-4eaeaacc"),s=s(),Ve(),s),Be={class:"app-container"},Re={style:{"max-width":"400px","white-space":"pre-wrap"}},Le=A(()=>c("h4",null,"项目内容详情",-1)),qe={class:"text-ellipsis"},ze={class:"detail-container"},Ee={class:"detail-input",style:{"margin-top":"20px"}},De={key:0,class:"detail-list"},Pe=A(()=>c("h4",null,"已添加的内容：",-1)),Te={class:"detail-index"},Fe={class:"detail-content"},je={class:"detail-actions"},Qe={class:"dialog-footer"},We=ge({name:"Skill"}),Ae=Object.assign(We,{setup(s){const{proxy:u}=ve(),P=p([]),T=p([]),h=p(!1),O=p(!0),V=p(!0),J=p([]),K=p(JSON.parse(JSON.stringify(!0))),G=p(JSON.parse(JSON.stringify(!0))),U=p(JSON.parse(JSON.stringify(0))),B=p(JSON.parse(JSON.stringify(""))),k=p(JSON.parse(JSON.stringify(""))),H=he({form:{},tempSegments:[],queryParams:{pageNum:1,pageSize:10,keyword:null},rules:{name:[{required:!0,message:"技能名称不能为空",trigger:"blur"}],proficiency:[{required:!0,message:"熟练度不能为空",trigger:"blur"}],description:[{required:!0,message:"技能描述不能为空",trigger:"blur"}]}}),{queryParams:m,form:d,rules:Z,tempSegments:v}=ke(H);function b(){O.value=!0,$e(m.value).then(o=>{P.value=o.data.records,T.value=P.value.map(t=>({...t,textList:t.segments?t.segments.map(i=>i.text):[]})),U.value=o.data.total||o.data.records.length,m.value.pageNum=o.data.current,m.value.pageSize=o.data.size,O.value=!1})}function M(){h.value=!1,R()}function R(){d.value={skiId:null,name:null,proficiency:null,description:null,segments:[]},v.value=[],k.value="",u.resetForm("skillRef")}function L(){m.value.pageNum=1,b()}function X(){u.resetForm("queryRef"),L()}function Y(o){J.value=o.map(t=>t.skiId),K.value=o.length!=1,G.value=!o.length}function ee(){R(),h.value=!0,B.value="添加技能"}function te(o){R();const t=o.skiId||J.value;Ie(t).then(i=>{i.code===0?(d.value={skiId:i.data.skiId,name:i.data.name,proficiency:i.data.proficiency,description:i.data.description},i.data.segments&&i.data.segments.length>0&&(v.value=i.data.segments.map(f=>({text:f.text||"",segmentOrder:f.segmentOrder||0,isBold:f.isBold||!1}))),h.value=!0,B.value="修改技能"):u.$modal.msgError(i.message||"获取技能信息失败")}).catch(i=>{console.error("获取技能信息错误:",i),u.$modal.msgError("获取技能信息失败")})}function le(){u.$refs.skillRef.validate(o=>{o&&(d.value.segments=[...v.value],d.value.skiId!=null?Je(d.value).then(()=>{u.$modal.msgSuccess("修改成功"),h.value=!1,b()}):Oe(d.value).then(()=>{u.$modal.msgSuccess("新增成功"),h.value=!1,b()}))})}function ae(o){const t=o.skiId||J.value;u.$modal.confirm('是否确认删除技能编号为"'+t+'"的数据项？').then(()=>Ue(t)).then(()=>{b(),u.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ne(){if(!k.value.trim()){u.$modal.msgError("详细说明不能为空");return}v.value.push({text:k.value,isBold:!1,segmentOrder:v.value.length+1}),k.value=""}function F(o){u.$modal.confirm("是否确认删除该条详细说明？").then(()=>{v.value.splice(o,1),v.value.forEach((t,i)=>{t.segmentOrder=i+1}),u.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(o,t){k.value=o.text,F(t)}return b(),(o,t)=>{const i=r("el-input"),f=r("el-form-item"),_=r("el-button"),j=r("el-form"),ie=r("el-col"),se=r("right-toolbar"),re=r("el-row"),w=r("el-table-column"),de=r("el-tooltip"),ue=r("el-table"),ce=r("pagination"),q=r("el-option"),me=r("el-select"),pe=r("el-dialog"),z=Q("hasPermi"),fe=Q("loading");return S(),N("div",Be,[x(e(j,{model:n(m),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(f,{label:"关键词",prop:"keyword"},{default:l(()=>[e(i,{modelValue:n(m).keyword,"onUpdate:modelValue":t[0]||(t[0]=a=>n(m).keyword=a),placeholder:"请输入技能名称/熟练度/描述",clearable:"",onKeyup:ye(L,["enter"])},null,8,["modelValue"])]),_:1}),e(f,null,{default:l(()=>[e(_,{type:"primary",icon:"Search",onClick:L},{default:l(()=>[g("搜索")]),_:1}),e(_,{icon:"Refresh",onClick:X},{default:l(()=>[g("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[W,n(V)]]),e(re,{gutter:10,class:"mb8"},{default:l(()=>[e(ie,{span:1.5},{default:l(()=>[x((S(),$(_,{type:"primary",plain:"",icon:"Plus",onClick:ee},{default:l(()=>[g("新增")]),_:1})),[[z,["system:skill:add"]]])]),_:1}),e(se,{showSearch:n(V),"onUpdate:showSearch":t[1]||(t[1]=a=>D(V)?V.value=a:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),x((S(),$(ue,{data:n(T),onSelectionChange:Y},{default:l(()=>[e(w,{type:"selection",width:"55",align:"center"}),e(w,{label:"技能名称",align:"center",prop:"name",width:"160"}),e(w,{label:"熟练度",align:"center",prop:"proficiency",width:"160"}),e(w,{label:"技能描述",align:"center",prop:"description",width:"300"}),e(w,{label:"详细说明",align:"center",width:"300"},{default:l(a=>[e(de,{effect:"dark",placement:"top"},{content:l(()=>[c("div",Re,[Le,c("div",null,I(a.row.textList.map((y,E)=>`${E+1}. ${y}`).join(`
`)),1)])]),default:l(()=>[c("div",qe,I(a.row.textList[0]||"暂无详细说明"),1)]),_:2},1024)]),_:1}),e(w,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[x((S(),$(_,{link:"",type:"primary",icon:"Edit",onClick:y=>te(a.row)},{default:l(()=>[g("修改")]),_:2},1032,["onClick"])),[[z,["system:skill:edit"]]]),x((S(),$(_,{link:"",type:"primary",icon:"Delete",onClick:y=>ae(a.row)},{default:l(()=>[g("删除")]),_:2},1032,["onClick"])),[[z,["system:skill:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,n(O)]]),x(e(ce,{total:n(U),page:n(m).pageNum,"onUpdate:page":t[2]||(t[2]=a=>n(m).pageNum=a),limit:n(m).pageSize,"onUpdate:limit":t[3]||(t[3]=a=>n(m).pageSize=a),onPagination:b},null,8,["total","page","limit"]),[[W,n(U)>0]]),e(pe,{title:n(B),modelValue:n(h),"onUpdate:modelValue":t[8]||(t[8]=a=>D(h)?h.value=a:null),width:"1080px","append-to-body":""},{footer:l(()=>[c("div",Qe,[e(_,{type:"primary",onClick:le},{default:l(()=>[g("确 定")]),_:1}),e(_,{onClick:M},{default:l(()=>[g("取 消")]),_:1})])]),default:l(()=>[e(j,{ref:"skillRef",model:n(d),rules:n(Z),"label-width":"80px"},{default:l(()=>[e(f,{label:"技能名称",prop:"name"},{default:l(()=>[e(i,{modelValue:n(d).name,"onUpdate:modelValue":t[4]||(t[4]=a=>n(d).name=a),placeholder:"请输入技能名称"},null,8,["modelValue"])]),_:1}),e(f,{label:"熟练度",prop:"proficiency"},{default:l(()=>[e(me,{modelValue:n(d).proficiency,"onUpdate:modelValue":t[5]||(t[5]=a=>n(d).proficiency=a),placeholder:"请选择熟练度"},{default:l(()=>[e(q,{label:"精通",value:"精通"}),e(q,{label:"熟练",value:"熟练"}),e(q,{label:"良好",value:"良好"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"技能描述",prop:"description"},{default:l(()=>[e(i,{modelValue:n(d).description,"onUpdate:modelValue":t[6]||(t[6]=a=>n(d).description=a),type:"textarea",placeholder:"请输入技能描述"},null,8,["modelValue"])]),_:1}),e(f,{label:"详细说明",prop:"text"},{default:l(()=>[c("div",ze,[c("div",Ee,[e(n(Ne),{modelValue:n(k),"onUpdate:modelValue":t[7]||(t[7]=a=>D(k)?k.value=a:null),language:"zh-CN","preview-theme":"default","code-theme":"github",style:{height:"200px"}},null,8,["modelValue"]),e(_,{type:"primary",onClick:ne,style:{"margin-top":"10px"}},{default:l(()=>[g("添加说明")]),_:1})])]),n(v).length>0?(S(),N("div",De,[Pe,(S(!0),N(be,null,we(n(v),(a,y)=>(S(),N("div",{key:y,class:"detail-item"},[c("div",Te,I(y+1)+".",1),c("div",Fe,[c("div",{style:xe({fontWeight:a.isBold?"bold":"normal"})},I(a.text),5)]),c("div",je,[e(_,{link:"",type:"primary",onClick:E=>oe(a,y)},{default:l(()=>[g("编辑")]),_:2},1032,["onClick"]),e(_,{link:"",type:"danger",onClick:E=>F(y)},{default:l(()=>[g("删除")]),_:2},1032,["onClick"])])]))),128))])):Se("",!0)]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),He=_e(Ae,[["__scopeId","data-v-4eaeaacc"]]);export{He as default};
