import request from '@/utils/request'

// 查询项目列表
export function listProject(query) {
  return request({
    url: '/project/page/list',
    method: 'get',
    params: query
  })
}

// 查询项目详细
export function getProject(proId) {
  return request({
    url: '/project/' + proId,
    method: 'get'
  })
}

// 新增项目
export function addProject(data) {
  return request({
    url: '/project/add',
    method: 'post',
    data: data
  })
}

// 修改项目
export function updateProject(data) {
  return request({
    url: `/project/${data.proId}`,
    method: 'put',
    data: data
  })
}

// 删除项目
export function delProject(proId) {
  return request({
    url: `/project/delete/${proId}`,
    method: 'delete'
  })
}
// 生成问题 /project/selectByContent
export function selectByContent(data) {
  return request({
    url: '/project/selectByContent',
    method: 'post',
    data: data
  })
}