import request from '@/utils/request'

// 查询作业管理列表
export function listAssignments(query) {
  return request({
    url: '/aqsystem/assignments/list',
    method: 'get',
    params: query
  })
}

// 查询作业管理详细
export function selectAssignments(assignmentId) {
  return request({
    url: '/aqsystem/assignments/' + assignmentId,
    method: 'get'
  })
}

// 修改作业管理
export function updateAssignments(data) {
  return request({
    url: '/aqsystem/assignments',
    method: 'put',
    data: data
  })
}

// 删除作业管理
export function delAssignments(assignmentId) {
  return request({
    url: '/aqsystem/assignments/' + assignmentId,
    method: 'delete'
  })
}

// aqsystem/exams/select/exam001
export function ass(assignmentId) {
  return request({
    url: '/aqsystem/assignments/select/' + assignmentId,
    method: 'get'
  })
}
