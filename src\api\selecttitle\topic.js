import request from '@/utils/request'

// 查询选题信息列表
export function listTopic(query) {
  return request({
    url: '/system/topic/list',
    method: 'post',
    data: {
      keyword: query.title || '',
      pageNum: query.pageNum,
      pageSize: query.pageSize
    }
  })
}

// 查询选题信息详细
export function getTopic(topicId) {
  return request({
    url: `/system/topic/selectById/${topicId}`,
    method: 'get'
  })
}

// 获取导师列表
export function listSupervisors() {
  return request({
    url: '/system/supervisor/list',
    method: 'post',
    data: {
      keyword: '',
      pageNum: 1,
      pageSize: 100
    }
  })
}

// 获取类别列表
export function listCategories() {
  return request({
    url: '/system/cate/list',
    method: 'post',
    data: {
      keyword: '',
      pageNum: 1,
      pageSize: 100
    }
  })
}

// 新增选题信息
export function addTopic(data) {
  return request({
    url: '/system/topic/add',
    method: 'post',
    data: data
  })
}

// 修改选题信息
export function updateTopic(data) {
  return request({
    url: '/system/topic/update',
    method: 'put',
    data: {
      topicId: data.topicId,
      title: data.title,
      description: data.description,
      supervisorId: data.supervisorId,
      categoryId: data.categoryId,
      tag: data.tag,
      publishDate: data.publishDate,
    }
  })
}

// 删除选题信息
export function delTopic(topicId) {
  return request({
    url: `/system/topic/delete/${topicId}`,
    method: 'delete'
  })
}
