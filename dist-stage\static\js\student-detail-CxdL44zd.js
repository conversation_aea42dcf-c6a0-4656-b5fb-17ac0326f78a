import{_ as H,u as L,a as W,r as I,z as X,F as Y,K as E,e as f,c,i as e,f as t,t as o,h as s,n as w,l as d,$ as Z,O as q,a0 as ee,R as te,S as se,a1 as ae,o as r,H as M,I as T,M as A,k as v,Q as N,a2 as B,v as oe,x as le}from"./index-BJsoK47l.js";const i=S=>(oe("data-v-2817487e"),S=S(),le(),S),ne={class:"app-container"},ce={class:"page-header"},re={class:"page-title"},de={class:"page-subtitle"},ie={class:"student-info"},ue={class:"student-info"},_e={class:"student-info"},pe={class:"header-actions"},fe={class:"card-header"},he=i(()=>e("span",null,"总分",-1)),ve={class:"card-value"},me={class:"card-footer"},xe={class:"card-header"},we=i(()=>e("span",null,"正确率",-1)),ye={class:"card-value"},Se={class:"card-footer"},Ce={class:"card-header"},be=i(()=>e("span",null,"答题用时",-1)),ge={class:"card-value"},Ie={class:"card-footer"},Ae={class:"card-header"},Ve=i(()=>e("span",null,"排名",-1)),ke={class:"card-value"},Ne={class:"card-footer"},Be={class:"card-header"},Pe=i(()=>e("span",null,"答题详情",-1)),je={class:"header-actions"},Ee={class:"question-list"},Me={class:"question-header"},Te={class:"question-title"},Oe={class:"question-index"},$e={class:"question-type"},De={class:"question-score"},Je={class:"question-status"},Ue={class:"question-content"},Qe={class:"question-text"},Re={key:0,class:"options-list"},ze={class:"option-label"},Fe={class:"option-text"},Ke={key:0,class:"option-icon"},Ge={key:1,class:"option-icon"},He={key:1,class:"judge-options"},Le=i(()=>e("span",{class:"option-label"},"A",-1)),We=i(()=>e("span",{class:"option-text"},"正确",-1)),Xe={key:0,class:"option-icon"},Ye={key:1,class:"option-icon"},Ze=i(()=>e("span",{class:"option-label"},"B",-1)),qe=i(()=>e("span",{class:"option-text"},"错误",-1)),et={key:0,class:"option-icon"},tt={key:1,class:"option-icon"},st={key:2,class:"fill-answer"},at={class:"student-answer"},ot=i(()=>e("div",{class:"answer-label"},"学生答案：",-1)),lt={class:"answer-content"},nt={class:"correct-answer"},ct=i(()=>e("div",{class:"answer-label"},"正确答案：",-1)),rt={class:"answer-content"},dt={key:3,class:"question-explanation"},it=i(()=>e("div",{class:"explanation-title"},"解析：",-1)),ut={class:"explanation-content"},_t={__name:"student-detail",setup(S){const m=L(),O=W(),u=I({examId:"",examName:"",className:"",totalScore:100,duration:7200,totalStudents:10}),_=I({studentId:"",studentName:"",totalScore:0,accuracy:0,duration:0,rank:0}),C=I("all"),b=I([]),$=X(()=>C.value==="all"?b.value:C.value==="correct"?b.value.filter(l=>l.isCorrect):b.value.filter(l=>!l.isCorrect));function D(l){return{choice:"单选题",multiple:"多选题",judge:"判断题",fill:"填空题",essay:"简答题"}[l]||l}function J(l){return l>=80?"#67c23a":l>=60?"#e6a23c":"#f56c6c"}function P(l){const h=Math.floor(l/3600),n=Math.floor(l%3600/60),x=l%60;return h>0?`${h}小时${n}分${x}秒`:n>0?`${n}分${x}秒`:`${x}秒`}function U(){O.push({path:"/aqsystem/exams/student-list",query:{examId:u.value.examId,examName:u.value.examName,className:u.value.className}})}function Q(){E.success("导出成功")}const V=[{id:1,type:"choice",content:"以下哪个不是JavaScript的数据类型？",options:[{label:"A",text:"String",isCorrect:!1,isSelected:!1},{label:"B",text:"Boolean",isCorrect:!1,isSelected:!1},{label:"C",text:"Integer",isCorrect:!0,isSelected:!0},{label:"D",text:"Object",isCorrect:!1,isSelected:!1}],score:5,totalScore:5,isCorrect:!0,explanation:"JavaScript的数据类型包括：String、Number、Boolean、Object、Undefined、Null、Symbol和BigInt。Integer不是JavaScript的数据类型，JavaScript中的数字类型是Number。"},{id:2,type:"choice",content:"在Vue 3中，以下哪个不是组合式API的函数？",options:[{label:"A",text:"ref",isCorrect:!1,isSelected:!0},{label:"B",text:"reactive",isCorrect:!1,isSelected:!1},{label:"C",text:"computed",isCorrect:!1,isSelected:!1},{label:"D",text:"watchEffect",isCorrect:!0,isSelected:!1}],score:0,totalScore:5,isCorrect:!1,explanation:"Vue 3的组合式API包括ref、reactive、computed、watch、watchEffect等函数。watchEffect是组合式API的函数，用于自动追踪依赖并在依赖变化时重新执行。"},{id:3,type:"judge",content:"Vue 3中的响应式系统是基于Proxy实现的。",answer:"true",studentAnswer:"true",score:5,totalScore:5,isCorrect:!0,explanation:"Vue 3的响应式系统是基于JavaScript的Proxy API实现的，这比Vue 2中基于Object.defineProperty的实现更加强大和灵活。"},{id:4,type:"judge",content:"在Vue 3中，setup函数是组件选项API的一部分。",answer:"false",studentAnswer:"true",score:0,totalScore:5,isCorrect:!1,explanation:"setup函数是Vue 3组合式API的一部分，而不是选项API的一部分。选项API包括data、methods、computed等选项，而组合式API则是基于函数的API。"},{id:5,type:"fill",content:"在Vue 3中，用于创建响应式引用的函数是______。",answer:"ref",studentAnswer:"ref",score:5,totalScore:5,isCorrect:!0,explanation:"ref是Vue 3中用于创建响应式引用的函数，它可以包装任何类型的值，使其成为响应式的。"},{id:6,type:"fill",content:"在Vue 3中，用于创建响应式对象的函数是______。",answer:"reactive",studentAnswer:"reactive",score:5,totalScore:5,isCorrect:!0,explanation:"reactive是Vue 3中用于创建响应式对象的函数，它接收一个普通对象作为参数，返回该对象的响应式代理。"},{id:7,type:"choice",content:"以下哪个不是Vue 3的生命周期钩子？",options:[{label:"A",text:"onMounted",isCorrect:!1,isSelected:!1},{label:"B",text:"onUpdated",isCorrect:!1,isSelected:!1},{label:"C",text:"onBeforeMount",isCorrect:!1,isSelected:!1},{label:"D",text:"onCreated",isCorrect:!0,isSelected:!0}],score:5,totalScore:5,isCorrect:!0,explanation:"在Vue 3的组合式API中，生命周期钩子包括onBeforeMount、onMounted、onBeforeUpdate、onUpdated、onBeforeUnmount、onUnmounted等，但没有onCreated。在选项API中，created是生命周期钩子，但在组合式API中，setup函数本身就相当于created和beforeCreate。"},{id:8,type:"choice",content:"在Vue 3中，以下哪个不是内置组件？",options:[{label:"A",text:"Transition",isCorrect:!1,isSelected:!1},{label:"B",text:"Suspense",isCorrect:!1,isSelected:!1},{label:"C",text:"Teleport",isCorrect:!1,isSelected:!1},{label:"D",text:"RouterView",isCorrect:!0,isSelected:!0}],score:5,totalScore:5,isCorrect:!0,explanation:"Vue 3的内置组件包括Transition、TransitionGroup、KeepAlive、Teleport、Suspense等，而RouterView是Vue Router提供的组件，不是Vue的内置组件。"},{id:9,type:"judge",content:"Vue 3支持多根节点组件（Fragment）。",answer:"true",studentAnswer:"true",score:5,totalScore:5,isCorrect:!0,explanation:"Vue 3支持多根节点组件，也称为Fragment，这允许组件有多个根节点，而不需要额外的包装元素。"},{id:10,type:"fill",content:"在Vue 3中，用于创建计算属性的函数是______。",answer:"computed",studentAnswer:"computed",score:5,totalScore:5,isCorrect:!0,explanation:"computed是Vue 3中用于创建计算属性的函数，它接收一个getter函数作为参数，返回一个只读的响应式引用。"}];return console.log("mockQuestions isExtensible:",Object.isExtensible(V)),V.forEach((l,h)=>console.log(`mockQuestions[${h}] isExtensible:`,Object.isExtensible(l))),Y(()=>{m.query.examId&&m.query.studentId?(u.value={examId:m.query.examId,examName:m.query.examName||"期中考试",className:m.query.className||"计算机科学2101",totalScore:100,duration:7200,totalStudents:10},_.value={studentId:m.query.studentId,studentName:m.query.studentName||"张三",totalScore:85,accuracy:85,duration:3600,rank:3},b.value=JSON.parse(JSON.stringify(V))):E.warning("未获取到考试或学生信息")}),(l,h)=>{const n=f("el-icon"),x=f("el-button"),y=f("el-card"),g=f("el-col"),R=f("el-progress"),z=f("el-row"),k=f("el-radio-button"),F=f("el-radio-group"),K=f("el-tag");return r(),c("div",ne,[e("div",ce,[e("div",null,[e("h1",re,o(u.value.examName)+" - 学生答题详情",1),e("p",de,[e("span",ie,"学生："+o(_.value.studentName),1),e("span",ue,"学号："+o(_.value.studentId),1),e("span",_e,"班级："+o(u.value.className),1)])]),e("div",pe,[t(x,{onClick:U},{default:s(()=>[t(n,null,{default:s(()=>[t(d(Z))]),_:1}),w("返回 ")]),_:1}),t(x,{type:"primary",onClick:Q},{default:s(()=>[t(n,null,{default:s(()=>[t(d(q))]),_:1}),w("导出详情 ")]),_:1})])]),t(z,{gutter:20,class:"overview-cards"},{default:s(()=>[t(g,{span:6},{default:s(()=>[t(y,{shadow:"hover",class:"overview-card"},{header:s(()=>[e("div",fe,[he,t(n,null,{default:s(()=>[t(d(ee))]),_:1})])]),default:s(()=>[e("div",ve,o(_.value.totalScore),1),e("div",me,[e("span",null,"满分："+o(u.value.totalScore),1)])]),_:1})]),_:1}),t(g,{span:6},{default:s(()=>[t(y,{shadow:"hover",class:"overview-card"},{header:s(()=>[e("div",xe,[we,t(n,null,{default:s(()=>[t(d(te))]),_:1})])]),default:s(()=>[e("div",ye,o(_.value.accuracy)+"%",1),e("div",Se,[t(R,{percentage:_.value.accuracy,color:J(_.value.accuracy)},null,8,["percentage","color"])])]),_:1})]),_:1}),t(g,{span:6},{default:s(()=>[t(y,{shadow:"hover",class:"overview-card"},{header:s(()=>[e("div",Ce,[be,t(n,null,{default:s(()=>[t(d(se))]),_:1})])]),default:s(()=>[e("div",ge,o(P(_.value.duration)),1),e("div",Ie,[e("span",null,"考试时长："+o(P(u.value.duration)),1)])]),_:1})]),_:1}),t(g,{span:6},{default:s(()=>[t(y,{shadow:"hover",class:"overview-card"},{header:s(()=>[e("div",Ae,[Ve,t(n,null,{default:s(()=>[t(d(ae))]),_:1})])]),default:s(()=>[e("div",ke,o(_.value.rank),1),e("div",Ne,[e("span",null,"共"+o(u.value.totalStudents)+"人",1)])]),_:1})]),_:1})]),_:1}),t(y,{class:"detail-card",shadow:"hover"},{header:s(()=>[e("div",Be,[Pe,e("div",je,[t(F,{modelValue:C.value,"onUpdate:modelValue":h[0]||(h[0]=a=>C.value=a),size:"small"},{default:s(()=>[t(k,{label:"all"},{default:s(()=>[w("全部")]),_:1}),t(k,{label:"correct"},{default:s(()=>[w("正确")]),_:1}),t(k,{label:"wrong"},{default:s(()=>[w("错误")]),_:1})]),_:1},8,["modelValue"])])])]),default:s(()=>[e("div",Ee,[(r(!0),c(M,null,T($.value,(a,j)=>(r(),c("div",{key:j,class:"question-item"},[e("div",Me,[e("div",Te,[e("span",Oe,"第"+o(j+1)+"题",1),e("span",$e,o(D(a.type)),1),e("span",De,[e("span",{class:A({"text-success":a.isCorrect,"text-danger":!a.isCorrect})},o(a.score)+"/"+o(a.totalScore)+"分 ",3)])]),e("div",Je,[t(K,{type:a.isCorrect?"success":"danger",size:"small"},{default:s(()=>[w(o(a.isCorrect?"正确":"错误"),1)]),_:2},1032,["type"])])]),e("div",Ue,[e("div",Qe,o(a.content),1),a.type==="choice"?(r(),c("div",Re,[(r(!0),c(M,null,T(a.options,(p,G)=>(r(),c("div",{key:G,class:A(["option-item",{"option-correct":p.isCorrect,"option-selected":p.isSelected,"option-wrong":p.isSelected&&!p.isCorrect}])},[e("span",ze,o(p.label),1),e("span",Fe,o(p.text),1),p.isCorrect?(r(),c("span",Ke,[t(n,null,{default:s(()=>[t(d(N))]),_:1})])):v("",!0),p.isSelected&&!p.isCorrect?(r(),c("span",Ge,[t(n,null,{default:s(()=>[t(d(B))]),_:1})])):v("",!0)],2))),128))])):a.type==="judge"?(r(),c("div",He,[e("div",{class:A(["option-item",{"option-correct":a.answer==="true","option-selected":a.studentAnswer==="true","option-wrong":a.studentAnswer==="true"&&a.answer!=="true"}])},[Le,We,a.answer==="true"?(r(),c("span",Xe,[t(n,null,{default:s(()=>[t(d(N))]),_:1})])):v("",!0),a.studentAnswer==="true"&&a.answer!=="true"?(r(),c("span",Ye,[t(n,null,{default:s(()=>[t(d(B))]),_:1})])):v("",!0)],2),e("div",{class:A(["option-item",{"option-correct":a.answer==="false","option-selected":a.studentAnswer==="false","option-wrong":a.studentAnswer==="false"&&a.answer!=="false"}])},[Ze,qe,a.answer==="false"?(r(),c("span",et,[t(n,null,{default:s(()=>[t(d(N))]),_:1})])):v("",!0),a.studentAnswer==="false"&&a.answer!=="false"?(r(),c("span",tt,[t(n,null,{default:s(()=>[t(d(B))]),_:1})])):v("",!0)],2)])):a.type==="fill"?(r(),c("div",st,[e("div",at,[ot,e("div",lt,o(a.studentAnswer||"未作答"),1)]),e("div",nt,[ct,e("div",rt,o(a.answer),1)])])):v("",!0),a.explanation?(r(),c("div",dt,[it,e("div",ut,o(a.explanation),1)])):v("",!0)])]))),128))])]),_:1})])}}},ft=H(_t,[["__scopeId","data-v-2817487e"]]);export{ft as default};
