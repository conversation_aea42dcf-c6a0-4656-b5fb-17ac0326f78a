<template>
  <div class="feedback-detail">
    <div class="feedback-section">
      <div class="section-title">总体评分</div>
      <div class="score-display">
        <el-progress 
          type="dashboard" 
          :percentage="score" 
          :color="getScoreColor(score)"
          :stroke-width="8"
          :width="80"
        >
          <template #default>
            <div class="progress-content">
              <span class="progress-value">{{ score }}</span>
              <span class="progress-label">分</span>
            </div>
          </template>
        </el-progress>
      </div>
    </div>

    <!-- 面试结果 -->
    <div v-if="result !== null" class="feedback-section">
      <div class="section-title">面试结果</div>
      <div class="result-display">
        <el-tag :type="result === 1 ? 'success' : 'danger'">
          {{ result === 1 ? '面试通过' : '面试未通过' }}
        </el-tag>
      </div>
    </div>

    <div v-if="feedback" class="feedback-section">
      <div class="section-title">面试反馈</div>
      <div class="feedback-content">{{ feedback }}</div>
    </div>

    <div v-if="strengths" class="feedback-section">
      <div class="section-title">优势表现</div>
      <div class="feedback-content">{{ strengths }}</div>
    </div>

    <div v-if="improvements" class="feedback-section">
      <div class="section-title">改进建议</div>
      <div class="feedback-content">{{ improvements }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  score: {
    type: Number,
    default: 0
  },
  feedback: {
    type: String,
    default: ''
  },
  strengths: {
    type: String,
    default: ''
  },
  improvements: {
    type: String,
    default: ''
  },
  result: {
    type: Number,
    default: null
  }
});

// 根据分数获取不同的颜色
const getScoreColor = (score) => {
  if (score < 60) {
    return '#F56C6C';
  } else if (score < 80) {
    return '#E6A23C';
  } else {
    return '#67C23A';
  }
};
</script>

<style scoped>
.feedback-detail {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 20px;
}

.feedback-section {
  margin-bottom: 15px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}

.score-display {
  display: flex;
  justify-content: center;
  margin: 15px 0;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-value {
  font-size: 20px;
  font-weight: bold;
}

.progress-label {
  font-size: 12px;
}

.feedback-content {
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  color: #606266;
  line-height: 1.5;
  white-space: pre-wrap;
}

.result-display {
  margin: 10px 0;
}
</style> 