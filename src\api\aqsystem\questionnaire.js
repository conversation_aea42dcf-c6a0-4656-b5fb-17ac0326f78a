import request from '@/utils/request'

// 查询问卷列表
export function listQuestionnaire(query) {
  return request({
    url: '/aqsystem/questionnaires/list',
    method: 'get',
    params: query
  })
}

// 获取问卷详细信息（含题目）
// /aqsystem/questionnaires/select/{questionnaireId}
export function getQuestionnaireDetail(questionnaireId) {
  return request({
    url: '/aqsystem/questionnaires/' + questionnaireId,
    method: 'get'
  })
}


// 新增问卷 /aqsystem/questionnaires
export function addQuestionnaire(data) {
  return request({
    url: '/aqsystem/questionnaires',
    method: 'post',
    data: data
  })
}

// 修改问卷
export function updateQuestionnaire(data) {
  return request({
    url: '/aqsystem/questionnaires',
    method: 'put',
    data: data
  })
}

 // /aqsystem/questionnaires/select/{questionnaireId}
 export function getQuestionnaireDetailPage(questionnaireId) {
  return request({
    url: '/aqsystem/questionnaires/select/' + questionnaireId,
    method: 'get'
  })
}


// 删除问卷 /aqsystem/questionnaires/{questionnaireIds}
export function deleteQuestionnaire(questionnaireIds) {
  return request({
    url: '/aqsystem/questionnaires/' + questionnaireIds,
    method: 'delete'
  })
}


// 复制问卷
export function copyQuestionnaire(data) {
  return request({
    url: '/aqsystem/questionnaire/copy',
    method: 'post',
    data: data
  })
}

// 获取问卷统计信息
export function getQuestionnaireStats(id) {
  return request({
    url: '/aqsystem/questionnaire/stats/' + id,
    method: 'get'
  })
} 





// /aqsystem/wjquestions/list
// 查询问卷题目列库
export function listQuestion(query) {
  return request({
    url: '/aqsystem/wjquestions/list',
    method: 'get',
    params: query
  })
}
// /aqsystem/label/list
// 查询问卷类型列表
export function listLabel(query) {
  return request({
    url: '/aqsystem/label/list',
    method: 'get',
    params: query
  })
}
// /aqsystem/wjquestions
// 新增问卷题目
export function addQuestion(data) {
  return request({
    url: '/aqsystem/wjquestions',
    method: 'post',
    data: data
  })
}

///aqsystem/wjquestions/{questionsId}
// 获取问卷题库详细信息
export function getQuestionDetail(questionsId) {
  return request({
    url: '/aqsystem/wjquestions/' + questionsId,
    method: 'get'
  })
}


// 修改问卷题库题目 /aqsystem/wjquestions 
export function updateQuestion(data) {
  return request({
    url: '/aqsystem/wjquestions',
    method: 'put',
    data: data
  })
}

// /aqsystem/wjquestions/{questionsIds}
// 批量删除问卷题库题目
export function deleteQuestion(questionsIds) {
  return request({
    url: '/aqsystem/wjquestions/' + questionsIds,
    method: 'delete'
  })
}
// 问卷题库导入 /aqsystem/wjquestions/import
export function importWjQuestions(data) {
  return request({
    url: '/aqsystem/wjquestions/import',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}