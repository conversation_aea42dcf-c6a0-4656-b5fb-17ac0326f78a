import request from '@/utils/request'

// 查询选题类别列表
export function listCategory(query) {
  return request({
    url: '/system/cate/list',
    method: 'post',
    data: {
      keyword: query.categoryName || '',
      pageNum: query.pageNum,
      pageSize: query.pageSize
    }
  })
}

// 查询选题类别详细
export function getCategory(categoryId) {
  return request({
    url: '/system/cate/selectById/' + categoryId,
    method: 'get'
  })
}

// 新增选题类别
export function addCategory(data) {
  return request({
    url: '/system/cate/add',
    method: 'post',
    data: data
  })
}

// 修改选题类别
export function updateCategory(data) {
  return request({
    url: '/system/cate/update',
    method: 'put',
    data: {
      categoryId: data.categoryId,
      categoryName: data.categoryName
    }
  })
}

// 删除选题类别
export function delCategory(categoryId) {
  return request({
    url: `/system/cate/delete/${categoryId}`,
    method: 'delete'
  })
}
