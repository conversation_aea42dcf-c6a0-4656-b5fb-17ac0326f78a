<template>
  <div class="app-container">
    <div class="page-header">
      <h1 class="page-title">试卷管理</h1>
    </div>
    <!-- 搜索和筛选区域 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <!-- 试卷搜索条件 -->
        <template v-if="isExamView">
          <el-form-item label="试卷名称">
            <el-input
              v-model="queryParams.title"
              placeholder="请输入试卷名称"
              clearable
              prefix-icon="Search"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="试卷状态">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="filter-select">
              <el-option label="未开始" value="未开始" />
              <el-option label="进行中" value="进行中" />
              <el-option label="已完成" value="已完成" />
            </el-select>
          </el-form-item>
          <el-form-item label="班级">
            <el-input
              v-model="queryParams.className"
              placeholder="请输入班级名称"
              clearable
              prefix-icon="Search"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </template>
        <!-- 作业搜索条件 -->
        <template v-else>
          <el-form-item label="作业名称">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入作业名称"
              clearable
              prefix-icon="Search"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="班级">
            <el-input
              v-model="queryParams.className"
              placeholder="请输入班级名称"
              clearable
              prefix-icon="Search"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </template>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ isExamView ? '试卷列表' : '作业列表' }}</span>
          <div class="header-buttons">
            <el-button
              :type="isExamView ? 'primary' : 'default'"
              :icon="Document"
              @click="toggleView"
            >{{ isExamView ? '作业' : '试卷' }}</el-button>
            <el-button
              type="primary"
              icon="Plus"
              @click="handleCreate"
            >新增{{ isExamView ? '试卷' : '作业' }}</el-button>
          </div>
        </div>
      </template>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="isExamView ? examList : assignmentList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <!-- 试卷列表列 -->
        <template v-if="isExamView">
          <el-table-column prop="title" label="试卷名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="className" label="班级" min-width="120" align="center" />
          <el-table-column prop="startTime" label="开始时间" min-width="160" align="center" />
          <el-table-column prop="endTime" label="结束时间" min-width="160" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="participantCount" label="参与人数" width="120" align="center">
            <template #default="scope">
              <el-progress 
                :percentage="Math.round((scope.row.participantCount / scope.row.totalCount) * 100)" 
                :format="() => `${scope.row.participantCount}/${scope.row.totalCount}`"
                :stroke-width="8"
                :color="getProgressColor(scope.row.participantCount, scope.row.totalCount)"
              />
            </template>
          </el-table-column>
        </template>
        <!-- 作业列表列 -->
        <template v-else>
          <el-table-column prop="name" label="作业名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="className" label="班级" min-width="120" align="center" />
          <el-table-column prop="description" label="作业描述" min-width="150" show-overflow-tooltip align="center" />
          <el-table-column prop="totalScore" label="总分" width="120" align="center" />
        </template>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleEdit(row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleView(row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改试卷对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="examFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="试卷名称" prop="examName">
          <el-input v-model="form.examName" placeholder="请输入试卷名称" />
        </el-form-item>
        <el-form-item label="班级" prop="className">
          <el-input v-model="form.className" placeholder="请输入班级名称" />
        </el-form-item>
        <el-form-item label="试卷时间" prop="examTime">
          <el-date-picker
            v-model="form.examTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="试卷说明" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入试卷说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { Edit, View, Delete, Document } from '@element-plus/icons-vue'
import { listExams,  delExams } from '@/api/aqsystem/exams'
import { listAssignments, delAssignments } from '@/api/aqsystem/zuoye'
const router = useRouter()

// 遮罩层
const loading = ref(false)
// 总条数
const total = ref(0)
// 试卷表格数据
const examList = ref([])
// 作业表格数据
const assignmentList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(JSON.parse(JSON.stringify(false)))
// 表单参数
const form = ref(JSON.parse(JSON.stringify({})))
// 表单校验
const rules = ref(JSON.parse(JSON.stringify({
  examName: [
    { required: true, message: "试卷名称不能为空", trigger: "blur" }
  ],
  className: [
    { required: true, message: "请输入班级名称", trigger: "blur" }
  ],
  examTime: [
    { required: true, message: "请选择试卷时间", trigger: "change" }
  ]
})))

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  name: undefined,
  status: undefined,
  className: undefined
})

// 状态选项


// 视图类型控制
const isExamView = ref(true)

/** 查询试卷列表 */
function getList() {
  loading.value = true
  if (isExamView.value) {
    listExams(queryParams).then(response => {
      console.log('查询参数:', queryParams)
      console.log('响应数据:', response)
      examList.value = response.rows
      total.value = response.total
      loading.value = false
    }).catch(error => {
      console.error('获取列表失败:', error)
      loading.value = false
    })
  } else {
    // 修改作业查询参数
    const assignmentParams = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      title: queryParams.name, // 使用name作为title查询
      className: queryParams.className
    }
    listAssignments(assignmentParams).then(response => {
      console.log('查询作业参数:', assignmentParams)
      console.log('作业响应数据:', response)
      assignmentList.value = response.rows
      total.value = response.total
      loading.value = false
    }).catch(error => {
      console.error('获取作业列表失败:', error)
      loading.value = false
    })
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  if (isExamView.value) {
    queryParams.title = undefined
    queryParams.status = undefined
  } else {
    queryParams.name = undefined
  }
  queryParams.className = undefined
  handleQuery()
}

/** 获取状态文本 */
function getStatusText(status) {
  switch (status) {
    case '1':
      return '进行中'
    case '0':
      return '未开始'
    case '2':
      return '已完成'
    default:
      return status
  }
}

/** 获取进度条颜色 */
function getProgressColor(current, total) {
  const percentage = (current / total) * 100
  if (percentage < 30) {
    return '#f56c6c'
  } else if (percentage < 70) {
    return '#e6a23c'
  } else {
    return '#67c23a'
  }
}

/** 新增按钮操作 */
function handleCreate() {
  router.push({
    path: '/aqsystem/exams/edit',
    query: { type: isExamView.value ? 'exam' : 'homework' }
  })
}

/** 修改按钮操作 */
function handleEdit(row) {
  if (isExamView.value) {
    router.push({
      path: '/aqsystem/exams/edit',
      query: { id: row.examId }
    })
  } else {
    router.push({
      path: '/aqsystem/exams/edit',
      query: { assignmentId: row.assignmentId }
    })
  }
}

/** 查看按钮操作 */
function handleView(row) {
  if (isExamView.value) {
    router.push({
      path: '/aqsystem/exams/student-list',
      query: {
        examId: row.examId,
        examName: row.examName,
        className: row.className
      }
    })
  } else {
    router.push({
      path: '/aqsystem/exams/student-list',
      query: {
        assignmentId: row.assignmentId
      }
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const type = isExamView.value ? '试卷' : '作业'
  const name = isExamView.value ? row.title : row.name
  ElMessageBox.confirm(`是否确认删除${type}名称为"${name}"的数据项?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      if (isExamView.value) {
        await delExams(row.examId)
      } else {
        await delAssignments(row.assignmentId)
      }
      ElMessage.success("删除成功")
      getList() // 重新加载列表数据
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error("删除失败：" + (error.message || '未知错误'))
    }
  }).catch(() => {
    ElMessage.info("已取消删除")
  })
}

/** 表单重置 */
function reset() {
  form.value = {
    examId: undefined,
    examName: undefined,
    className: undefined,
    examTime: undefined,
    description: undefined
  }
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 提交按钮 */
function submitForm() {
  // 这里应该是实际的提交API调用
  ElMessage.success("保存成功")
  open.value = false
  getList()
}


/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case '未开始':
      return 'info'     // 未开始 - 灰色
    case '进行中':
      return 'warning'  // 进行中 - 黄色
    case '已完成':
      return 'success'  // 已完成 - 绿色
    default:
      return 'info'
  }
}

// 切换视图
function toggleView() {
  isExamView.value = !isExamView.value
  // 这里可以添加获取作业列表的逻辑
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.filter-container {
  margin-bottom: 20px;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-select {
  width: 160px;
}

.search-buttons {
  margin-left: auto;
}

.box-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-buttons {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .filter-select {
    width: 100%;
  }
}
</style>
