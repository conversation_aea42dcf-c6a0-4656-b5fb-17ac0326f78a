<template>
  <div class="app-container">
    <div class="page-header">
      <h1 class="page-title">{{ form.questionId ? '编辑题目' : '添加题目' }}</h1>
      <div class="header-actions">
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="form-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="custom-form">
        <el-form-item label="题目类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择题目类型" class="form-select">
            <el-option
              v-for="dict in questionTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="题目分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择题目分类" class="form-select">
            <el-option
              v-for="dict in categoryOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="3"
            placeholder="请输入题目内容"
            class="content-input"
          />
        </el-form-item>

        <!-- 选项区域 -->
        <div v-if="['单选题', '多选题', '不定项选择题'].includes(form.type)" class="options-section">
          <div class="options-header">
            <span class="options-title">选项列表</span>
            <el-button type="primary" link @click="addOption" class="add-option-btn">
              <el-icon><Plus /></el-icon>添加选项
            </el-button>
          </div>
          
          <el-form-item
            v-for="(option, index) in form.options"
            :key="index"
            :prop="'options.' + index + '.content'"
            :rules="{
              required: true,
              message: '选项内容不能为空',
              trigger: 'blur'
            }"
            class="option-form-item"
          >
            <div class="option-item">
              <div class="option-label">{{ String.fromCharCode(65 + index) }}</div>
              <el-input
                v-model="option.content"
                :placeholder="'请输入选项内容'"
                class="option-input"
              />
              <el-checkbox
                v-model="option.isCorrect"
                :disabled="form.type === '单选题' && getCorrectOptionsCount() > 0 && !option.isCorrect"
                class="correct-checkbox"
              >
                正确答案
              </el-checkbox>
              <el-button
                type="danger"
                link
                @click="removeOption(index)"
                class="delete-option-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </div>

        <el-form-item label="答案解析" prop="analysis">
          <el-input
            v-model="form.analysis"
            type="textarea"
            :rows="3"
            placeholder="请输入答案解析"
            class="analysis-input"
          />
        </el-form-item>

        <el-form-item class="form-footer">
          <el-button type="primary" @click="submitForm" class="submit-btn">保存题目</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { updateQuestions, addQuestions, getCategoryList } from '@/api/aqsystem/questions'
const route = useRoute()
const router = useRouter()
const formRef = ref(JSON.parse(JSON.stringify(null)))

// 表单数据
const form = reactive({
  questionId: undefined,
  type: '',
  category: '',
  content: '',
  options: [],
  analysis: ''
})

// 表单校验规则
const rules = {
  type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择题目分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }]
}

// 题目类型选项
const questionTypeOptions = ref(JSON.parse(JSON.stringify([
  { value: '单选题', label: '单选题' },
  { value: '多选题', label: '多选题' },
  { value: '填空题', label: '填空题' },
  { value: '简答题', label: '简答题' },
  { value: '不定项选择题', label: '不定项选择题' }
])))

// 类目选项
const categoryOptions = ref(JSON.parse(JSON.stringify([])))

// 获取类目列表
const getCategoryOptions = async () => {
  try {
    const res = await getCategoryList()
    if (Array.isArray(res)) {
      categoryOptions.value = res.map(item => ({
        value: item.id,
        label: item.name
      }))
    }
  } catch (error) {
    console.error('获取类目列表失败:', error)
    ElMessage.error('获取类目列表失败')
  }
}

// 添加选项
const addOption = () => {
  form.options.push({
    content: '',
    isCorrect: false
  })
}

// 删除选项
const removeOption = (index) => {
  form.options.splice(index, 1)
}

// 获取已选中的正确答案数量
const getCorrectOptionsCount = () => {
  return form.options.filter(option => option.isCorrect).length
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 验证是否有正确答案
      if (['单选题', '多选题', '不定项选择题'].includes(form.type)) {
        const correctCount = getCorrectOptionsCount()
        if (correctCount === 0) {
          ElMessage.error('请至少选择一个正确答案')
          return
        }
        if (form.type === '单选题' && correctCount > 1) {
          ElMessage.error('单选题只能有一个正确答案')
          return
        }
      }
      
      try {
        // 将选项数组转换为对象格式
        const optionsObj = {}
        form.options.forEach((option, index) => {
          const key = String.fromCharCode(65 + index) // A, B, C, D...
          optionsObj[key] = option.content
        })

        // 获取所有正确答案的字母
        const correctAnswers = form.options
          .map((option, index) => option.isCorrect ? String.fromCharCode(65 + index) : null)
          .filter(Boolean)
          .join('')

        // 构建提交的数据
        const submitData = {
          questionId: form.questionId,
          type: form.type,
          topicClassification: form.category,
          content: form.content,
          options: JSON.stringify(optionsObj),
          analysis: form.analysis,
          answer: correctAnswers
        }

        console.log('提交的数据:', submitData) // 添加日志

        // 根据是否有 questionId 决定是新增还是更新
        if (form.questionId) {
          console.log('更新题目，ID:', form.questionId) // 添加日志
          await updateQuestions(submitData)
        } else {
          console.log('新增题目') // 添加日志
          await addQuestions(submitData)
        }
        
        ElMessage.success('保存成功')
        goBack()
      } catch (error) {
        console.error('保存题目失败:', error)
        ElMessage.error('保存失败，请重试')
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.replace('/aqsystem/exams/setting')
}

onMounted(async () => {
  await getCategoryOptions() // 获取类目列表
  // 如果是编辑模式，获取题目详情
  if (route.query.id) {
    // 设置基本信息
    form.questionId = route.query.id
    form.type = route.query.type
    // 将类目名称转换为ID
    const categoryOption = categoryOptions.value.find(opt => opt.label === route.query.topicClassification)
    form.category = categoryOption ? categoryOption.value : route.query.topicClassification
    form.content = route.query.content
    form.analysis = route.query.analysis
    
    // 根据题目类型初始化选项
    if (['单选题', '多选题', '不定项选择题'].includes(form.type)) {
      // 初始化选项数据
      if (route.query.options) {
        try {
          const optionsData = JSON.parse(route.query.options)
          const answer = route.query.answer
          
          // 将对象格式的选项转换为数组格式，并设置正确答案
          form.options = Object.entries(optionsData).map(([key, value]) => {
            // 对于多选题和不定项选择题，答案可能是多个字母
            const isCorrect = form.type === '单选题' 
              ? key === answer 
              : answer && answer.includes(key)
            
            return {
              content: value,
              isCorrect: isCorrect
            }
          })
        } catch (error) {
          console.error('解析选项数据失败:', error)
          form.options = []
        }
      }
    }
  }
})

// 监听题目类型变化
watch(() => form.type, (newType) => {
  // 如果是编辑模式且有选项数据，不重置选项
  if (form.questionId && form.options.length > 0) {
    return
  }

  // 新增模式下的处理
  if (['单选题', '多选题', '不定项选择题'].includes(newType)) {
    // 单选题、多选题或不定项选择题，清空选项并添加两个空选项
    form.options = []
    addOption()
    addOption()
  } else {
    // 其他类型（填空题、简答题），清空选项
    form.options = []
  }
}, { immediate: true })
</script>

<style scoped>
.app-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.form-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.custom-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-select {
  width: 100%;
}

.content-input,
.analysis-input {
  width: 100%;
}

.options-section {
  margin: 24px 0;
  padding: 16px;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.options-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.add-option-btn {
  padding: 8px 16px;
}

.option-form-item {
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.option-label {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background-color: #f0f2f5;
  border-radius: 50%;
  font-weight: 500;
  color: #606266;
}

.option-input {
  flex: 1;
}

.delete-option-btn {
  margin-left: 8px;
  color: #f56c6c;
}

.form-footer {
  margin-top: 32px;
  text-align: center;
}

.submit-btn {
  padding: 12px 24px;
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  border-color: #66b1ff;
}

.correct-checkbox {
  margin-left: 12px;
  margin-right: 12px;
}
</style> 