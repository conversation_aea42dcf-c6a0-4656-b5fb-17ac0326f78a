import{_ as X,B as Y,a as Z,C as ee,r as m,F as te,a3 as oe,e as i,G as ae,c as C,i as c,f as a,h as l,n as u,m as le,o as _,H as D,I as L,j as k,J as M,t as q,a4 as ne,K as s,v as se,x as ie,E as re}from"./index-BJsoK47l.js";import{l as ce,g as ue,d as pe,i as de}from"./questions-BTIDeXeU.js";const T=y=>(se("data-v-e2761b83"),y=y(),ie(),y),fe={class:"app-container"},_e={class:"page-header"},me=T(()=>c("div",null,[c("h1",{class:"page-title"},"题库管理"),c("p",{class:"page-subtitle"},"管理所有题目和题库")],-1)),ye={class:"header-actions"},ge={class:"form-row"},ve={class:"form-actions"},he={class:"table-header"},be=T(()=>c("h2",{class:"table-title"},"题目列表",-1)),we={class:"table-count"},xe=Y({name:"QuestionBank"}),Ce=Object.assign(xe,{setup(y){const V=Z(),n=ee({pageNum:1,pageSize:10,content:void 0,topicClassification:void 0,type:void 0}),h=m([]),U=async()=>{try{const e=await ue();console.log("获取到的类目数据:",e),Array.isArray(e)?(h.value=e.map(t=>({value:t.id,label:t.name})),console.log("转换后的类目选项:",h.value)):(console.error("类目数据格式不正确:",e),s.error("类目数据格式不正确"))}catch(e){console.error("获取类目列表失败:",e),s.error("获取类目列表失败")}},B=m([{value:"填空题",label:"填空题"},{value:"单选题",label:"单选题"},{value:"多选题",label:"多选题"},{value:"不定项选择题",label:"不定项选择题"}]),b=m(!1),g=m(0),E=m([]),p=async()=>{b.value=!0;try{console.log("查询参数:",n);const e=await ce(n);console.log("获取到的题目列表数据:",e),e.code===200?(E.value=e.rows||[],g.value=e.total||0):s.error(e.msg||"获取题目列表失败")}catch(e){console.error("获取题目列表失败:",e),s.error("获取题目列表失败")}finally{b.value=!1}},w=()=>{n.pageNum=1,p()},z=()=>{n.pageNum=1,n.pageSize=10,n.content=void 0,n.topicClassification=void 0,n.type=void 0,w()},Q=async e=>{const t=e.file;if(t){if(!(t.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||t.type==="application/vnd.ms-excel")){s.error("只能上传Excel文件!");return}if(t.size>2*1024*1024){s.error("文件大小不能超过2MB");return}const v=new FormData;v.append("file",t);try{const d=await de(v);d.code===0?(s.success("导入成功"),p()):s.error(d.msg||"导入失败")}catch(d){console.error("导入失败:",d),s.error("导入失败，请重试")}}},A=e=>e.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||e.type==="application/vnd.ms-excel"?e.size/1024/1024<2?!0:(s.error("文件大小不能超过 2MB!"),!1):(s.error("只能上传Excel文件!"),!1),F=()=>{V.push("/aqsystem/exams/questionForm")},O=e=>{console.log("编辑题目数据:",e),V.push({path:"/aqsystem/exams/questionForm",query:{id:e.questionId,type:e.type,topicClassification:e.topicClassification,content:e.content,options:e.options,analysis:e.analysis,answer:e.answer}})},K=e=>{re.confirm("确认要删除该题目吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await pe(e.questionId),s.success("删除成功"),p()}catch(t){console.error("删除题目失败:",t),s.error("删除失败，请重试")}}).catch(()=>{})},P=e=>{switch(e){case"单选题":return"success";case"简答题":return"primary";case"多选题":return"warning";case"填空题":return"danger";default:return"info"}},j=e=>{const t=B.value.find(r=>r.value===e);return t?t.label:e};return te(()=>{p(),U()}),oe(()=>{console.log("组件被激活，刷新列表数据"),p()}),(e,t)=>{const r=i("el-button"),v=i("el-upload"),d=i("el-input"),x=i("el-form-item"),S=i("el-option"),I=i("el-select"),$=i("el-form"),N=i("el-card"),f=i("el-table-column"),G=i("el-tag"),H=i("el-table"),J=i("pagination"),R=ae("loading");return _(),C("div",fe,[c("div",_e,[me,c("div",ye,[a(v,{class:"upload-btn",action:null,"http-request":Q,"show-file-list":!1,"before-upload":A,accept:".xlsx,.xls"},{default:l(()=>[a(r,{type:"primary",icon:"Upload"},{default:l(()=>[u("导入题目")]),_:1})]),_:1}),a(r,{type:"success",icon:"Plus",onClick:F},{default:l(()=>[u("添加新题目")]),_:1})])]),a(N,{class:"filter-container",shadow:"hover"},{default:l(()=>[a($,{inline:!0,model:n,class:"search-form"},{default:l(()=>[c("div",ge,[a(x,{label:"搜索题目"},{default:l(()=>[a(d,{modelValue:n.content,"onUpdate:modelValue":t[0]||(t[0]=o=>n.content=o),placeholder:"输入关键词搜索题目...",clearable:"","prefix-icon":"Search",onKeyup:le(w,["enter"])},null,8,["modelValue"])]),_:1}),a(x,{label:"类目"},{default:l(()=>[a(I,{modelValue:n.topicClassification,"onUpdate:modelValue":t[1]||(t[1]=o=>n.topicClassification=o),placeholder:"全部类目",clearable:"",class:"filter-select"},{default:l(()=>[(_(!0),C(D,null,L(h.value,o=>(_(),k(S,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(x,{label:"题型"},{default:l(()=>[a(I,{modelValue:n.type,"onUpdate:modelValue":t[2]||(t[2]=o=>n.type=o),placeholder:"全部题型",clearable:"",class:"filter-select"},{default:l(()=>[(_(!0),C(D,null,L(B.value,o=>(_(),k(S,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),c("div",ve,[a(r,{onClick:z},{default:l(()=>[u("重置筛选")]),_:1}),a(r,{type:"primary",onClick:w},{default:l(()=>[u("应用筛选")]),_:1})])]),_:1},8,["model"])]),_:1}),a(N,{class:"table-container",shadow:"hover"},{default:l(()=>[c("div",he,[be,c("div",we,"共 "+q(g.value)+" 个题目",1)]),M((_(),k(H,{data:E.value,"row-key":"questions_id",border:"",style:{width:"100%"}},{default:l(()=>[a(f,{type:"selection",width:"55",align:"center"}),a(f,{prop:"content",label:"题目","min-width":"300","show-overflow-tooltip":""}),a(f,{prop:"type",label:"类型",width:"180",align:"center"},{default:l(o=>[a(G,{type:P(o.row.type)},{default:l(()=>[u(q(j(o.row.type)),1)]),_:2},1032,["type"])]),_:1}),a(f,{prop:"topicClassification",label:"类目",width:"180",align:"center"},{default:l(o=>[u(q(o.row.topicClassification),1)]),_:1}),a(f,{prop:"createdAt",label:"创建时间",width:"180",align:"center"}),a(f,{label:"操作",width:"150",align:"center"},{default:l(o=>[a(r,{type:"primary",link:"",icon:"Edit",onClick:W=>O(o.row)},{default:l(()=>[u("编辑")]),_:2},1032,["onClick"]),a(r,{type:"danger",link:"",icon:"Delete",onClick:W=>K(o.row)},{default:l(()=>[u("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[R,b.value]]),M(a(J,{total:g.value,page:n.pageNum,"onUpdate:page":t[3]||(t[3]=o=>n.pageNum=o),limit:n.pageSize,"onUpdate:limit":t[4]||(t[4]=o=>n.pageSize=o),onPagination:p},null,8,["total","page","limit"]),[[ne,g.value>0]])]),_:1})])}}}),Ve=X(Ce,[["__scopeId","data-v-e2761b83"]]);export{Ve as default};
