<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">题库管理</h1>
      <div class="header-actions">
        <el-upload
          class="upload-btn"
          :action="null"
          :http-request="handleImport"
          :show-file-list="false"
          :before-upload="beforeImport"
          accept=".xlsx,.xls"
        >
          <el-button type="primary" icon="Upload">导入题目</el-button>
        </el-upload>
        <el-button type="success" icon="Plus" @click="handleAdd">添加题目</el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <div class="form-row">
          <el-form-item label="题目类型">
            <el-select v-model="queryParams.type" placeholder="所有类型" clearable class="filter-select">
              <el-option
                v-for="dict in questionTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="问卷类型">
            <el-select v-model="queryParams.labelId" placeholder="所有类型" clearable class="filter-select">
              <el-option
                v-for="dict in labelOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="题目内容">
            <el-input
              v-model="queryParams.content"
              placeholder="搜索题目内容..."
              clearable
              prefix-icon="Search"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 题目列表 -->
    <el-card class="table-container" shadow="hover">
      <el-table
        v-loading="loading"
        :data="questionList"
        row-key="questionsId"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="content" label="题目内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="type" label="题目类型" width="120" align="center">
          <template #default="scope">
            <el-tag
              :type="getQuestionTypeTag(scope.row.type)"
              effect="light"
            >
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="labelName" label="问卷类型" width="120" align="center">
          <template #default="scope">
            <el-tag
              :type="getLabelTypeTag(scope.row.labelName)"
              effect="light"
            >
              {{ scope.row.labelName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160" align="center" />
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button type="primary" link icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup name="QuestionBank">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { listQuestion,listLabel,deleteQuestion,importWjQuestions } from '@/api/aqsystem/questionnaire' 
const router = useRouter()


// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: undefined,
  labelId: undefined,
  content: undefined
})

// 题目类型选项
const questionTypeOptions = ref([
  { value: '单选题', label: '单选题' },
  { value: '多选题', label: '多选题' },
  { value: '判断题', label: '判断题' },
  { value: '填空题', label: '填空题' },
  { value: '简答题', label: '简答题' }
])

// 题目标签选项（问卷类型）
const labelOptions = ref([])

const loading = ref(false)
const total = ref(0)
const questionList = ref([])

// 获取题目列表
const getList = async () => {
  loading.value = true
  try {
    const response = await listQuestion({
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      type: queryParams.type,
      labelId: queryParams.labelId,
      content: queryParams.content
    })
    if (response.code === 200) {
      console.log('获取题目列表响应:', response)
      // 确保返回的数据格式正确
      if (response.data) {
        questionList.value = response.data.list || []
        total.value = response.data.total || 0
      } else {
        questionList.value = response.rows || []
        total.value = response.total || 0
      }
      console.log('分页数据:', {
        currentPage: queryParams.pageNum,
        pageSize: queryParams.pageSize,
        total: total.value,
        list: questionList.value
      })
    } else {
      ElMessage.error(response.msg || '获取题目列表失败')
    }
  } catch (error) {
    console.error('获取题目列表失败:', error)
    ElMessage.error('获取题目列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.pageNum = 1
  queryParams.type = undefined
  queryParams.labelId = undefined
  queryParams.content = undefined
  handleQuery()
}

// 分页大小改变
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  queryParams.pageNum = 1 // 切换每页条数时，重置为第一页
  getList()
}

// 页码改变
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 导入前校验
const beforeImport = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('文件大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理文件导入
const handleImport = async (options) => {
  const file = options.file
  if (file) {
    const formData = new FormData()
    formData.append('file', file)
    try {
      const response = await importWjQuestions(formData)
      if (response.code === 0) {
        ElMessage.success('导入成功')
        getList() // 刷新列表
      } else {
        ElMessage.error(response.msg || '导入失败')
      }
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败，请重试')
    }
  }
}

// 添加题目
const handleAdd = () => {
  router.push('/aqsystem/questionnaires/questionForm1')
}

// 修改题目
const handleUpdate = (row) => {
  router.push({
    path: '/aqsystem/questionnaires/questionForm1',
    query: { id: row.questionsId }
  })
}

// 删除题目
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该题目吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteQuestion(row.questionsId)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 获取题目类型对应的标签类型
const getQuestionTypeTag = (type) => {
  const typeMap = {
    '单选题': 'primary',
    '多选题': 'success',
    '判断题': 'warning',
    '填空题': 'info',
    '简答题': 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取问卷类型对应的标签类型
const getLabelTypeTag = (labelName) => {
  const labelMap = {
    '收集问卷': 'primary',
    '个性化问卷': 'success'
  }
  return labelMap[labelName] || 'info'
}

// 获取问卷类型列表
const getLabelList = async () => {
  try {
    const response = await listLabel()
    if (response.code === 200) {
      // 检查返回的数据结构
      console.log('问卷类型列表响应:', response)
      if (response.rows) {
        labelOptions.value = response.rows.map(item => ({
          value: item.labelId,
          label: item.labelName
        }))
      } else if (response.data) {
        labelOptions.value = response.data.map(item => ({
          value: item.id,
          label: item.name
        }))
      } else {
        ElMessage.warning('问卷类型列表数据格式不正确')
      }
    } else {
      ElMessage.error(response.msg || '获取问卷类型列表失败')
    }
  } catch (error) {
    console.error('获取问卷类型列表失败:', error)
    ElMessage.error('获取问卷类型列表失败')
  }
}

onMounted(() => {
  getList()
  getLabelList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-container {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  align-items: center;
}

.filter-select {
  width: 180px;
}

.table-container {
  margin-top: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>


