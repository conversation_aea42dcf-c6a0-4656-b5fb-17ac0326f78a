import{_ as v,e as i,c,o,i as t,k as n,f as l,h as r,t as a,n as f,v as k,x as b}from"./index-BJsoK47l.js";const s=e=>(k("data-v-163c99ee"),e=e(),b(),e),g={class:"feedback-detail"},m={class:"feedback-section"},p=s(()=>t("div",{class:"section-title"},"总体评分",-1)),y={class:"score-display"},C={class:"progress-content"},S={class:"progress-value"},w=s(()=>t("span",{class:"progress-label"},"分",-1)),x={key:0,class:"feedback-section"},I=s(()=>t("div",{class:"section-title"},"面试结果",-1)),N={class:"result-display"},V={key:1,class:"feedback-section"},B=s(()=>t("div",{class:"section-title"},"面试反馈",-1)),D={class:"feedback-content"},F={key:2,class:"feedback-section"},A=s(()=>t("div",{class:"section-title"},"优势表现",-1)),E={class:"feedback-content"},T={key:3,class:"feedback-section"},j=s(()=>t("div",{class:"section-title"},"改进建议",-1)),q={class:"feedback-content"},z={__name:"InterviewFeedbackDetail",props:{score:{type:Number,default:0},feedback:{type:String,default:""},strengths:{type:String,default:""},improvements:{type:String,default:""},result:{type:Number,default:null}},setup(e){const _=d=>d<60?"#F56C6C":d<80?"#E6A23C":"#67C23A";return(d,G)=>{const h=i("el-progress"),u=i("el-tag");return o(),c("div",g,[t("div",m,[p,t("div",y,[l(h,{type:"dashboard",percentage:e.score,color:_(e.score),"stroke-width":8,width:80},{default:r(()=>[t("div",C,[t("span",S,a(e.score),1),w])]),_:1},8,["percentage","color"])])]),e.result!==null?(o(),c("div",x,[I,t("div",N,[l(u,{type:e.result===1?"success":"danger"},{default:r(()=>[f(a(e.result===1?"面试通过":"面试未通过"),1)]),_:1},8,["type"])])])):n("",!0),e.feedback?(o(),c("div",V,[B,t("div",D,a(e.feedback),1)])):n("",!0),e.strengths?(o(),c("div",F,[A,t("div",E,a(e.strengths),1)])):n("",!0),e.improvements?(o(),c("div",T,[j,t("div",q,a(e.improvements),1)])):n("",!0)])}}},J=v(z,[["__scopeId","data-v-163c99ee"]]);export{J as default};
