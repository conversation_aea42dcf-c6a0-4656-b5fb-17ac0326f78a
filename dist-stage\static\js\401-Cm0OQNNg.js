import{_ as h,d as f,r as m,e as n,c as g,f as t,h as o,n as _,i as e,l as k,v as x,x as w,o as b}from"./index-BJsoK47l.js";const I="/stage/static/gif/401-HGF6Q5qM.gif",c=s=>(x("data-v-7afd82d1"),s=s(),w(),s),v={class:"errPage-container"},y=c(()=>e("h1",{class:"text-jumbo text-ginormous"}," 401错误! ",-1)),B=c(()=>e("h2",null,"您没有访问权限！",-1)),C=c(()=>e("h6",null,"对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面",-1)),G={class:"list-unstyled"},N={class:"link-type"},S=["src"],V={__name:"401",setup(s){let{proxy:a}=f();const l=m(I+"?"+ +new Date);function i(){a.$route.query.noGoBack?a.$router.push({path:"/"}):a.$router.go(-1)}return($,q)=>{const d=n("el-button"),u=n("router-link"),r=n("el-col"),p=n("el-row");return b(),g("div",v,[t(d,{icon:"arrow-left",class:"pan-back-btn",onClick:i},{default:o(()=>[_(" 返回 ")]),_:1}),t(p,null,{default:o(()=>[t(r,{span:12},{default:o(()=>[y,B,C,e("ul",G,[e("li",N,[t(u,{to:"/"},{default:o(()=>[_(" 回首页 ")]),_:1})])])]),_:1}),t(r,{span:12},{default:o(()=>[e("img",{src:k(l),width:"313",height:"428",alt:"Girl has dropped her ice cream."},null,8,S)]),_:1})]),_:1})])}}},D=h(V,[["__scopeId","data-v-7afd82d1"]]);export{D as default};
