import{Z as B,_ as lt,B as at,d as rt,r as I,C as ct,a5 as dt,e as D,G as Ee,c as n,o as i,J as R,f as o,a4 as xe,l as t,h as u,m as Pe,n as h,j as A,D as Y,i as e,k as v,H as w,I as $,t as r,P as ut,cc as _t,cd as mt,ce as ht,v as pt,x as vt}from"./index-BJsoK47l.js";import ft from"./Template4-CvzdWm4c.js";import gt from"./Template5-DIGd6RAR.js";import"./style-CI2cvTa4.js";function kt(C){return B({url:"/resume/list",method:"post",data:{title:C.title||"",name:C.name||""}})}function yt(C){return B({url:"/system/resume",method:"post",data:C})}function bt(C){return B({url:"/system/resume",method:"put",data:C})}function Te(C,y){return B({url:`/resume/audit/${C}`,method:"put",data:y})}function Lt(C){return B({url:`/resume/detail/${C}`,method:"get"})}const _=C=>(pt("data-v-92b10927"),C=C(),vt(),C),wt={class:"app-container"},$t={style:{padding:"0 50px"}},Vt=_(()=>e("h4",null,"教育经历",-1)),Ct=_(()=>e("h4",null,"工作经历",-1)),jt=_(()=>e("h4",null,"项目经验",-1)),Dt=_(()=>e("h4",null,"实践经历",-1)),It=_(()=>e("h4",null,"技能列表",-1)),qt=_(()=>e("h4",null,"证书列表",-1)),Et=_(()=>e("h4",null,"校园经历",-1)),xt=_(()=>e("h4",null,"兴趣爱好",-1)),Pt=_(()=>e("h4",null,"自我评价",-1)),Tt={key:0,class:"detail-list"},St=_(()=>e("h4",null,"已添加的内容：",-1)),Nt={class:"detail-index"},Ot={class:"detail-content"},Ht={key:0,class:"questions-list"},Mt={class:"question-header"},Rt={class:"question-index"},Qt={class:"question-text"},Ut={class:"question-actions"},At={key:0,class:"question-answer"},Bt={style:{"white-space":"pre-wrap"}},Ft={key:1,class:"question-editor"},zt={class:"detail-input"},Kt={class:"button-group"},Gt={class:"detail-actions"},Jt={class:"dialog-footer"},Zt={key:0,class:"audit-opinion-display"},Wt=_(()=>e("h4",null,"审核意见：",-1)),Xt={class:"audit-opinion-content"},Yt={key:1,class:"resume-template template-1"},es={class:"resume-header"},ts={class:"resume-title"},ss={class:"resume-contact"},os={key:0,class:"contact-item"},is={key:1,class:"contact-item"},ns={key:2,class:"contact-item"},ls={key:3,class:"contact-item"},as={key:0,class:"avatar-container"},rs=["src"],cs={class:"resume-content"},ds={key:0,class:"resume-section"},us=_(()=>e("div",{class:"section-header"},[e("h2",null,"教育经历")],-1)),_s={class:"education-content"},ms={class:"education-header"},hs={class:"edu-date"},ps={class:"edu-school"},vs={class:"edu-degree"},fs={key:0},gs={class:"education-details"},ks={key:0,class:"edu-courses"},ys={key:1,class:"resume-section"},bs=_(()=>e("div",{class:"section-header"},[e("h2",null,"工作经验")],-1)),Ls={class:"work-content"},ws={class:"work-header"},$s={class:"work-date"},Vs={class:"work-company"},Cs={class:"work-position"},js=["innerHTML"],Ds={key:2,class:"resume-section"},Is=_(()=>e("div",{class:"section-header"},[e("h2",null,"项目经验")],-1)),qs={class:"section-content"},Es={class:"project-header"},xs={class:"project-title"},Ps={class:"project-date"},Ts={class:"project-role"},Ss=["innerHTML"],Ns={key:3,class:"resume-section"},Os=_(()=>e("div",{class:"section-header"},[e("h2",null,"技能特长")],-1)),Hs={class:"skills-content"},Ms=["innerHTML"],Rs={key:4,class:"resume-section"},Qs=_(()=>e("div",{class:"section-header"},[e("h2",null,"证书列表")],-1)),Us={class:"certificate-content"},As={key:5,class:"resume-section"},Bs=_(()=>e("div",{class:"section-header"},[e("h2",null,"校园经历")],-1)),Fs={class:"campus-content"},zs={key:6,class:"resume-section"},Ks=_(()=>e("div",{class:"section-header"},[e("h2",null,"兴趣爱好")],-1)),Gs={class:"interests-content"},Js={key:7,class:"resume-section"},Zs=_(()=>e("div",{class:"section-header"},[e("h2",null,"自我评价")],-1)),Ws={class:"evaluation-content"},Xs={key:2,class:"resume-template template-3"},Ys={class:"resume-container"},eo={class:"header-content"},to={class:"header-text"},so={class:"resume-title"},oo={class:"basic-info-section"},io={class:"basic-info-grid"},no={class:"info-item"},lo=_(()=>e("span",{class:"info-label"},"姓名：",-1)),ao={class:"info-value"},ro={class:"info-item"},co=_(()=>e("span",{class:"info-label"},"年龄：",-1)),uo={class:"info-value"},_o={class:"info-item"},mo=_(()=>e("span",{class:"info-label"},"电话：",-1)),ho={class:"info-value"},po={class:"info-item"},vo=_(()=>e("span",{class:"info-label"},"邮箱：",-1)),fo={class:"info-value"},go={class:"basic-info-grid"},ko={class:"info-item"},yo=_(()=>e("span",{class:"info-label"},"性别：",-1)),bo={class:"info-value"},Lo={key:0,class:"info-item"},wo=_(()=>e("span",{class:"info-label"},"籍贯：",-1)),$o={class:"info-value"},Vo={key:0,class:"avatar-container"},Co=["src"],jo={key:0,class:"resume-section"},Do=_(()=>e("div",{class:"section-header"},[e("h2",null,"教育经历")],-1)),Io={class:"education-content"},qo={class:"edu-header"},Eo={class:"edu-date"},xo={class:"edu-school"},Po={class:"edu-info"},To={key:0,class:"edu-courses"},So=_(()=>e("span",{class:"courses-label"},"主修课程：",-1)),No={key:1,class:"resume-section"},Oo=_(()=>e("div",{class:"section-header"},[e("h2",null,"工作经验")],-1)),Ho={class:"work-content"},Mo={class:"work-header"},Ro={class:"work-time"},Qo={class:"work-company"},Uo={class:"work-position"},Ao=["innerHTML"],Bo={key:2,class:"resume-section"},Fo=_(()=>e("div",{class:"section-header"},[e("h2",null,"项目经验")],-1)),zo={class:"work-content"},Ko={class:"work-header"},Go={class:"project-date"},Jo={class:"project-title"},Zo={class:"project-role"},Wo=["innerHTML"],Xo={key:3,class:"resume-section"},Yo=_(()=>e("div",{class:"section-header"},[e("h2",null,"技能特长")],-1)),ei={class:"skills-content"},ti={class:"skills-description"},si={class:"skill-description-body"},oi={key:4,class:"resume-section"},ii=_(()=>e("div",{class:"section-header"},[e("h2",null,"荣誉证书")],-1)),ni={class:"certificate-content"},li={key:5,class:"resume-section"},ai=_(()=>e("div",{class:"section-header"},[e("h2",null,"校园经历")],-1)),ri={class:"campus-content"},ci={key:6,class:"resume-section"},di=_(()=>e("div",{class:"section-header"},[e("h2",null,"兴趣爱好")],-1)),ui={class:"interests-content"},_i={key:7,class:"resume-section"},mi=_(()=>e("div",{class:"section-header"},[e("h2",null,"个人评价")],-1)),hi={class:"evaluation-content"},pi={key:3,class:"resume-template template-4"},vi={class:"resume-container"},fi={class:"header-section"},gi={class:"personal-info"},ki={class:"info-name"},yi={class:"info-row"},bi={class:"info-item"},Li=_(()=>e("span",{class:"label"},"性别：",-1)),wi={class:"info-item"},$i=_(()=>e("span",{class:"label"},"年龄：",-1)),Vi={class:"info-row"},Ci={class:"info-item"},ji=_(()=>e("span",{class:"label"},"电话：",-1)),Di={class:"info-item"},Ii=_(()=>e("span",{class:"label"},"邮箱：",-1)),qi={class:"info-row"},Ei={class:"info-item full-width"},xi=_(()=>e("span",{class:"label"},"籍贯：",-1)),Pi={key:0,class:"avatar-container",style:{width:"85px",height:"110px"}},Ti=["src"],Si={key:0,class:"section"},Ni=_(()=>e("div",{class:"section-header"},[e("h2",null,"自我评价")],-1)),Oi={class:"evaluation-content"},Hi=["innerHTML"],Mi={key:1,class:"section"},Ri=_(()=>e("div",{class:"section-header"},[e("h2",null,"工作经验")],-1)),Qi={class:"section-content"},Ui={class:"experience-header"},Ai={class:"experience-date"},Bi={class:"experience-company"},Fi={class:"experience-position"},zi=["innerHTML"],Ki={key:2,class:"section"},Gi=_(()=>e("div",{class:"section-header"},[e("h2",null,"项目经验")],-1)),Ji={class:"section-content"},Zi={class:"project-header"},Wi={class:"project-title"},Xi={class:"project-role"},Yi={class:"project-date"},en=["innerHTML"],tn={key:3,class:"section"},sn=_(()=>e("div",{class:"section-header"},[e("h2",null,"相关技能")],-1)),on={class:"section-content skills-content"},nn={class:"skills-list"},ln={class:"skill-name"},an=["innerHTML"],rn={key:4,class:"section"},cn=_(()=>e("div",{class:"section-header"},[e("h2",null,"教育背景")],-1)),dn={class:"section-content"},un={class:"education-header"},_n={class:"education-school"},mn={class:"education-date"},hn={key:0,class:"dialog-footer"},pn={class:"audit-opinion-container",style:{"margin-bottom":"15px"}},vn={key:0,class:"el-form-item__error"},fn={key:1,class:"dialog-footer"},gn=at({name:"Resume"}),kn=Object.assign(gn,{setup(C){const{proxy:y}=rt(),Q=I([]),O=I(!1),M=I(!0),F=I(!0),Se=I([]),Ne=I(!0),Oe=I(!0),U=I(0),ee=I(""),E=I(!1),X=I(!1),s=I({}),z=I(!1),He=ct({form:{},queryParams:{pageNum:1,pageSize:10,userId:null,title:null,createAt:null,isDelete:null},rules:{userId:[{required:!0,message:"用户ID不能为空",trigger:"blur"}],title:[{required:!0,message:"简历标题不能为空",trigger:"blur"}]}}),{queryParams:q,form:V,rules:Me}=dt(He);function S(){M.value=!0;const l={title:q.value.title,name:q.value.userId};kt(l).then(a=>{a.data&&Array.isArray(a.data)?(Q.value=a.data,U.value=a.total||a.data.length,console.log("简历列表数据加载成功:",Q.value)):(console.error("简历列表数据格式不正确:",a),Q.value=[],U.value=0),M.value=!1}).catch(a=>{console.error("获取简历列表失败:",a),Q.value=[],U.value=0,M.value=!1})}function Re(){O.value=!1,te()}function te(){console.log("重置表单前的数据:",{form:V.value,tempContents:tempContents.value}),V.value={proId:null,name:null,startTime:null,endTime:null,catName:null,content:null,segments:[]},tempContents.value=tempContents.value.map(l=>({...l,questions:[],currentQuestion:"",showEditor:!1})),console.log("重置表单后的数据:",{form:V.value,tempContents:tempContents.value}),y.resetForm("projectRef")}function K(){q.value.pageNum=1,S()}function Qe(){y.resetForm("queryRef"),K()}function Ue(l){Se.value=l.map(a=>a.resumeId),Ne.value=l.length!=1,Oe.value=!l.length}function Ae(){te(),O.value=!0,ee.value="添加简历主"}function Be(){y.$refs.resumeRef.validate(l=>{l&&(V.value.resumeId!=null?bt(V.value).then(a=>{y.$modal.msgSuccess("修改成功"),O.value=!1,S()}):yt(V.value).then(a=>{y.$modal.msgSuccess("新增成功"),O.value=!1,S()}))})}function Fe(l){X.value=!0,E.value=!0,z.value=!1,Lt(l.resumeVo.resumeId).then(a=>{a.code===0?(s.value=a.data,s.value.auditOpinion=s.value.resumeVo.auditOpinion||""):(y.$modal.msgError(a.message||"获取简历详情失败"),E.value=!1)}).catch(a=>{y.$modal.msgError("获取简历详情失败"),E.value=!1}).finally(()=>{X.value=!1})}function ze(){y.$modal.confirm("您确定审核通过吗？","审核确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const l=s.value.resumeVo.resumeId;Te(l,{status:1,auditOpinion:s.value.auditOpinion||""}).then(a=>{a.code===0?(y.$modal.msgSuccess("审核通过"),E.value=!1,S()):y.$modal.msgError(a.message||"审核失败")}).catch(a=>{y.$modal.msgError("审核失败")})}).catch(()=>{})}function Ke(){if(!s.value.auditOpinion){z.value=!0;return}y.$modal.confirm("您确定审核拒绝吗？","审核确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const l=s.value.resumeVo.resumeId;Te(l,{status:2,auditOpinion:s.value.auditOpinion}).then(a=>{a.code===0?(y.$modal.msgSuccess("审核拒绝成功"),E.value=!1,S()):y.$modal.msgError(a.message||"审核失败")}).catch(a=>{y.$modal.msgError("审核失败")})}).catch(()=>{})}function Ge(l){switch(l){case 0:return"info";case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}}function Je(l){switch(l){case 0:return"未审核";case 1:return"审核通过";case 2:return"审核未通过";case 3:return"已删除";default:return"未知状态"}}function Ze(l){if(!l.currentQuestion||!l.currentQuestion.trim()){y.$modal.msgError("项目问题不能为空");return}l.questions||(l.questions=[]),l.questions.push({queId:null,question:l.currentQuestion,questionOrder:l.questions.length+1}),l.currentQuestion="",y.$modal.msgSuccess("添加问题成功")}function se(l,a){y.$modal.confirm("是否确认删除该条项目问题？").then(()=>{l.questions.splice(a,1),l.questions.forEach((k,g)=>{k.questionOrder=g+1}),y.$modal.msgSuccess("删除成功")}).catch(()=>{})}function We(l,a,k){l.currentQuestion=a.question,l.showEditor=!0,se(l,k)}function Xe(l){console.log("开始生成问题，内容:",l),y.$modal.confirm("是否确认生成项目问题？").then(()=>{M.value=!0,request({url:"http://localhost:7091/ai/chatAPI/contentQuestion",method:"post",data:{content:l.text},timeout:6e4}).then(a=>{console.log("生成问题接口返回数据:",a);let k;try{let g=a;typeof a=="string"&&(g=a.replace(/^```json\s*/,"").replace(/\s*```$/,"")),k=typeof g=="string"?JSON.parse(g):g,console.log("解析后的responseData:",k)}catch(g){console.error("解析响应数据失败:",g),y.$modal.msgError("生成问题失败：数据格式不正确");return}k&&k.questions&&Array.isArray(k.questions)?(l.questions||(l.questions=[]),k.questions.forEach(g=>{console.log("item.questions isExtensible before push:",Object.isExtensible(l.questions)),l.questions.push({queId:null,question:g.question,answer:g.answer,questionOrder:l.questions.length+1})}),y.$modal.msgSuccess("生成问题成功")):(console.log("返回的数据格式不正确，responseData:",k),y.$modal.msgError("生成问题失败：数据格式不正确"))}).catch(a=>{console.error("生成问题错误:",a),y.$modal.msgError("生成问题失败")}).finally(()=>{M.value=!1})}).catch(()=>{})}function Ye(l){l.showEditor=!0}function et(l){l.showEditor=!1}const N=l=>!l||typeof l=="object"?"":l.includes("<")&&l.includes(">")?l:l.replace(/^### (.*?)$/gm,"<h3>$1</h3>").replace(/^## (.*?)$/gm,"<h2>$1</h2>").replace(/^# (.*?)$/gm,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^\- (.*?)$/gm,"<li>$1</li>").replace(/(<\/li>\n<li>)/g,"</li><li>").replace(/(<li>.*?<\/li>)/gs,"<ul>$1</ul>").replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>").replace(/^(.+)$/,"<p>$1</p>"),tt=l=>{if(!l||typeof l=="object")return"";if(l.includes("<")&&l.includes(">"))return l;let a=l.replace(/^\- (.*?)$/gm,"<li>$1</li>").replace(/(<\/li>\n<li>)/g,"</li><li>").replace(/(<li>.*?<\/li>)/gs,"<ul>$1</ul>");a=a.replace(/^### (.*?)$/gm,"<h3>$1</h3>").replace(/^## (.*?)$/gm,"<h2>$1</h2>").replace(/^# (.*?)$/gm,"<h1>$1</h1>"),a=a.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>");const k=a.split(`
`);let g="",L=1;return k.forEach(x=>{!x.trim()||x.match(/^<\/?[a-z][\s\S]*>$/i)?g+=x+`
`:g+=`<span class="line-number">${L++}.</span> ${x}
`}),a=g.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>").replace(/^(.+)$/,"<p>$1</p>"),a};function oe(l){var a,k,g,L,x,G,J;return{modules:{basic:{name:((a=l.information)==null?void 0:a.name)||"",gender:((k=l.information)==null?void 0:k.gender)||"",age:((g=l.information)==null?void 0:g.birthDate)||"",phone:((L=l.information)==null?void 0:L.phone)||"",email:((x=l.information)==null?void 0:x.email)||"",jobObjective:"",avatar:((G=l.information)==null?void 0:G.avatar)||""},work:(l.workList||[]).map(f=>({...f,time:f.timePeriod?[f.timePeriod,f.timePeriod]:["",""]})),projects:(l.projectList||[]).map(f=>({...f,name:f.projectName,time:f.timePeriod?[f.timePeriod,f.timePeriod]:["",""]})),practices:(l.practiceList||[]).map(f=>({...f,name:f.projectName,time:f.timePeriod?[f.timePeriod,f.timePeriod]:["",""]})),skills:(l.talentList||[]).map(f=>({description:f.skillDescription||""})),education:(l.educationList||[]).map(f=>({school:f.school,degree:f.education,major:f.major,time:f.timePeriod?[f.timePeriod,f.timePeriod]:["",""],courses:f.mainCourses||""})),certificates:(l.certificateList||[]).map(f=>({certificateName:f.certificateName||""})),campus:l.campusList||[],interests:l.interestList||[],selfEvaluation:l.evaluateList&&((J=l.evaluateList[0])==null?void 0:J.selfEvaluation)||""}}}return S(),(l,a)=>{const k=D("el-input"),g=D("el-form-item"),L=D("el-button"),x=D("el-form"),G=D("el-col"),J=D("right-toolbar"),f=D("el-row"),p=D("el-table-column"),P=D("el-table"),st=D("el-collapse-item"),ot=D("el-collapse"),it=D("el-tag"),nt=D("pagination"),ie=D("el-dialog"),Z=D("el-icon"),ne=Ee("hasPermi"),le=Ee("loading");return i(),n("div",wt,[R(o(x,{model:t(q),ref:"queryRef",inline:!0,"label-width":"68px"},{default:u(()=>[o(g,{label:"创建人",prop:"userId"},{default:u(()=>[o(k,{modelValue:t(q).userId,"onUpdate:modelValue":a[0]||(a[0]=d=>t(q).userId=d),placeholder:"请输入创建人",clearable:"",onKeyup:Pe(K,["enter"])},null,8,["modelValue"])]),_:1}),o(g,{label:"简历标题",prop:"title"},{default:u(()=>[o(k,{modelValue:t(q).title,"onUpdate:modelValue":a[1]||(a[1]=d=>t(q).title=d),placeholder:"请输入简历标题",clearable:"",onKeyup:Pe(K,["enter"])},null,8,["modelValue"])]),_:1}),o(g,null,{default:u(()=>[o(L,{type:"primary",icon:"Search",onClick:K},{default:u(()=>[h("搜索")]),_:1}),o(L,{icon:"Refresh",onClick:Qe},{default:u(()=>[h("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[xe,t(F)]]),o(f,{gutter:10,class:"mb8"},{default:u(()=>[o(G,{span:1.5},{default:u(()=>[R((i(),A(L,{type:"primary",plain:"",icon:"Plus",onClick:Ae},{default:u(()=>[h("新增")]),_:1})),[[ne,["system:resume:add"]]])]),_:1}),o(J,{showSearch:t(F),"onUpdate:showSearch":a[2]||(a[2]=d=>Y(F)?F.value=d:null),onQueryTable:S},null,8,["showSearch"])]),_:1}),R((i(),A(P,{data:t(Q),onSelectionChange:Ue},{default:u(()=>[o(p,{type:"expand"},{default:u(d=>[e("div",$t,[Vt,o(P,{data:d.row.educationList,"show-header":!1},{default:u(()=>[o(p,{label:"学校",prop:"school"}),o(p,{label:"专业",prop:"major"}),o(p,{label:"学历",prop:"education"}),o(p,{label:"时间段",prop:"timePeriod"}),o(p,{label:"主修课程",prop:"mainCourses"})]),_:2},1032,["data"]),Ct,o(P,{data:d.row.workList,"show-header":!1},{default:u(()=>[o(p,{label:"公司",prop:"company"}),o(p,{label:"职位",prop:"position"}),o(p,{label:"时间段",prop:"timePeriod"}),o(p,{label:"工作描述",prop:"workDescription"})]),_:2},1032,["data"]),jt,o(P,{data:d.row.projectList,"show-header":!1},{default:u(()=>[o(p,{label:"项目名称",prop:"projectName"}),o(p,{label:"时间段",prop:"timePeriod"}),o(p,{label:"职位类型",prop:"positionType"}),o(p,{label:"角色",prop:"role"}),o(p,{label:"项目描述",prop:"projectDescription"})]),_:2},1032,["data"]),Dt,o(P,{data:d.row.practiceList,"show-header":!1},{default:u(()=>[o(p,{label:"时间段",prop:"timePeriod"}),o(p,{label:"项目名称",prop:"projectName"}),o(p,{label:"角色",prop:"role"}),o(p,{label:"项目描述",prop:"projectDescription"}),o(p,{label:"项目链接",prop:"projectUrl"})]),_:2},1032,["data"]),It,o(P,{data:d.row.talentList,"show-header":!1},{default:u(()=>[o(p,{label:"技能名称",prop:"skillName"}),o(p,{label:"熟练度",prop:"proficiency"}),o(p,{label:"技能描述",prop:"skillDescription"})]),_:2},1032,["data"]),qt,o(P,{data:d.row.certificateList,"show-header":!1},{default:u(()=>[o(p,{label:"证书名称",prop:"certificateName"})]),_:2},1032,["data"]),Et,o(P,{data:d.row.campusList,"show-header":!1},{default:u(()=>[o(p,{label:"经历描述",prop:"campusExperience"})]),_:2},1032,["data"]),xt,o(P,{data:d.row.interestList,"show-header":!1},{default:u(()=>[o(p,{label:"爱好描述",prop:"interest"})]),_:2},1032,["data"]),Pt,(i(!0),n(w,null,$(d.row.evaluateList,(b,T)=>(i(),n("div",{key:T,style:{"margin-bottom":"10px"}},[e("p",null,r(b.selfEvaluation),1)]))),128)),l.tempContents&&l.tempContents.length>0?(i(),n("div",Tt,[St,(i(!0),n(w,null,$(l.tempContents,(b,T)=>(i(),n("div",{key:T,class:"detail-item"},[e("div",Nt,r(T+1)+".",1),e("div",Ot,[e("div",null,r(b.text),1),b.questions&&b.questions.length>0?(i(),n("div",Ht,[(i(!0),n(w,null,$(b.questions,(j,H)=>(i(),n("div",{key:H,class:"question-item"},[e("div",Mt,[e("span",Rt,r(H+1)+".",1),e("span",Qt,r(j.question),1)]),e("div",Ut,[o(L,{link:"",type:"primary",onClick:W=>We(b,j,H)},{default:u(()=>[h("编辑")]),_:2},1032,["onClick"]),o(L,{link:"",type:"danger",onClick:W=>se(b,H)},{default:u(()=>[h("删除1")]),_:2},1032,["onClick"])]),j.answer?(i(),n("div",At,[o(ot,null,{default:u(()=>[o(st,{title:"查看答案"},{default:u(()=>[e("div",Bt,r(j.answer),1)]),_:2},1024)]),_:2},1024)])):v("",!0)]))),128))])):v("",!0),b.showEditor?(i(),n("div",Ft,[e("div",zt,[o(k,{modelValue:b.currentQuestion,"onUpdate:modelValue":j=>b.currentQuestion=j,type:"textarea",rows:4,placeholder:"请输入问题内容"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Kt,[o(L,{type:"primary",onClick:j=>Ze(b)},{default:u(()=>[h("添加问题")]),_:2},1032,["onClick"]),o(L,{type:"success",onClick:j=>Xe(b)},{default:u(()=>[h("生成问题")]),_:2},1032,["onClick"]),o(L,{onClick:j=>et(b)},{default:u(()=>[h("关闭")]),_:2},1032,["onClick"])])])])):v("",!0)]),e("div",Gt,[o(L,{link:"",type:"primary",onClick:j=>l.handleEditContent(b,T)},{default:u(()=>[h("编辑内容")]),_:2},1032,["onClick"]),o(L,{link:"",type:"primary",onClick:j=>Ye(b)},{default:u(()=>[h("编辑问题")]),_:2},1032,["onClick"]),o(L,{link:"",type:"danger",onClick:j=>l.handleRemoveContent(T)},{default:u(()=>[h("删除内容")]),_:2},1032,["onClick"])])]))),128))])):v("",!0)])]),_:1}),o(p,{type:"selection",width:"55",align:"center"}),o(p,{label:"简历ID",align:"center",prop:"resumeId",width:"80"},{default:u(d=>[h(r(d.row.resumeVo.resumeId),1)]),_:1}),o(p,{label:"姓名",align:"center",prop:"userName",width:"70"},{default:u(d=>[h(r(d.row.information.name),1)]),_:1}),o(p,{label:"生日",align:"center",prop:"birthDate"},{default:u(d=>[h(r(d.row.information.birthDate),1)]),_:1}),o(p,{label:"性别",align:"center",prop:"gender",width:"50"},{default:u(d=>[h(r(d.row.information.gender),1)]),_:1}),o(p,{label:"手机号",align:"center",prop:"phone"},{default:u(d=>[h(r(d.row.information.phone),1)]),_:1}),o(p,{label:"邮箱",align:"center",prop:"email"},{default:u(d=>[h(r(d.row.information.email),1)]),_:1}),o(p,{label:"居住地",align:"center",prop:"hometown"},{default:u(d=>[h(r(d.row.information.hometown),1)]),_:1}),o(p,{label:"简历标题",align:"center",prop:"title"},{default:u(d=>[h(r(d.row.resumeVo.title),1)]),_:1}),o(p,{label:"简历模板",align:"center",prop:"templateId",width:"80"},{default:u(d=>[h(r(d.row.resumeVo.templateId),1)]),_:1}),o(p,{label:"状态",align:"center",prop:"status",width:"100"},{default:u(d=>[o(it,{type:Ge(d.row.resumeVo.status)},{default:u(()=>[h(r(Je(d.row.resumeVo.status)),1)]),_:2},1032,["type"])]),_:1}),o(p,{label:"创建者",align:"center",prop:"createAt"},{default:u(d=>[h(r(d.row.information.createAt),1)]),_:1}),o(p,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:u(d=>[R((i(),A(L,{link:"",type:"primary",icon:"Check",onClick:b=>Fe(d.row)},{default:u(()=>[h(r(d.row.resumeVo.status===0?"审核":"查看"),1)]),_:2},1032,["onClick"])),[[ne,["system:resume:audit"]]])]),_:1})]),_:1},8,["data"])),[[le,t(M)]]),R(o(nt,{total:t(U),page:t(q).pageNum,"onUpdate:page":a[3]||(a[3]=d=>t(q).pageNum=d),limit:t(q).pageSize,"onUpdate:limit":a[4]||(a[4]=d=>t(q).pageSize=d),onPagination:S},null,8,["total","page","limit"]),[[xe,t(U)>0]]),o(ie,{title:t(ee),modelValue:t(O),"onUpdate:modelValue":a[11]||(a[11]=d=>Y(O)?O.value=d:null),width:"500px","append-to-body":""},{footer:u(()=>[e("div",Jt,[o(L,{type:"primary",onClick:Be},{default:u(()=>[h("确 定")]),_:1}),o(L,{onClick:Re},{default:u(()=>[h("取 消")]),_:1})])]),default:u(()=>[o(x,{ref:"resumeRef",model:t(V),rules:t(Me),"label-width":"80px"},{default:u(()=>[o(g,{label:"用户ID",prop:"userId"},{default:u(()=>[o(k,{modelValue:t(V).userId,"onUpdate:modelValue":a[5]||(a[5]=d=>t(V).userId=d),placeholder:"请输入用户ID"},null,8,["modelValue"])]),_:1}),o(g,{label:"简历标题",prop:"title"},{default:u(()=>[o(k,{modelValue:t(V).title,"onUpdate:modelValue":a[6]||(a[6]=d=>t(V).title=d),placeholder:"请输入简历标题"},null,8,["modelValue"])]),_:1}),o(g,{label:"认是否默简历(0-否,1-是)",prop:"isDefault"},{default:u(()=>[o(k,{modelValue:t(V).isDefault,"onUpdate:modelValue":a[7]||(a[7]=d=>t(V).isDefault=d),placeholder:"请输入认是否默简历(0-否,1-是)"},null,8,["modelValue"])]),_:1}),o(g,{label:"简历模板ID",prop:"templateId"},{default:u(()=>[o(k,{modelValue:t(V).templateId,"onUpdate:modelValue":a[8]||(a[8]=d=>t(V).templateId=d),placeholder:"请输入简历模板ID"},null,8,["modelValue"])]),_:1}),o(g,{label:"创建人",prop:"createAt"},{default:u(()=>[o(k,{modelValue:t(V).createAt,"onUpdate:modelValue":a[9]||(a[9]=d=>t(V).createAt=d),placeholder:"请输入创建人"},null,8,["modelValue"])]),_:1}),o(g,{label:"删除标识(0-未删除,1-已删除)",prop:"isDelete"},{default:u(()=>[o(k,{modelValue:t(V).isDelete,"onUpdate:modelValue":a[10]||(a[10]=d=>t(V).isDelete=d),placeholder:"请输入删除标识(0-未删除,1-已删除)"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),o(ie,{title:"简历审核",modelValue:t(E),"onUpdate:modelValue":a[15]||(a[15]=d=>Y(E)?E.value=d:null),width:"800px","append-to-body":""},{footer:u(()=>{var d;return[((d=t(s).resumeVo)==null?void 0:d.status)===0?(i(),n("div",hn,[e("div",pn,[o(k,{modelValue:t(s).auditOpinion,"onUpdate:modelValue":a[12]||(a[12]=b=>t(s).auditOpinion=b),type:"textarea",rows:3,placeholder:"请输入审核意见",required:t(z)},null,8,["modelValue","required"]),t(z)&&!t(s).auditOpinion?(i(),n("div",vn," 审核拒绝时，审核意见为必填 ")):v("",!0)]),o(L,{type:"primary",onClick:ze},{default:u(()=>[h("审核通过")]),_:1}),o(L,{type:"danger",onClick:Ke},{default:u(()=>[h("审核拒绝")]),_:1}),o(L,{onClick:a[13]||(a[13]=b=>E.value=!1)},{default:u(()=>[h("取 消")]),_:1})])):(i(),n("div",fn,[o(L,{onClick:a[14]||(a[14]=b=>E.value=!1)},{default:u(()=>[h("关 闭")]),_:1})]))]}),default:u(()=>{var d,b,T,j,H,W,ae,re,ce,de,ue,_e,me,he,pe,ve,fe,ge,ke,ye,be,Le,we,$e,Ve,Ce,je,De,Ie,qe;return[R((i(),n("div",null,[((d=t(s).resumeVo)==null?void 0:d.status)!==0&&((b=t(s).resumeVo)!=null&&b.auditOpinion)?(i(),n("div",Zt,[Wt,e("div",Xt,r(t(s).resumeVo.auditOpinion),1)])):v("",!0),((T=t(s).resumeVo)==null?void 0:T.templateId)===1?(i(),n("div",Yt,[e("div",es,[e("div",ts,[e("h1",null,r(((j=t(s).information)==null?void 0:j.name)||"求职者"),1)]),e("div",ss,[(H=t(s).information)!=null&&H.birthDate?(i(),n("div",os,[o(Z,null,{default:u(()=>[o(t(ut))]),_:1}),h(" "+r(t(s).information.birthDate),1)])):v("",!0),(W=t(s).information)!=null&&W.phone?(i(),n("div",is,[o(Z,null,{default:u(()=>[o(t(_t))]),_:1}),h(" "+r(t(s).information.phone),1)])):v("",!0),(ae=t(s).information)!=null&&ae.email?(i(),n("div",ns,[o(Z,null,{default:u(()=>[o(t(mt))]),_:1}),h(" "+r(t(s).information.email),1)])):v("",!0),(re=t(s).information)!=null&&re.hometown?(i(),n("div",ls,[o(Z,null,{default:u(()=>[o(t(ht))]),_:1}),h(" "+r(t(s).information.hometown),1)])):v("",!0)]),(ce=t(s).information)!=null&&ce.avatar?(i(),n("div",as,[e("img",{class:"avatar",src:t(s).information.avatar,alt:"头像",style:{width:"100%",height:"100%"}},null,8,rs)])):v("",!0)]),e("div",cs,[t(s).educationList&&t(s).educationList.length>0?(i(),n("div",ds,[us,e("div",_s,[(i(!0),n(w,null,$(t(s).educationList,(c,m)=>(i(),n("div",{key:m,class:"education-item"},[e("div",ms,[e("div",hs,r(c.timePeriod),1),e("div",ps,r(c.school),1),e("div",vs,[h(r(c.major)+" ",1),c.education?(i(),n("span",fs,"（"+r(c.education)+"）",1)):v("",!0)])]),e("div",gs,[c.mainCourses?(i(),n("div",ks,"主修课程："+r(c.mainCourses),1)):v("",!0)])]))),128))])])):v("",!0),t(s).workList&&t(s).workList.length>0?(i(),n("div",ys,[bs,e("div",Ls,[(i(!0),n(w,null,$(t(s).workList,(c,m)=>(i(),n("div",{key:m,class:"work-item"},[e("div",ws,[e("div",$s,r(c.timePeriod),1),e("div",Vs,r(c.company),1),e("div",Cs,r(c.position),1)]),e("div",{class:"work-description",innerHTML:N(c.workDescription)},null,8,js)]))),128))])])):v("",!0),t(s).projectList&&t(s).projectList.length>0?(i(),n("div",Ds,[Is,e("div",qs,[(i(!0),n(w,null,$(t(s).projectList,(c,m)=>(i(),n("div",{key:m,class:"project-item"},[e("div",Es,[e("div",xs,r(c.projectName),1),e("div",Ps,r(c.timePeriod),1)]),e("div",Ts,r(c.role),1),e("div",{class:"project-description",innerHTML:N(c.projectDescription)},null,8,Ss)]))),128))])])):v("",!0),t(s).talentList&&t(s).talentList.length>0?(i(),n("div",Ns,[Os,e("div",Hs,[(i(!0),n(w,null,$(t(s).talentList,(c,m)=>(i(),n("div",{key:m,class:"skill-description-item"},[e("div",{class:"skill-description-text",innerHTML:N(c.skillDescription)},null,8,Ms)]))),128))])])):v("",!0),t(s).certificateList&&t(s).certificateList.length>0?(i(),n("div",Rs,[Qs,e("div",Us,[e("ul",null,[(i(!0),n(w,null,$(t(s).certificateList,(c,m)=>(i(),n("li",{key:m},r(c.certificateName),1))),128))])])])):v("",!0),t(s).campusList&&t(s).campusList.length>0?(i(),n("div",As,[Bs,e("div",Fs,[e("ul",null,[(i(!0),n(w,null,$(t(s).campusList,(c,m)=>(i(),n("li",{key:m},r(c.campusExperience),1))),128))])])])):v("",!0),t(s).interestList&&t(s).interestList.length>0?(i(),n("div",zs,[Ks,e("div",Gs,[e("ul",null,[(i(!0),n(w,null,$(t(s).interestList,(c,m)=>(i(),n("li",{key:m},r(c.interest),1))),128))])])])):v("",!0),t(s).evaluateList&&t(s).evaluateList.length>0?(i(),n("div",Js,[Zs,e("div",Ws,[(i(!0),n(w,null,$(t(s).evaluateList,(c,m)=>(i(),n("div",{key:m},r(c.selfEvaluation),1))),128))])])):v("",!0)])])):((de=t(s).resumeVo)==null?void 0:de.templateId)===2?(i(),n("div",Xs,[e("div",Ys,[e("div",null,[e("div",eo,[e("div",to,[e("div",so,r(((ue=t(s).information)==null?void 0:ue.name)||"姓名"),1),e("div",oo,[e("div",io,[e("div",no,[lo,e("span",ao,r(((_e=t(s).information)==null?void 0:_e.name)||""),1)]),e("div",ro,[co,e("span",uo,r(((me=t(s).information)==null?void 0:me.birthDate)||""),1)]),e("div",_o,[mo,e("span",ho,r(((he=t(s).information)==null?void 0:he.phone)||""),1)]),e("div",po,[vo,e("span",fo,r(((pe=t(s).information)==null?void 0:pe.email)||""),1)])]),e("div",go,[e("div",ko,[yo,e("span",bo,r(((ve=t(s).information)==null?void 0:ve.gender)||""),1)]),(fe=t(s).information)!=null&&fe.hometown?(i(),n("div",Lo,[wo,e("span",$o,r((ge=t(s).information)==null?void 0:ge.hometown),1)])):v("",!0)])])]),(ke=t(s).information)!=null&&ke.avatar?(i(),n("div",Vo,[e("img",{class:"avatar",src:t(s).information.avatar,alt:"头像",style:{width:"100%",height:"100%"}},null,8,Co)])):v("",!0)])])]),t(s).educationList&&t(s).educationList.length>0?(i(),n("div",jo,[Do,e("div",Io,[(i(!0),n(w,null,$(t(s).educationList,(c,m)=>(i(),n("div",{key:m,class:"education-item"},[e("div",qo,[e("div",Eo,r(c.timePeriod),1),e("div",xo,r(c.school),1),e("div",null,r(c.education)+"，"+r(c.major),1)]),e("div",Po,[c.mainCourses?(i(),n("div",To,[So,h(r(c.mainCourses),1)])):v("",!0)])]))),128))])])):v("",!0),t(s).workList&&t(s).workList.length>0?(i(),n("div",No,[Oo,e("div",Ho,[(i(!0),n(w,null,$(t(s).workList,(c,m)=>(i(),n("div",{key:m,class:"work-item"},[e("div",Mo,[e("div",Ro,r(c.timePeriod),1),e("div",Qo,r(c.company),1),e("div",Uo,r(c.position),1)]),e("div",{class:"work-description",innerHTML:N(c.workDescription)},null,8,Ao)]))),128))])])):v("",!0),t(s).projectList&&t(s).projectList.length>0?(i(),n("div",Bo,[Fo,e("div",zo,[(i(!0),n(w,null,$(t(s).projectList,(c,m)=>(i(),n("div",{key:m,class:"project-item"},[e("div",Ko,[e("div",Go,r(c.timePeriod),1),e("div",Jo,r(c.projectName),1),e("div",Zo,r(c.role),1)]),e("div",{class:"project-description",innerHTML:N(c.projectDescription)},null,8,Wo)]))),128))])])):v("",!0),t(s).talentList&&t(s).talentList.length>0?(i(),n("div",Xo,[Yo,e("div",ei,[e("div",ti,[(i(!0),n(w,null,$(t(s).talentList,(c,m)=>(i(),n("div",{key:"desc-"+m,class:"skill-description-item"},[e("div",si,r(m+1)+". "+r(c.skillDescription),1)]))),128))])])])):v("",!0),t(s).certificateList&&t(s).certificateList.length>0?(i(),n("div",oi,[ii,e("div",ni,[e("ul",null,[(i(!0),n(w,null,$(t(s).certificateList,(c,m)=>(i(),n("li",{key:m},r(c.certificateName),1))),128))])])])):v("",!0),t(s).campusList&&t(s).campusList.length>0?(i(),n("div",li,[ai,e("div",ri,[e("ul",null,[(i(!0),n(w,null,$(t(s).campusList,(c,m)=>(i(),n("li",{key:m},r(c.campusExperience),1))),128))])])])):v("",!0),t(s).interestList&&t(s).interestList.length>0?(i(),n("div",ci,[di,e("div",ui,[e("ul",null,[(i(!0),n(w,null,$(t(s).interestList,(c,m)=>(i(),n("li",{key:m},r(c.interest),1))),128))])])])):v("",!0),t(s).evaluateList&&t(s).evaluateList.length>0?(i(),n("div",_i,[mi,e("div",hi,[(i(!0),n(w,null,$(t(s).evaluateList,(c,m)=>(i(),n("div",{key:m},r(c.selfEvaluation),1))),128))])])):v("",!0)])):((ye=t(s).resumeVo)==null?void 0:ye.templateId)===3?(i(),n("div",pi,[e("div",vi,[e("div",fi,[e("div",gi,[e("div",ki,r(((be=t(s).information)==null?void 0:be.name)||""),1),e("div",yi,[e("div",bi,[Li,h(r(((Le=t(s).information)==null?void 0:Le.gender)||"未填写"),1)]),e("div",wi,[$i,h(r(((we=t(s).information)==null?void 0:we.birthDate)||"未填写"),1)])]),e("div",Vi,[e("div",Ci,[ji,h(r((($e=t(s).information)==null?void 0:$e.phone)||"未填写"),1)]),e("div",Di,[Ii,h(r(((Ve=t(s).information)==null?void 0:Ve.email)||"未填写"),1)])]),e("div",qi,[e("div",Ei,[xi,h(r(((Ce=t(s).information)==null?void 0:Ce.hometown)||"未填写"),1)])])]),(je=t(s).information)!=null&&je.avatar?(i(),n("div",Pi,[e("img",{class:"avatar",src:t(s).information.avatar,alt:"头像",style:{width:"100%",height:"100%"}},null,8,Ti)])):v("",!0)]),t(s).evaluateList&&t(s).evaluateList.length>0?(i(),n("div",Si,[Ni,e("div",Oi,[e("div",{class:"evaluation-text",innerHTML:tt((De=t(s).evaluateList[0])==null?void 0:De.selfEvaluation)},null,8,Hi)])])):v("",!0),t(s).workList&&t(s).workList.length>0?(i(),n("div",Mi,[Ri,e("div",Qi,[(i(!0),n(w,null,$(t(s).workList,(c,m)=>(i(),n("div",{key:m,class:"experience-item"},[e("div",Ui,[e("div",Ai,r(c.timePeriod),1),e("div",Bi,r(c.company),1),e("div",Fi,r(c.position),1)]),e("div",{class:"experience-description",innerHTML:N(c.workDescription)},null,8,zi)]))),128))])])):v("",!0),t(s).projectList&&t(s).projectList.length>0?(i(),n("div",Ki,[Gi,e("div",Ji,[(i(!0),n(w,null,$(t(s).projectList,(c,m)=>(i(),n("div",{key:m,class:"project-item"},[e("div",Zi,[e("div",Wi,r(c.projectName),1),e("div",Xi,r(c.role),1),e("div",Yi,r(c.timePeriod),1)]),e("div",{class:"project-description",innerHTML:N(c.projectDescription)},null,8,en)]))),128))])])):v("",!0),t(s).talentList&&t(s).talentList.length>0?(i(),n("div",tn,[sn,e("div",on,[e("div",nn,[(i(!0),n(w,null,$(t(s).talentList,(c,m)=>(i(),n("div",{key:m,class:"skill-item"},[e("div",ln,"• 熟练 "+r(c.skillName)+"：",1),e("div",{class:"skill-description",innerHTML:N(c.skillDescription)},null,8,an)]))),128))])])])):v("",!0),t(s).educationList&&t(s).educationList.length>0?(i(),n("div",rn,[cn,e("div",dn,[(i(!0),n(w,null,$(t(s).educationList,(c,m)=>(i(),n("div",{key:m,class:"education-item"},[e("div",un,[e("div",_n,r(c.school)+"("+r(c.education)+")",1),e("div",mn,r(c.timePeriod),1)])]))),128))])])):v("",!0)])])):((Ie=t(s).resumeVo)==null?void 0:Ie.templateId)===4?(i(),A(ft,{key:4,resume:oe(t(s))},null,8,["resume"])):((qe=t(s).resumeVo)==null?void 0:qe.templateId)===5?(i(),A(gt,{key:5,resume:oe(t(s))},null,8,["resume"])):v("",!0)])),[[le,t(X)]])]}),_:1},8,["modelValue"])])}}}),$n=lt(kn,[["__scopeId","data-v-92b10927"]]);export{$n as default};
