import{B as le,d as ae,r as p,C as te,a5 as re,e as d,G as z,c as oe,J as f,f as l,a4 as F,l as a,h as t,m as se,n as c,o as b,j as y,D as L,i as ne}from"./index-BJsoK47l.js";import{l as ie,g as ue,d as de,u as me,a as pe}from"./supervisor-CpA1uLH3.js";const ce={class:"app-container"},ve={class:"dialog-footer"},fe=le({name:"Supervisor"}),he=Object.assign(fe,{setup(ge){const{proxy:i}=ae(),U=p([]),g=p(!1),S=p(!0),w=p(!0),C=p([]),E=p(!0),q=p(!0),k=p(0),D=p(""),V=p(!1),Q=te({form:{},queryParams:{pageNum:1,pageSize:10,supervisorName:null,department:null,title:null,email:null,phoneNumber:null,editTime:null,isDelete:null},rules:{supervisorName:[{required:!0,message:"导师姓名不能为空",trigger:"blur"}],department:[{required:!0,message:"所属院系不能为空",trigger:"change"}],title:[{required:!0,message:"职称，如教授、副教授等不能为空",trigger:"blur"}],email:[{required:!0,message:"导师邮箱，具有唯一性不能为空",trigger:"blur"}]}}),{queryParams:v,form:n,rules:j}=re(Q);function h(){S.value=!0,ie(v.value).then(s=>{s.code===200?(U.value=s.data.topics||[],k.value=s.data.total||0):i.$modal.msgError(s.message||"获取导师列表失败"),S.value=!1}).catch(s=>{console.error("获取导师列表错误:",s),i.$modal.msgError("获取导师列表失败"),S.value=!1})}function K(){g.value=!1,I()}function I(){n.value={supervisorId:null,supervisorName:null,department:null,title:null,email:null,phoneNumber:null,createTime:null,editTime:null,isDelete:null},i.resetForm("supervisorRef")}function $(){v.value.pageNum=1,h()}function O(){i.resetForm("queryRef"),$()}function A(s){C.value=s.map(e=>e.supervisorId),E.value=s.length!=1,q.value=!s.length}function G(){I(),g.value=!0,D.value="添加导师信息，用于记录导师的基本资料"}function x(s){I();const e=s.supervisorId||C.value;ue(e).then(r=>{r.code===200?(n.value={supervisorId:r.data.supervisor.supervisorId,supervisorName:r.data.supervisor.supervisorName,department:r.data.supervisor.department,title:r.data.supervisor.title,email:r.data.supervisor.email,phoneNumber:r.data.supervisor.phoneNumber,createTime:r.data.supervisor.createTime,editTime:r.data.supervisor.editTime,isDelete:r.data.supervisor.isDelete},g.value=!0,D.value="修改导师信息"):i.$modal.msgError(r.message||"获取导师信息失败")}).catch(r=>{console.error("获取导师信息错误:",r),i.$modal.msgError("获取导师信息失败")})}function J(){V.value||i.$refs.supervisorRef.validate(s=>{if(s){V.value=!0;const e=new Date().toISOString().slice(0,19);if(n.value.supervisorId!=null){const r={...n.value,editTime:e};me(r).then(u=>{i.$modal.msgSuccess("修改成功"),g.value=!1,h()}).catch(u=>{i.$modal.msgError(u.message||"修改失败")}).finally(()=>{V.value=!1})}else{const r={...n.value,createTime:e,editTime:e};pe(r).then(u=>{i.$modal.msgSuccess("新增成功"),g.value=!1,h()}).catch(u=>{i.$modal.msgError(u.message||"新增失败")}).finally(()=>{V.value=!1})}}})}function R(s){const e=s.supervisorId||C.value;i.$modal.confirm('是否确认删除导师信息编号为"'+e+'"的数据项？').then(function(){return de(e)}).then(()=>{h(),i.$modal.msgSuccess("删除成功")}).catch(()=>{})}function H(s){const e=s.supervisorId;if(!e){i.$modal.msgError("获取导师ID失败");return}i.$router.push({name:"SupervisorDetail",params:{supervisorId:e}})}return h(),(s,e)=>{const r=d("el-input"),u=d("el-form-item"),m=d("el-button"),P=d("el-form"),T=d("el-col"),M=d("right-toolbar"),W=d("el-row"),_=d("el-table-column"),X=d("el-table"),Y=d("pagination"),Z=d("el-dialog"),N=z("hasPermi"),ee=z("loading");return b(),oe("div",ce,[f(l(P,{model:a(v),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[l(u,{label:"导师姓名",prop:"supervisorName"},{default:t(()=>[l(r,{modelValue:a(v).supervisorName,"onUpdate:modelValue":e[0]||(e[0]=o=>a(v).supervisorName=o),placeholder:"请输入导师姓名",clearable:"",onKeyup:se($,["enter"])},null,8,["modelValue"])]),_:1}),l(u,null,{default:t(()=>[l(m,{type:"primary",icon:"Search",onClick:$},{default:t(()=>[c("搜索")]),_:1}),l(m,{icon:"Refresh",onClick:O},{default:t(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[F,a(w)]]),l(W,{gutter:10,class:"mb8"},{default:t(()=>[l(T,{span:1.5},{default:t(()=>[f((b(),y(m,{type:"primary",plain:"",icon:"Plus",onClick:G},{default:t(()=>[c("新增")]),_:1})),[[N,["system:supervisor:add"]]])]),_:1}),l(T,{span:1.5},{default:t(()=>[f((b(),y(m,{type:"success",plain:"",icon:"Edit",disabled:a(E),onClick:x},{default:t(()=>[c("修改")]),_:1},8,["disabled"])),[[N,["system:supervisor:edit"]]])]),_:1}),l(T,{span:1.5},{default:t(()=>[f((b(),y(m,{type:"danger",plain:"",icon:"Delete",disabled:a(q),onClick:R},{default:t(()=>[c("删除")]),_:1},8,["disabled"])),[[N,["system:supervisor:remove"]]])]),_:1}),l(M,{showSearch:a(w),"onUpdate:showSearch":e[1]||(e[1]=o=>L(w)?w.value=o:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),f((b(),y(X,{data:a(U),onSelectionChange:A},{default:t(()=>[l(_,{type:"selection",width:"55",align:"center"}),l(_,{label:"导师ID",align:"center",prop:"supervisorId"}),l(_,{label:"导师姓名",align:"center",prop:"supervisorName"}),l(_,{label:"所属院系",align:"center",prop:"department"}),l(_,{label:"职称",align:"center",prop:"title"}),l(_,{label:"邮箱",align:"center",prop:"email"}),l(_,{label:"电话",align:"center",prop:"phoneNumber"}),l(_,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(o=>[f((b(),y(m,{link:"",type:"primary",icon:"View",onClick:B=>H(o.row)},{default:t(()=>[c("详情")]),_:2},1032,["onClick"])),[[N,["system:supervisor:query"]]]),f((b(),y(m,{link:"",type:"primary",icon:"Edit",onClick:B=>x(o.row)},{default:t(()=>[c("修改")]),_:2},1032,["onClick"])),[[N,["system:supervisor:edit"]]]),f((b(),y(m,{link:"",type:"primary",icon:"Delete",onClick:B=>R(o.row)},{default:t(()=>[c("删除")]),_:2},1032,["onClick"])),[[N,["system:supervisor:remove"]]])]),_:1})]),_:1},8,["data"])),[[ee,a(S)]]),f(l(Y,{total:a(k),page:a(v).pageNum,"onUpdate:page":e[2]||(e[2]=o=>a(v).pageNum=o),limit:a(v).pageSize,"onUpdate:limit":e[3]||(e[3]=o=>a(v).pageSize=o),onPagination:h},null,8,["total","page","limit"]),[[F,a(k)>0]]),l(Z,{title:a(D),modelValue:a(g),"onUpdate:modelValue":e[9]||(e[9]=o=>L(g)?g.value=o:null),width:"500px","append-to-body":""},{footer:t(()=>[ne("div",ve,[l(m,{type:"primary",loading:a(V),onClick:J},{default:t(()=>[c("确 定")]),_:1},8,["loading"]),l(m,{onClick:K},{default:t(()=>[c("取 消")]),_:1})])]),default:t(()=>[l(P,{ref:"supervisorRef",model:a(n),rules:a(j),"label-width":"80px"},{default:t(()=>[l(u,{label:"导师姓名",prop:"supervisorName"},{default:t(()=>[l(r,{modelValue:a(n).supervisorName,"onUpdate:modelValue":e[4]||(e[4]=o=>a(n).supervisorName=o),placeholder:"请输入导师姓名"},null,8,["modelValue"])]),_:1}),l(u,{label:"所属院系",prop:"department"},{default:t(()=>[l(r,{modelValue:a(n).department,"onUpdate:modelValue":e[5]||(e[5]=o=>a(n).department=o),placeholder:"请输入所属院系"},null,8,["modelValue"])]),_:1}),l(u,{label:"职称",prop:"title"},{default:t(()=>[l(r,{modelValue:a(n).title,"onUpdate:modelValue":e[6]||(e[6]=o=>a(n).title=o),placeholder:"请输入职称"},null,8,["modelValue"])]),_:1}),l(u,{label:"邮箱",prop:"email"},{default:t(()=>[l(r,{modelValue:a(n).email,"onUpdate:modelValue":e[7]||(e[7]=o=>a(n).email=o),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),l(u,{label:"电话",prop:"phoneNumber"},{default:t(()=>[l(r,{modelValue:a(n).phoneNumber,"onUpdate:modelValue":e[8]||(e[8]=o=>a(n).phoneNumber=o),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{he as default};
