import{af as Ut,ag as $t,ah as qt,ai as Zt,aj as Jt,ak as Qt,al as en,H as tn,am as nn,an as on,ao as an,ap as rn,aq as sn,ar as ln,as as un,at as cn,au as fn,av as dn,aw as hn,ax as pn,ay as gn,az as mn,aA as vn,aB as bn,aC as yn,aD as wn,z as En,aE as Sn,j as Dn,k as _n,c as Cn,i as Tn,aF as On,aG as An,aH as In,aI as Nn,aJ as Mn,A as Pn,n as xn,f as Rn,aK as Fn,aL as kn,B as Bn,aM as Hn,aN as Xn,aO as Yn,aP as Gn,aQ as Ln,aR as jn,aS as Kn,aT as zn,aU as Wn,aV as Vn,aW as Un,d as $n,aX as qn,aY as Zn,aZ as Jn,a_ as Qn,a$ as eo,b0 as to,b1 as no,b2 as oo,b3 as io,b4 as ao,b5 as ro,b6 as so,b7 as lo,b8 as uo,D as co,b9 as fo,ba as ho,bb as po,bc as go,bd as mo,be as vo,bf as bo,a6 as yo,M as wo,bg as Eo,L as So,a3 as Do,bh as _o,bi as Co,bj as To,bk as Oo,bl as Ao,F as Io,bm as No,bn as Mo,bo as Po,bp as xo,bq as Ro,br as Fo,o as ko,x as Bo,bs as Ho,bt as Xo,v as Yo,bu as Go,C as Lo,bv as jo,r as Ko,bw as zo,bx as Wo,I as Vo,by as Uo,e as $o,G as qo,bz as Zo,bA as Jo,bB as Qo,bC as ei,bD as ti,bE as ni,bF as oi,bG as ii,bH as ai,bI as ri,bJ as si,bK as li,t as ui,bL as ci,bM as fi,bN as di,bO as hi,a5 as pi,bP as gi,bQ as mi,bR as vi,l as bi,bS as yi,bT as wi,bU as Ei,bV as Si,bW as Di,bX as _i,bY as Ci,bZ as Ti,b_ as Oi,b$ as Ai,c0 as Ii,c1 as Ni,a4 as Mi,c2 as Pi,c3 as xi,w as Ri,c4 as Fi,c5 as ki,c6 as Bi,c7 as Hi,h as Xi,c8 as Yi,J as Gi,m as Li,c9 as ji,p as Ki,ca as zi,cb as Rt}from"./index-BJsoK47l.js";/**
* vue v3.4.31
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Wi=()=>{},Vi=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Ut,BaseTransitionPropsValidators:$t,Comment:qt,DeprecationTypes:Zt,EffectScope:Jt,ErrorCodes:Qt,ErrorTypeStrings:en,Fragment:tn,KeepAlive:nn,ReactiveEffect:on,Static:an,Suspense:rn,Teleport:sn,Text:ln,TrackOpTypes:un,Transition:cn,TransitionGroup:fn,TriggerOpTypes:dn,VueElement:hn,assertNumber:pn,callWithAsyncErrorHandling:gn,callWithErrorHandling:mn,camelize:vn,capitalize:bn,cloneVNode:yn,compatUtils:wn,compile:Wi,computed:En,createApp:Sn,createBlock:Dn,createCommentVNode:_n,createElementBlock:Cn,createElementVNode:Tn,createHydrationRenderer:On,createPropsRestProxy:An,createRenderer:In,createSSRApp:Nn,createSlots:Mn,createStaticVNode:Pn,createTextVNode:xn,createVNode:Rn,customRef:Fn,defineAsyncComponent:kn,defineComponent:Bn,defineCustomElement:Hn,defineEmits:Xn,defineExpose:Yn,defineModel:Gn,defineOptions:Ln,defineProps:jn,defineSSRCustomElement:Kn,defineSlots:zn,devtools:Wn,effect:Vn,effectScope:Un,getCurrentInstance:$n,getCurrentScope:qn,getTransitionRawChildren:Zn,guardReactiveProps:Jn,h:Qn,handleError:eo,hasInjectionContext:to,hydrate:no,initCustomFormatter:oo,initDirectivesForSSR:io,inject:ao,isMemoSame:ro,isProxy:so,isReactive:lo,isReadonly:uo,isRef:co,isRuntimeOnly:fo,isShallow:ho,isVNode:po,markRaw:go,mergeDefaults:mo,mergeModels:vo,mergeProps:bo,nextTick:yo,normalizeClass:wo,normalizeProps:Eo,normalizeStyle:So,onActivated:Do,onBeforeMount:_o,onBeforeUnmount:Co,onBeforeUpdate:To,onDeactivated:Oo,onErrorCaptured:Ao,onMounted:Io,onRenderTracked:No,onRenderTriggered:Mo,onScopeDispose:Po,onServerPrefetch:xo,onUnmounted:Ro,onUpdated:Fo,openBlock:ko,popScopeId:Bo,provide:Ho,proxyRefs:Xo,pushScopeId:Yo,queuePostFlushCb:Go,reactive:Lo,readonly:jo,ref:Ko,registerRuntimeCompiler:zo,render:Wo,renderList:Vo,renderSlot:Uo,resolveComponent:$o,resolveDirective:qo,resolveDynamicComponent:Zo,resolveFilter:Jo,resolveTransitionHooks:Qo,setBlockTracking:ei,setDevtoolsHook:ti,setTransitionHooks:ni,shallowReactive:oi,shallowReadonly:ii,shallowRef:ai,ssrContextKey:ri,ssrUtils:si,stop:li,toDisplayString:ui,toHandlerKey:ci,toHandlers:fi,toRaw:di,toRef:hi,toRefs:pi,toValue:gi,transformVNodeArgs:mi,triggerRef:vi,unref:bi,useAttrs:yi,useCssModule:wi,useCssVars:Ei,useModel:Si,useSSRContext:Di,useSlots:_i,useTransitionState:Ci,vModelCheckbox:Ti,vModelDynamic:Oi,vModelRadio:Ai,vModelSelect:Ii,vModelText:Ni,vShow:Mi,version:Pi,warn:xi,watch:Ri,watchEffect:Fi,watchPostEffect:ki,watchSyncEffect:Bi,withAsyncContext:Hi,withCtx:Xi,withDefaults:Yi,withDirectives:Gi,withKeys:Li,withMemo:ji,withModifiers:Ki,withScopeId:zi},Symbol.toStringTag,{value:"Module"})),Pa=Rt(Vi);/**!
 * Sortable 1.14.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Dt(o,e){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(o);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),t.push.apply(t,n)}return t}function Q(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Dt(Object(t),!0).forEach(function(n){Ui(o,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):Dt(Object(t)).forEach(function(n){Object.defineProperty(o,n,Object.getOwnPropertyDescriptor(t,n))})}return o}function We(o){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?We=function(e){return typeof e}:We=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(o)}function Ui(o,e,t){return e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}function W(){return W=Object.assign||function(o){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(o[n]=t[n])}return o},W.apply(this,arguments)}function $i(o,e){if(o==null)return{};var t={},n=Object.keys(o),i,a;for(a=0;a<n.length;a++)i=n[a],!(e.indexOf(i)>=0)&&(t[i]=o[i]);return t}function qi(o,e){if(o==null)return{};var t=$i(o,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(o);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(o,n)&&(t[n]=o[n])}return t}function Zi(o){return Ji(o)||Qi(o)||ea(o)||ta()}function Ji(o){if(Array.isArray(o))return ht(o)}function Qi(o){if(typeof Symbol<"u"&&o[Symbol.iterator]!=null||o["@@iterator"]!=null)return Array.from(o)}function ea(o,e){if(o){if(typeof o=="string")return ht(o,e);var t=Object.prototype.toString.call(o).slice(8,-1);if(t==="Object"&&o.constructor&&(t=o.constructor.name),t==="Map"||t==="Set")return Array.from(o);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ht(o,e)}}function ht(o,e){(e==null||e>o.length)&&(e=o.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=o[t];return n}function ta(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var na="1.14.0";function te(o){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(o)}var ne=te(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Be=te(/Edge/i),_t=te(/firefox/i),Pe=te(/safari/i)&&!te(/chrome/i)&&!te(/android/i),Ft=te(/iP(ad|od|hone)/i),oa=te(/chrome/i)&&te(/android/i),kt={capture:!1,passive:!1};function w(o,e,t){o.addEventListener(e,t,!ne&&kt)}function y(o,e,t){o.removeEventListener(e,t,!ne&&kt)}function Ze(o,e){if(e){if(e[0]===">"&&(e=e.substring(1)),o)try{if(o.matches)return o.matches(e);if(o.msMatchesSelector)return o.msMatchesSelector(e);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(e)}catch{return!1}return!1}}function ia(o){return o.host&&o!==document&&o.host.nodeType?o.host:o.parentNode}function $(o,e,t,n){if(o){t=t||document;do{if(e!=null&&(e[0]===">"?o.parentNode===t&&Ze(o,e):Ze(o,e))||n&&o===t)return o;if(o===t)break}while(o=ia(o))}return null}var Ct=/\s+/g;function O(o,e,t){if(o&&e)if(o.classList)o.classList[t?"add":"remove"](e);else{var n=(" "+o.className+" ").replace(Ct," ").replace(" "+e+" "," ");o.className=(n+(t?" "+e:"")).replace(Ct," ")}}function h(o,e,t){var n=o&&o.style;if(n){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(o,""):o.currentStyle&&(t=o.currentStyle),e===void 0?t:t[e];!(e in n)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),n[e]=t+(typeof t=="string"?"":"px")}}function he(o,e){var t="";if(typeof o=="string")t=o;else do{var n=h(o,"transform");n&&n!=="none"&&(t=n+" "+t)}while(!e&&(o=o.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(t)}function Bt(o,e,t){if(o){var n=o.getElementsByTagName(e),i=0,a=n.length;if(t)for(;i<a;i++)t(n[i],i);return n}return[]}function J(){var o=document.scrollingElement;return o||document.documentElement}function T(o,e,t,n,i){if(!(!o.getBoundingClientRect&&o!==window)){var a,r,s,l,u,d,f;if(o!==window&&o.parentNode&&o!==J()?(a=o.getBoundingClientRect(),r=a.top,s=a.left,l=a.bottom,u=a.right,d=a.height,f=a.width):(r=0,s=0,l=window.innerHeight,u=window.innerWidth,d=window.innerHeight,f=window.innerWidth),(e||t)&&o!==window&&(i=i||o.parentNode,!ne))do if(i&&i.getBoundingClientRect&&(h(i,"transform")!=="none"||t&&h(i,"position")!=="static")){var p=i.getBoundingClientRect();r-=p.top+parseInt(h(i,"border-top-width")),s-=p.left+parseInt(h(i,"border-left-width")),l=r+a.height,u=s+a.width;break}while(i=i.parentNode);if(n&&o!==window){var E=he(i||o),b=E&&E.a,S=E&&E.d;E&&(r/=S,s/=b,f/=b,d/=S,l=r+d,u=s+f)}return{top:r,left:s,bottom:l,right:u,width:f,height:d}}}function Tt(o,e,t){for(var n=se(o,!0),i=T(o)[e];n;){var a=T(n)[t],r=void 0;if(r=i>=a,!r)return n;if(n===J())break;n=se(n,!1)}return!1}function ye(o,e,t,n){for(var i=0,a=0,r=o.children;a<r.length;){if(r[a].style.display!=="none"&&r[a]!==g.ghost&&(n||r[a]!==g.dragged)&&$(r[a],t.draggable,o,!1)){if(i===e)return r[a];i++}a++}return null}function bt(o,e){for(var t=o.lastElementChild;t&&(t===g.ghost||h(t,"display")==="none"||e&&!Ze(t,e));)t=t.previousElementSibling;return t||null}function N(o,e){var t=0;if(!o||!o.parentNode)return-1;for(;o=o.previousElementSibling;)o.nodeName.toUpperCase()!=="TEMPLATE"&&o!==g.clone&&(!e||Ze(o,e))&&t++;return t}function Ot(o){var e=0,t=0,n=J();if(o)do{var i=he(o),a=i.a,r=i.d;e+=o.scrollLeft*a,t+=o.scrollTop*r}while(o!==n&&(o=o.parentNode));return[e,t]}function aa(o,e){for(var t in o)if(o.hasOwnProperty(t)){for(var n in e)if(e.hasOwnProperty(n)&&e[n]===o[t][n])return Number(t)}return-1}function se(o,e){if(!o||!o.getBoundingClientRect)return J();var t=o,n=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var i=h(t);if(t.clientWidth<t.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return J();if(n||e)return t;n=!0}}while(t=t.parentNode);return J()}function ra(o,e){if(o&&e)for(var t in e)e.hasOwnProperty(t)&&(o[t]=e[t]);return o}function ot(o,e){return Math.round(o.top)===Math.round(e.top)&&Math.round(o.left)===Math.round(e.left)&&Math.round(o.height)===Math.round(e.height)&&Math.round(o.width)===Math.round(e.width)}var xe;function Ht(o,e){return function(){if(!xe){var t=arguments,n=this;t.length===1?o.call(n,t[0]):o.apply(n,t),xe=setTimeout(function(){xe=void 0},e)}}}function sa(){clearTimeout(xe),xe=void 0}function Xt(o,e,t){o.scrollLeft+=e,o.scrollTop+=t}function yt(o){var e=window.Polymer,t=window.jQuery||window.Zepto;return e&&e.dom?e.dom(o).cloneNode(!0):t?t(o).clone(!0)[0]:o.cloneNode(!0)}function At(o,e){h(o,"position","absolute"),h(o,"top",e.top),h(o,"left",e.left),h(o,"width",e.width),h(o,"height",e.height)}function it(o){h(o,"position",""),h(o,"top",""),h(o,"left",""),h(o,"width",""),h(o,"height","")}var k="Sortable"+new Date().getTime();function la(){var o=[],e;return{captureAnimationState:function(){if(o=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(h(i,"display")==="none"||i===g.ghost)){o.push({target:i,rect:T(i)});var a=Q({},o[o.length-1].rect);if(i.thisAnimationDuration){var r=he(i,!0);r&&(a.top-=r.f,a.left-=r.e)}i.fromRect=a}})}},addAnimationState:function(n){o.push(n)},removeAnimationState:function(n){o.splice(aa(o,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(e),typeof n=="function"&&n();return}var a=!1,r=0;o.forEach(function(s){var l=0,u=s.target,d=u.fromRect,f=T(u),p=u.prevFromRect,E=u.prevToRect,b=s.rect,S=he(u,!0);S&&(f.top-=S.f,f.left-=S.e),u.toRect=f,u.thisAnimationDuration&&ot(p,f)&&!ot(d,f)&&(b.top-f.top)/(b.left-f.left)===(d.top-f.top)/(d.left-f.left)&&(l=ca(b,p,E,i.options)),ot(f,d)||(u.prevFromRect=d,u.prevToRect=f,l||(l=i.options.animation),i.animate(u,b,f,l)),l&&(a=!0,r=Math.max(r,l),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},l),u.thisAnimationDuration=l)}),clearTimeout(e),a?e=setTimeout(function(){typeof n=="function"&&n()},r):typeof n=="function"&&n(),o=[]},animate:function(n,i,a,r){if(r){h(n,"transition",""),h(n,"transform","");var s=he(this.el),l=s&&s.a,u=s&&s.d,d=(i.left-a.left)/(l||1),f=(i.top-a.top)/(u||1);n.animatingX=!!d,n.animatingY=!!f,h(n,"transform","translate3d("+d+"px,"+f+"px,0)"),this.forRepaintDummy=ua(n),h(n,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},r)}}}}function ua(o){return o.offsetWidth}function ca(o,e,t,n){return Math.sqrt(Math.pow(e.top-o.top,2)+Math.pow(e.left-o.left,2))/Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))*n.animation}var pe=[],at={initializeByDefault:!0},He={mount:function(e){for(var t in at)at.hasOwnProperty(t)&&!(t in e)&&(e[t]=at[t]);pe.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),pe.push(e)},pluginEvent:function(e,t,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var a=e+"Global";pe.forEach(function(r){t[r.pluginName]&&(t[r.pluginName][a]&&t[r.pluginName][a](Q({sortable:t},n)),t.options[r.pluginName]&&t[r.pluginName][e]&&t[r.pluginName][e](Q({sortable:t},n)))})},initializePlugins:function(e,t,n,i){pe.forEach(function(s){var l=s.pluginName;if(!(!e.options[l]&&!s.initializeByDefault)){var u=new s(e,t,e.options);u.sortable=e,u.options=e.options,e[l]=u,W(n,u.defaults)}});for(var a in e.options)if(e.options.hasOwnProperty(a)){var r=this.modifyOption(e,a,e.options[a]);typeof r<"u"&&(e.options[a]=r)}},getEventProperties:function(e,t){var n={};return pe.forEach(function(i){typeof i.eventProperties=="function"&&W(n,i.eventProperties.call(t[i.pluginName],e))}),n},modifyOption:function(e,t,n){var i;return pe.forEach(function(a){e[a.pluginName]&&a.optionListeners&&typeof a.optionListeners[t]=="function"&&(i=a.optionListeners[t].call(e[a.pluginName],n))}),i}};function Ae(o){var e=o.sortable,t=o.rootEl,n=o.name,i=o.targetEl,a=o.cloneEl,r=o.toEl,s=o.fromEl,l=o.oldIndex,u=o.newIndex,d=o.oldDraggableIndex,f=o.newDraggableIndex,p=o.originalEvent,E=o.putSortable,b=o.extraEventProperties;if(e=e||t&&t[k],!!e){var S,M=e.options,K="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!ne&&!Be?S=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(S=document.createEvent("Event"),S.initEvent(n,!0,!0)),S.to=r||t,S.from=s||t,S.item=i||t,S.clone=a,S.oldIndex=l,S.newIndex=u,S.oldDraggableIndex=d,S.newDraggableIndex=f,S.originalEvent=p,S.pullMode=E?E.lastPutMode:void 0;var x=Q(Q({},b),He.getEventProperties(n,e));for(var H in x)S[H]=x[H];t&&t.dispatchEvent(S),M[K]&&M[K].call(e,S)}}var fa=["evt"],X=function(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,a=qi(n,fa);He.pluginEvent.bind(g)(e,t,Q({dragEl:c,parentEl:A,ghostEl:v,rootEl:C,nextEl:de,lastDownEl:Ve,cloneEl:I,cloneHidden:re,dragStarted:Ie,putSortable:R,activeSortable:g.active,originalEvent:i,oldIndex:be,oldDraggableIndex:Re,newIndex:j,newDraggableIndex:ae,hideGhostForTarget:jt,unhideGhostForTarget:Kt,cloneNowHidden:function(){re=!0},cloneNowShown:function(){re=!1},dispatchSortableEvent:function(s){B({sortable:t,name:s,originalEvent:i})}},a))};function B(o){Ae(Q({putSortable:R,cloneEl:I,targetEl:c,rootEl:C,oldIndex:be,oldDraggableIndex:Re,newIndex:j,newDraggableIndex:ae},o))}var c,A,v,C,de,Ve,I,re,be,j,Re,ae,Ye,R,ve=!1,Je=!1,Qe=[],ce,V,rt,st,It,Nt,Ie,ge,Fe,ke=!1,Ge=!1,Ue,F,lt=[],pt=!1,et=[],nt=typeof document<"u",Le=Ft,Mt=Be||ne?"cssFloat":"float",da=nt&&!oa&&!Ft&&"draggable"in document.createElement("div"),Yt=function(){if(nt){if(ne)return!1;var o=document.createElement("x");return o.style.cssText="pointer-events:auto",o.style.pointerEvents==="auto"}}(),Gt=function(e,t){var n=h(e),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),a=ye(e,0,t),r=ye(e,1,t),s=a&&h(a),l=r&&h(r),u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+T(a).width,d=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+T(r).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(a&&s.float&&s.float!=="none"){var f=s.float==="left"?"left":"right";return r&&(l.clear==="both"||l.clear===f)?"vertical":"horizontal"}return a&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||u>=i&&n[Mt]==="none"||r&&n[Mt]==="none"&&u+d>i)?"vertical":"horizontal"},ha=function(e,t,n){var i=n?e.left:e.top,a=n?e.right:e.bottom,r=n?e.width:e.height,s=n?t.left:t.top,l=n?t.right:t.bottom,u=n?t.width:t.height;return i===s||a===l||i+r/2===s+u/2},pa=function(e,t){var n;return Qe.some(function(i){var a=i[k].options.emptyInsertThreshold;if(!(!a||bt(i))){var r=T(i),s=e>=r.left-a&&e<=r.right+a,l=t>=r.top-a&&t<=r.bottom+a;if(s&&l)return n=i}}),n},Lt=function(e){function t(a,r){return function(s,l,u,d){var f=s.options.group.name&&l.options.group.name&&s.options.group.name===l.options.group.name;if(a==null&&(r||f))return!0;if(a==null||a===!1)return!1;if(r&&a==="clone")return a;if(typeof a=="function")return t(a(s,l,u,d),r)(s,l,u,d);var p=(r?s:l).options.group.name;return a===!0||typeof a=="string"&&a===p||a.join&&a.indexOf(p)>-1}}var n={},i=e.group;(!i||We(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=t(i.pull,!0),n.checkPut=t(i.put),n.revertClone=i.revertClone,e.group=n},jt=function(){!Yt&&v&&h(v,"display","none")},Kt=function(){!Yt&&v&&h(v,"display","")};nt&&document.addEventListener("click",function(o){if(Je)return o.preventDefault(),o.stopPropagation&&o.stopPropagation(),o.stopImmediatePropagation&&o.stopImmediatePropagation(),Je=!1,!1},!0);var fe=function(e){if(c){e=e.touches?e.touches[0]:e;var t=pa(e.clientX,e.clientY);if(t){var n={};for(var i in e)e.hasOwnProperty(i)&&(n[i]=e[i]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[k]._onDragOver(n)}}},ga=function(e){c&&c.parentNode[k]._isOutsideThisEl(e.target)};function g(o,e){if(!(o&&o.nodeType&&o.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));this.el=o,this.options=e=W({},e),o[k]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(o.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Gt(o,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(r,s){r.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&!Pe,emptyInsertThreshold:5};He.initializePlugins(this,o,t);for(var n in t)!(n in e)&&(e[n]=t[n]);Lt(e);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=e.forceFallback?!1:da,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?w(o,"pointerdown",this._onTapStart):(w(o,"mousedown",this._onTapStart),w(o,"touchstart",this._onTapStart)),this.nativeDraggable&&(w(o,"dragover",this),w(o,"dragenter",this)),Qe.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),W(this,la())}g.prototype={constructor:g,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(ge=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,c):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,i=this.options,a=i.preventOnFilter,r=e.type,s=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,l=(s||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||l,d=i.filter;if(Da(n),!c&&!(/mousedown|pointerdown/.test(r)&&e.button!==0||i.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Pe&&l&&l.tagName.toUpperCase()==="SELECT")&&(l=$(l,i.draggable,n,!1),!(l&&l.animated)&&Ve!==l)){if(be=N(l),Re=N(l,i.draggable),typeof d=="function"){if(d.call(this,e,l,this)){B({sortable:t,rootEl:u,name:"filter",targetEl:l,toEl:n,fromEl:n}),X("filter",t,{evt:e}),a&&e.cancelable&&e.preventDefault();return}}else if(d&&(d=d.split(",").some(function(f){if(f=$(u,f.trim(),n,!1),f)return B({sortable:t,rootEl:f,name:"filter",targetEl:l,fromEl:n,toEl:n}),X("filter",t,{evt:e}),!0}),d)){a&&e.cancelable&&e.preventDefault();return}i.handle&&!$(u,i.handle,n,!1)||this._prepareDragStart(e,s,l)}}},_prepareDragStart:function(e,t,n){var i=this,a=i.el,r=i.options,s=a.ownerDocument,l;if(n&&!c&&n.parentNode===a){var u=T(n);if(C=a,c=n,A=c.parentNode,de=c.nextSibling,Ve=n,Ye=r.group,g.dragged=c,ce={target:c,clientX:(t||e).clientX,clientY:(t||e).clientY},It=ce.clientX-u.left,Nt=ce.clientY-u.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,c.style["will-change"]="all",l=function(){if(X("delayEnded",i,{evt:e}),g.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!_t&&i.nativeDraggable&&(c.draggable=!0),i._triggerDragStart(e,t),B({sortable:i,name:"choose",originalEvent:e}),O(c,r.chosenClass,!0)},r.ignore.split(",").forEach(function(d){Bt(c,d.trim(),ut)}),w(s,"dragover",fe),w(s,"mousemove",fe),w(s,"touchmove",fe),w(s,"mouseup",i._onDrop),w(s,"touchend",i._onDrop),w(s,"touchcancel",i._onDrop),_t&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),X("delayStart",this,{evt:e}),r.delay&&(!r.delayOnTouchOnly||t)&&(!this.nativeDraggable||!(Be||ne))){if(g.eventCanceled){this._onDrop();return}w(s,"mouseup",i._disableDelayedDrag),w(s,"touchend",i._disableDelayedDrag),w(s,"touchcancel",i._disableDelayedDrag),w(s,"mousemove",i._delayedDragTouchMoveHandler),w(s,"touchmove",i._delayedDragTouchMoveHandler),r.supportPointer&&w(s,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(l,r.delay)}else l()}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&ut(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._disableDelayedDrag),y(e,"touchend",this._disableDelayedDrag),y(e,"touchcancel",this._disableDelayedDrag),y(e,"mousemove",this._delayedDragTouchMoveHandler),y(e,"touchmove",this._delayedDragTouchMoveHandler),y(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?w(document,"pointermove",this._onTouchMove):t?w(document,"touchmove",this._onTouchMove):w(document,"mousemove",this._onTouchMove):(w(c,"dragend",this),w(C,"dragstart",this._onDragStart));try{document.selection?$e(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(ve=!1,C&&c){X("dragStarted",this,{evt:t}),this.nativeDraggable&&w(document,"dragover",ga);var n=this.options;!e&&O(c,n.dragClass,!1),O(c,n.ghostClass,!0),g.active=this,e&&this._appendGhost(),B({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(V){this._lastX=V.clientX,this._lastY=V.clientY,jt();for(var e=document.elementFromPoint(V.clientX,V.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(V.clientX,V.clientY),e!==t);)t=e;if(c.parentNode[k]._isOutsideThisEl(e),t)do{if(t[k]){var n=void 0;if(n=t[k]._onDragOver({clientX:V.clientX,clientY:V.clientY,target:e,rootEl:t}),n&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Kt()}},_onTouchMove:function(e){if(ce){var t=this.options,n=t.fallbackTolerance,i=t.fallbackOffset,a=e.touches?e.touches[0]:e,r=v&&he(v,!0),s=v&&r&&r.a,l=v&&r&&r.d,u=Le&&F&&Ot(F),d=(a.clientX-ce.clientX+i.x)/(s||1)+(u?u[0]-lt[0]:0)/(s||1),f=(a.clientY-ce.clientY+i.y)/(l||1)+(u?u[1]-lt[1]:0)/(l||1);if(!g.active&&!ve){if(n&&Math.max(Math.abs(a.clientX-this._lastX),Math.abs(a.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(v){r?(r.e+=d-(rt||0),r.f+=f-(st||0)):r={a:1,b:0,c:0,d:1,e:d,f};var p="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");h(v,"webkitTransform",p),h(v,"mozTransform",p),h(v,"msTransform",p),h(v,"transform",p),rt=d,st=f,V=a}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!v){var e=this.options.fallbackOnBody?document.body:C,t=T(c,!0,Le,!0,e),n=this.options;if(Le){for(F=e;h(F,"position")==="static"&&h(F,"transform")==="none"&&F!==document;)F=F.parentNode;F!==document.body&&F!==document.documentElement?(F===document&&(F=J()),t.top+=F.scrollTop,t.left+=F.scrollLeft):F=J(),lt=Ot(F)}v=c.cloneNode(!0),O(v,n.ghostClass,!1),O(v,n.fallbackClass,!0),O(v,n.dragClass,!0),h(v,"transition",""),h(v,"transform",""),h(v,"box-sizing","border-box"),h(v,"margin",0),h(v,"top",t.top),h(v,"left",t.left),h(v,"width",t.width),h(v,"height",t.height),h(v,"opacity","0.8"),h(v,"position",Le?"absolute":"fixed"),h(v,"zIndex","100000"),h(v,"pointerEvents","none"),g.ghost=v,e.appendChild(v),h(v,"transform-origin",It/parseInt(v.style.width)*100+"% "+Nt/parseInt(v.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,i=e.dataTransfer,a=n.options;if(X("dragStart",this,{evt:e}),g.eventCanceled){this._onDrop();return}X("setupClone",this),g.eventCanceled||(I=yt(c),I.draggable=!1,I.style["will-change"]="",this._hideClone(),O(I,this.options.chosenClass,!1),g.clone=I),n.cloneId=$e(function(){X("clone",n),!g.eventCanceled&&(n.options.removeCloneOnHide||C.insertBefore(I,c),n._hideClone(),B({sortable:n,name:"clone"}))}),!t&&O(c,a.dragClass,!0),t?(Je=!0,n._loopId=setInterval(n._emulateDragOver,50)):(y(document,"mouseup",n._onDrop),y(document,"touchend",n._onDrop),y(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",a.setData&&a.setData.call(n,i,c)),w(document,"drop",n),h(c,"transform","translateZ(0)")),ve=!0,n._dragStartId=$e(n._dragStarted.bind(n,t,e)),w(document,"selectstart",n),Ie=!0,Pe&&h(document.body,"user-select","none")},_onDragOver:function(e){var t=this.el,n=e.target,i,a,r,s=this.options,l=s.group,u=g.active,d=Ye===l,f=s.sort,p=R||u,E,b=this,S=!1;if(pt)return;function M(_e,Wt){X(_e,b,Q({evt:e,isOwner:d,axis:E?"vertical":"horizontal",revert:r,dragRect:i,targetRect:a,canSort:f,fromSortable:p,target:n,completed:x,onMove:function(St,Vt){return je(C,t,c,i,St,T(St),e,Vt)},changed:H},Wt))}function K(){M("dragOverAnimationCapture"),b.captureAnimationState(),b!==p&&p.captureAnimationState()}function x(_e){return M("dragOverCompleted",{insertion:_e}),_e&&(d?u._hideClone():u._showClone(b),b!==p&&(O(c,R?R.options.ghostClass:u.options.ghostClass,!1),O(c,s.ghostClass,!0)),R!==b&&b!==g.active?R=b:b===g.active&&R&&(R=null),p===b&&(b._ignoreWhileAnimating=n),b.animateAll(function(){M("dragOverAnimationComplete"),b._ignoreWhileAnimating=null}),b!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(n===c&&!c.animated||n===t&&!n.animated)&&(ge=null),!s.dragoverBubble&&!e.rootEl&&n!==document&&(c.parentNode[k]._isOutsideThisEl(e.target),!_e&&fe(e)),!s.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),S=!0}function H(){j=N(c),ae=N(c,s.draggable),B({sortable:b,name:"change",toEl:t,newIndex:j,newDraggableIndex:ae,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),n=$(n,s.draggable,t,!0),M("dragOver"),g.eventCanceled)return S;if(c.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||b._ignoreWhileAnimating===n)return x(!1);if(Je=!1,u&&!s.disabled&&(d?f||(r=A!==C):R===this||(this.lastPutMode=Ye.checkPull(this,u,c,e))&&l.checkPut(this,u,c,e))){if(E=this._getDirection(e,n)==="vertical",i=T(c),M("dragOverValid"),g.eventCanceled)return S;if(r)return A=C,K(),this._hideClone(),M("revert"),g.eventCanceled||(de?C.insertBefore(c,de):C.appendChild(c)),x(!0);var D=bt(t,s.draggable);if(!D||ya(e,E,this)&&!D.animated){if(D===c)return x(!1);if(D&&t===e.target&&(n=D),n&&(a=T(n)),je(C,t,c,i,n,a,e,!!n)!==!1)return K(),t.appendChild(c),A=t,H(),x(!0)}else if(D&&ba(e,E,this)){var q=ye(t,0,s,!0);if(q===c)return x(!1);if(n=q,a=T(n),je(C,t,c,i,n,a,e,!1)!==!1)return K(),t.insertBefore(c,q),A=t,H(),x(!0)}else if(n.parentNode===t){a=T(n);var Z=0,le,we=c.parentNode!==t,G=!ha(c.animated&&c.toRect||i,n.animated&&n.toRect||a,E),Ee=E?"top":"left",oe=Tt(n,"top","top")||Tt(c,"top","top"),Se=oe?oe.scrollTop:void 0;ge!==n&&(le=a[Ee],ke=!1,Ge=!G&&s.invertSwap||we),Z=wa(e,n,a,E,G?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,Ge,ge===n);var ee;if(Z!==0){var ue=N(c);do ue-=Z,ee=A.children[ue];while(ee&&(h(ee,"display")==="none"||ee===v))}if(Z===0||ee===n)return x(!1);ge=n,Fe=Z;var De=n.nextElementSibling,ie=!1;ie=Z===1;var Xe=je(C,t,c,i,n,a,e,ie);if(Xe!==!1)return(Xe===1||Xe===-1)&&(ie=Xe===1),pt=!0,setTimeout(va,30),K(),ie&&!De?t.appendChild(c):n.parentNode.insertBefore(c,ie?De:n),oe&&Xt(oe,0,Se-oe.scrollTop),A=c.parentNode,le!==void 0&&!Ge&&(Ue=Math.abs(le-T(n)[Ee])),H(),x(!0)}if(t.contains(c))return x(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){y(document,"mousemove",this._onTouchMove),y(document,"touchmove",this._onTouchMove),y(document,"pointermove",this._onTouchMove),y(document,"dragover",fe),y(document,"mousemove",fe),y(document,"touchmove",fe)},_offUpEvents:function(){var e=this.el.ownerDocument;y(e,"mouseup",this._onDrop),y(e,"touchend",this._onDrop),y(e,"pointerup",this._onDrop),y(e,"touchcancel",this._onDrop),y(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;if(j=N(c),ae=N(c,n.draggable),X("drop",this,{evt:e}),A=c&&c.parentNode,j=N(c),ae=N(c,n.draggable),g.eventCanceled){this._nulling();return}ve=!1,Ge=!1,ke=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),gt(this.cloneId),gt(this._dragStartId),this.nativeDraggable&&(y(document,"drop",this),y(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Pe&&h(document.body,"user-select",""),h(c,"transform",""),e&&(Ie&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),v&&v.parentNode&&v.parentNode.removeChild(v),(C===A||R&&R.lastPutMode!=="clone")&&I&&I.parentNode&&I.parentNode.removeChild(I),c&&(this.nativeDraggable&&y(c,"dragend",this),ut(c),c.style["will-change"]="",Ie&&!ve&&O(c,R?R.options.ghostClass:this.options.ghostClass,!1),O(c,this.options.chosenClass,!1),B({sortable:this,name:"unchoose",toEl:A,newIndex:null,newDraggableIndex:null,originalEvent:e}),C!==A?(j>=0&&(B({rootEl:A,name:"add",toEl:A,fromEl:C,originalEvent:e}),B({sortable:this,name:"remove",toEl:A,originalEvent:e}),B({rootEl:A,name:"sort",toEl:A,fromEl:C,originalEvent:e}),B({sortable:this,name:"sort",toEl:A,originalEvent:e})),R&&R.save()):j!==be&&j>=0&&(B({sortable:this,name:"update",toEl:A,originalEvent:e}),B({sortable:this,name:"sort",toEl:A,originalEvent:e})),g.active&&((j==null||j===-1)&&(j=be,ae=Re),B({sortable:this,name:"end",toEl:A,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){X("nulling",this),C=c=A=v=de=I=Ve=re=ce=V=Ie=j=ae=be=Re=ge=Fe=R=Ye=g.dragged=g.ghost=g.clone=g.active=null,et.forEach(function(e){e.checked=!0}),et.length=rt=st=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":c&&(this._onDragOver(e),ma(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],t,n=this.el.children,i=0,a=n.length,r=this.options;i<a;i++)t=n[i],$(t,r.draggable,this.el,!1)&&e.push(t.getAttribute(r.dataIdAttr)||Sa(t));return e},sort:function(e,t){var n={},i=this.el;this.toArray().forEach(function(a,r){var s=i.children[r];$(s,this.options.draggable,i,!1)&&(n[a]=s)},this),t&&this.captureAnimationState(),e.forEach(function(a){n[a]&&(i.removeChild(n[a]),i.appendChild(n[a]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return $(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(t===void 0)return n[e];var i=He.modifyOption(this,e,t);typeof i<"u"?n[e]=i:n[e]=t,e==="group"&&Lt(n)},destroy:function(){X("destroy",this);var e=this.el;e[k]=null,y(e,"mousedown",this._onTapStart),y(e,"touchstart",this._onTapStart),y(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(y(e,"dragover",this),y(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Qe.splice(Qe.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!re){if(X("hideClone",this),g.eventCanceled)return;h(I,"display","none"),this.options.removeCloneOnHide&&I.parentNode&&I.parentNode.removeChild(I),re=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(re){if(X("showClone",this),g.eventCanceled)return;c.parentNode==C&&!this.options.group.revertClone?C.insertBefore(I,c):de?C.insertBefore(I,de):C.appendChild(I),this.options.group.revertClone&&this.animate(c,I),h(I,"display",""),re=!1}}};function ma(o){o.dataTransfer&&(o.dataTransfer.dropEffect="move"),o.cancelable&&o.preventDefault()}function je(o,e,t,n,i,a,r,s){var l,u=o[k],d=u.options.onMove,f;return window.CustomEvent&&!ne&&!Be?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=e,l.from=o,l.dragged=t,l.draggedRect=n,l.related=i||e,l.relatedRect=a||T(e),l.willInsertAfter=s,l.originalEvent=r,o.dispatchEvent(l),d&&(f=d.call(u,l,r)),f}function ut(o){o.draggable=!1}function va(){pt=!1}function ba(o,e,t){var n=T(ye(t.el,0,t.options,!0)),i=10;return e?o.clientX<n.left-i||o.clientY<n.top&&o.clientX<n.right:o.clientY<n.top-i||o.clientY<n.bottom&&o.clientX<n.left}function ya(o,e,t){var n=T(bt(t.el,t.options.draggable)),i=10;return e?o.clientX>n.right+i||o.clientX<=n.right&&o.clientY>n.bottom&&o.clientX>=n.left:o.clientX>n.right&&o.clientY>n.top||o.clientX<=n.right&&o.clientY>n.bottom+i}function wa(o,e,t,n,i,a,r,s){var l=n?o.clientY:o.clientX,u=n?t.height:t.width,d=n?t.top:t.left,f=n?t.bottom:t.right,p=!1;if(!r){if(s&&Ue<u*i){if(!ke&&(Fe===1?l>d+u*a/2:l<f-u*a/2)&&(ke=!0),ke)p=!0;else if(Fe===1?l<d+Ue:l>f-Ue)return-Fe}else if(l>d+u*(1-i)/2&&l<f-u*(1-i)/2)return Ea(e)}return p=p||r,p&&(l<d+u*a/2||l>f-u*a/2)?l>d+u/2?1:-1:0}function Ea(o){return N(c)<N(o)?1:-1}function Sa(o){for(var e=o.tagName+o.className+o.src+o.href+o.textContent,t=e.length,n=0;t--;)n+=e.charCodeAt(t);return n.toString(36)}function Da(o){et.length=0;for(var e=o.getElementsByTagName("input"),t=e.length;t--;){var n=e[t];n.checked&&et.push(n)}}function $e(o){return setTimeout(o,0)}function gt(o){return clearTimeout(o)}nt&&w(document,"touchmove",function(o){(g.active||ve)&&o.cancelable&&o.preventDefault()});g.utils={on:w,off:y,css:h,find:Bt,is:function(e,t){return!!$(e,t,e,!1)},extend:ra,throttle:Ht,closest:$,toggleClass:O,clone:yt,index:N,nextTick:$e,cancelNextTick:gt,detectDirection:Gt,getChild:ye};g.get=function(o){return o[k]};g.mount=function(){for(var o=arguments.length,e=new Array(o),t=0;t<o;t++)e[t]=arguments[t];e[0].constructor===Array&&(e=e[0]),e.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(g.utils=Q(Q({},g.utils),n.utils)),He.mount(n)})};g.create=function(o,e){return new g(o,e)};g.version=na;var P=[],Ne,mt,vt=!1,ct,ft,tt,Me;function _a(){function o(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return o.prototype={dragStarted:function(t){var n=t.originalEvent;this.sortable.nativeDraggable?w(document,"dragover",this._handleAutoScroll):this.options.supportPointer?w(document,"pointermove",this._handleFallbackAutoScroll):n.touches?w(document,"touchmove",this._handleFallbackAutoScroll):w(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var n=t.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):(y(document,"pointermove",this._handleFallbackAutoScroll),y(document,"touchmove",this._handleFallbackAutoScroll),y(document,"mousemove",this._handleFallbackAutoScroll)),Pt(),qe(),sa()},nulling:function(){tt=mt=Ne=vt=Me=ct=ft=null,P.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,n){var i=this,a=(t.touches?t.touches[0]:t).clientX,r=(t.touches?t.touches[0]:t).clientY,s=document.elementFromPoint(a,r);if(tt=t,n||this.options.forceAutoScrollFallback||Be||ne||Pe){dt(t,this.options,s,n);var l=se(s,!0);vt&&(!Me||a!==ct||r!==ft)&&(Me&&Pt(),Me=setInterval(function(){var u=se(document.elementFromPoint(a,r),!0);u!==l&&(l=u,qe()),dt(t,i.options,u,n)},10),ct=a,ft=r)}else{if(!this.options.bubbleScroll||se(s,!0)===J()){qe();return}dt(t,this.options,se(s,!1),!1)}}},W(o,{pluginName:"scroll",initializeByDefault:!0})}function qe(){P.forEach(function(o){clearInterval(o.pid)}),P=[]}function Pt(){clearInterval(Me)}var dt=Ht(function(o,e,t,n){if(e.scroll){var i=(o.touches?o.touches[0]:o).clientX,a=(o.touches?o.touches[0]:o).clientY,r=e.scrollSensitivity,s=e.scrollSpeed,l=J(),u=!1,d;mt!==t&&(mt=t,qe(),Ne=e.scroll,d=e.scrollFn,Ne===!0&&(Ne=se(t,!0)));var f=0,p=Ne;do{var E=p,b=T(E),S=b.top,M=b.bottom,K=b.left,x=b.right,H=b.width,D=b.height,q=void 0,Z=void 0,le=E.scrollWidth,we=E.scrollHeight,G=h(E),Ee=E.scrollLeft,oe=E.scrollTop;E===l?(q=H<le&&(G.overflowX==="auto"||G.overflowX==="scroll"||G.overflowX==="visible"),Z=D<we&&(G.overflowY==="auto"||G.overflowY==="scroll"||G.overflowY==="visible")):(q=H<le&&(G.overflowX==="auto"||G.overflowX==="scroll"),Z=D<we&&(G.overflowY==="auto"||G.overflowY==="scroll"));var Se=q&&(Math.abs(x-i)<=r&&Ee+H<le)-(Math.abs(K-i)<=r&&!!Ee),ee=Z&&(Math.abs(M-a)<=r&&oe+D<we)-(Math.abs(S-a)<=r&&!!oe);if(!P[f])for(var ue=0;ue<=f;ue++)P[ue]||(P[ue]={});(P[f].vx!=Se||P[f].vy!=ee||P[f].el!==E)&&(P[f].el=E,P[f].vx=Se,P[f].vy=ee,clearInterval(P[f].pid),(Se!=0||ee!=0)&&(u=!0,P[f].pid=setInterval((function(){n&&this.layer===0&&g.active._onTouchMove(tt);var De=P[this.layer].vy?P[this.layer].vy*s:0,ie=P[this.layer].vx?P[this.layer].vx*s:0;typeof d=="function"&&d.call(g.dragged.parentNode[k],ie,De,o,tt,P[this.layer].el)!=="continue"||Xt(P[this.layer].el,ie,De)}).bind({layer:f}),24))),f++}while(e.bubbleScroll&&p!==l&&(p=se(p,!1)));vt=u}},30),zt=function(e){var t=e.originalEvent,n=e.putSortable,i=e.dragEl,a=e.activeSortable,r=e.dispatchSortableEvent,s=e.hideGhostForTarget,l=e.unhideGhostForTarget;if(t){var u=n||a;s();var d=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,f=document.elementFromPoint(d.clientX,d.clientY);l(),u&&!u.el.contains(f)&&(r("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function wt(){}wt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=ye(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(t,i):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:zt};W(wt,{pluginName:"revertOnSpill"});function Et(){}Et.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable,i=n||this.sortable;i.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),i.animateAll()},drop:zt};W(Et,{pluginName:"removeOnSpill"});var z;function Ca(){function o(){this.defaults={swapClass:"sortable-swap-highlight"}}return o.prototype={dragStart:function(t){var n=t.dragEl;z=n},dragOverValid:function(t){var n=t.completed,i=t.target,a=t.onMove,r=t.activeSortable,s=t.changed,l=t.cancel;if(r.options.swap){var u=this.sortable.el,d=this.options;if(i&&i!==u){var f=z;a(i)!==!1?(O(i,d.swapClass,!0),z=i):z=null,f&&f!==z&&O(f,d.swapClass,!1)}s(),n(!0),l()}},drop:function(t){var n=t.activeSortable,i=t.putSortable,a=t.dragEl,r=i||this.sortable,s=this.options;z&&O(z,s.swapClass,!1),z&&(s.swap||i&&i.options.swap)&&a!==z&&(r.captureAnimationState(),r!==n&&n.captureAnimationState(),Ta(a,z),r.animateAll(),r!==n&&n.animateAll())},nulling:function(){z=null}},W(o,{pluginName:"swap",eventProperties:function(){return{swapItem:z}}})}function Ta(o,e){var t=o.parentNode,n=e.parentNode,i,a;!t||!n||t.isEqualNode(e)||n.isEqualNode(o)||(i=N(o),a=N(e),t.isEqualNode(n)&&i<a&&a++,t.insertBefore(e,t.children[i]),n.insertBefore(o,n.children[a]))}var m=[],L=[],Ce,U,Te=!1,Y=!1,me=!1,_,Oe,Ke;function Oa(){function o(e){for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this));e.options.supportPointer?w(document,"pointerup",this._deselectMultiDrag):(w(document,"mouseup",this._deselectMultiDrag),w(document,"touchend",this._deselectMultiDrag)),w(document,"keydown",this._checkKeyDown),w(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(i,a){var r="";m.length&&U===e?m.forEach(function(s,l){r+=(l?", ":"")+s.textContent}):r=a.textContent,i.setData("Text",r)}}}return o.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var n=t.dragEl;_=n},delayEnded:function(){this.isMultiDrag=~m.indexOf(_)},setupClone:function(t){var n=t.sortable,i=t.cancel;if(this.isMultiDrag){for(var a=0;a<m.length;a++)L.push(yt(m[a])),L[a].sortableIndex=m[a].sortableIndex,L[a].draggable=!1,L[a].style["will-change"]="",O(L[a],this.options.selectedClass,!1),m[a]===_&&O(L[a],this.options.chosenClass,!1);n._hideClone(),i()}},clone:function(t){var n=t.sortable,i=t.rootEl,a=t.dispatchSortableEvent,r=t.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||m.length&&U===n&&(xt(!0,i),a("clone"),r()))},showClone:function(t){var n=t.cloneNowShown,i=t.rootEl,a=t.cancel;this.isMultiDrag&&(xt(!1,i),L.forEach(function(r){h(r,"display","")}),n(),Ke=!1,a())},hideClone:function(t){var n=this;t.sortable;var i=t.cloneNowHidden,a=t.cancel;this.isMultiDrag&&(L.forEach(function(r){h(r,"display","none"),n.options.removeCloneOnHide&&r.parentNode&&r.parentNode.removeChild(r)}),i(),Ke=!0,a())},dragStartGlobal:function(t){t.sortable,!this.isMultiDrag&&U&&U.multiDrag._deselectMultiDrag(),m.forEach(function(n){n.sortableIndex=N(n)}),m=m.sort(function(n,i){return n.sortableIndex-i.sortableIndex}),me=!0},dragStarted:function(t){var n=this,i=t.sortable;if(this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){m.forEach(function(r){r!==_&&h(r,"position","absolute")});var a=T(_,!1,!0,!0);m.forEach(function(r){r!==_&&At(r,a)}),Y=!0,Te=!0}i.animateAll(function(){Y=!1,Te=!1,n.options.animation&&m.forEach(function(r){it(r)}),n.options.sort&&ze()})}},dragOver:function(t){var n=t.target,i=t.completed,a=t.cancel;Y&&~m.indexOf(n)&&(i(!1),a())},revert:function(t){var n=t.fromSortable,i=t.rootEl,a=t.sortable,r=t.dragRect;m.length>1&&(m.forEach(function(s){a.addAnimationState({target:s,rect:Y?T(s):r}),it(s),s.fromRect=r,n.removeAnimationState(s)}),Y=!1,Aa(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(t){var n=t.sortable,i=t.isOwner,a=t.insertion,r=t.activeSortable,s=t.parentEl,l=t.putSortable,u=this.options;if(a){if(i&&r._hideClone(),Te=!1,u.animation&&m.length>1&&(Y||!i&&!r.options.sort&&!l)){var d=T(_,!1,!0,!0);m.forEach(function(p){p!==_&&(At(p,d),s.appendChild(p))}),Y=!0}if(!i)if(Y||ze(),m.length>1){var f=Ke;r._showClone(n),r.options.animation&&!Ke&&f&&L.forEach(function(p){r.addAnimationState({target:p,rect:Oe}),p.fromRect=Oe,p.thisAnimationDuration=null})}else r._showClone(n)}},dragOverAnimationCapture:function(t){var n=t.dragRect,i=t.isOwner,a=t.activeSortable;if(m.forEach(function(s){s.thisAnimationDuration=null}),a.options.animation&&!i&&a.multiDrag.isMultiDrag){Oe=W({},n);var r=he(_,!0);Oe.top-=r.f,Oe.left-=r.e}},dragOverAnimationComplete:function(){Y&&(Y=!1,ze())},drop:function(t){var n=t.originalEvent,i=t.rootEl,a=t.parentEl,r=t.sortable,s=t.dispatchSortableEvent,l=t.oldIndex,u=t.putSortable,d=u||this.sortable;if(n){var f=this.options,p=a.children;if(!me)if(f.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),O(_,f.selectedClass,!~m.indexOf(_)),~m.indexOf(_))m.splice(m.indexOf(_),1),Ce=null,Ae({sortable:r,rootEl:i,name:"deselect",targetEl:_});else{if(m.push(_),Ae({sortable:r,rootEl:i,name:"select",targetEl:_}),n.shiftKey&&Ce&&r.el.contains(Ce)){var E=N(Ce),b=N(_);if(~E&&~b&&E!==b){var S,M;for(b>E?(M=E,S=b):(M=b,S=E+1);M<S;M++)~m.indexOf(p[M])||(O(p[M],f.selectedClass,!0),m.push(p[M]),Ae({sortable:r,rootEl:i,name:"select",targetEl:p[M]}))}}else Ce=_;U=d}if(me&&this.isMultiDrag){if(Y=!1,(a[k].options.sort||a!==i)&&m.length>1){var K=T(_),x=N(_,":not(."+this.options.selectedClass+")");if(!Te&&f.animation&&(_.thisAnimationDuration=null),d.captureAnimationState(),!Te&&(f.animation&&(_.fromRect=K,m.forEach(function(D){if(D.thisAnimationDuration=null,D!==_){var q=Y?T(D):K;D.fromRect=q,d.addAnimationState({target:D,rect:q})}})),ze(),m.forEach(function(D){p[x]?a.insertBefore(D,p[x]):a.appendChild(D),x++}),l===N(_))){var H=!1;m.forEach(function(D){if(D.sortableIndex!==N(D)){H=!0;return}}),H&&s("update")}m.forEach(function(D){it(D)}),d.animateAll()}U=d}(i===a||u&&u.lastPutMode!=="clone")&&L.forEach(function(D){D.parentNode&&D.parentNode.removeChild(D)})}},nullingGlobal:function(){this.isMultiDrag=me=!1,L.length=0},destroyGlobal:function(){this._deselectMultiDrag(),y(document,"pointerup",this._deselectMultiDrag),y(document,"mouseup",this._deselectMultiDrag),y(document,"touchend",this._deselectMultiDrag),y(document,"keydown",this._checkKeyDown),y(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!(typeof me<"u"&&me)&&U===this.sortable&&!(t&&$(t.target,this.options.draggable,this.sortable.el,!1))&&!(t&&t.button!==0))for(;m.length;){var n=m[0];O(n,this.options.selectedClass,!1),m.shift(),Ae({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:n})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},W(o,{pluginName:"multiDrag",utils:{select:function(t){var n=t.parentNode[k];!n||!n.options.multiDrag||~m.indexOf(t)||(U&&U!==n&&(U.multiDrag._deselectMultiDrag(),U=n),O(t,n.options.selectedClass,!0),m.push(t))},deselect:function(t){var n=t.parentNode[k],i=m.indexOf(t);!n||!n.options.multiDrag||!~i||(O(t,n.options.selectedClass,!1),m.splice(i,1))}},eventProperties:function(){var t=this,n=[],i=[];return m.forEach(function(a){n.push({multiDragElement:a,index:a.sortableIndex});var r;Y&&a!==_?r=-1:Y?r=N(a,":not(."+t.options.selectedClass+")"):r=N(a),i.push({multiDragElement:a,index:r})}),{items:Zi(m),clones:[].concat(L),oldIndicies:n,newIndicies:i}},optionListeners:{multiDragKey:function(t){return t=t.toLowerCase(),t==="ctrl"?t="Control":t.length>1&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}function Aa(o,e){m.forEach(function(t,n){var i=e.children[t.sortableIndex+(o?Number(n):0)];i?e.insertBefore(t,i):e.appendChild(t)})}function xt(o,e){L.forEach(function(t,n){var i=e.children[t.sortableIndex+(o?Number(n):0)];i?e.insertBefore(t,i):e.appendChild(t)})}function ze(){m.forEach(function(o){o!==_&&o.parentNode&&o.parentNode.removeChild(o)})}g.mount(new _a);g.mount(Et,wt);const Ia=Object.freeze(Object.defineProperty({__proto__:null,MultiDrag:Oa,Sortable:g,Swap:Ca,default:g},Symbol.toStringTag,{value:"Module"})),xa=Rt(Ia);export{xa as a,Pa as r};
