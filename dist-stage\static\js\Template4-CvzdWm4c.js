import{_ as ce,bU as le,a5 as re,w as ae,z as d,c as l,o as r,i as e,k as u,t as i,l as n,H as y,I as k,f as J,J as de,a4 as ue,v as me,x as _e}from"./index-BJsoK47l.js";import{W as R}from"./style-CI2cvTa4.js";const a=f=>(me("data-v-7077831a"),f=f(),_e(),f),he={class:"resume-template template-3"},pe={class:"resume-container"},ve={class:"header-content"},fe={class:"header-text"},ye={class:"resume-title"},ke={class:"basic-info-section"},ge={class:"basic-info-grid"},be={class:"info-item"},je=a(()=>e("span",{class:"info-label"},"姓名：",-1)),we={class:"info-value"},Te={class:"info-item"},Le=a(()=>e("span",{class:"info-label"},"年龄：",-1)),He={class:"info-value"},Me={class:"info-item"},$e=a(()=>e("span",{class:"info-label"},"电话：",-1)),Se={class:"info-value"},Ve={class:"info-item"},xe=a(()=>e("span",{class:"info-label"},"邮箱：",-1)),Ee={class:"info-value"},Ne={class:"basic-info-grid"},Ce={class:"info-item"},Ie=a(()=>e("span",{class:"info-label"},"性别：",-1)),De={class:"info-value"},Oe={key:0,class:"info-item",style:{"margin-left":"20px"}},We=a(()=>e("span",{class:"info-label"},"籍贯：",-1)),Be={class:"info-value"},Fe={class:"info-item"},Pe=a(()=>e("span",{class:"info-label"},"求职意向：",-1)),qe={class:"info-value"},ze={key:0,class:"photo-container"},Je=["src"],Re={key:0,class:"resume-section"},Ue=a(()=>e("div",{class:"section-header"},[e("h2",null,"教育经历")],-1)),Ye={class:"education-content"},Ae={class:"edu-header"},Ge={class:"edu-date"},Ke={class:"edu-school"},Qe={class:"edu-info"},Xe={key:0,class:"edu-courses"},Ze=a(()=>e("span",{class:"courses-label"},"主修课程：",-1)),es={key:1,class:"resume-section"},ss=a(()=>e("div",{class:"section-header"},[e("h2",null,"工作经验")],-1)),ts={class:"work-content"},os={class:"work-header"},is={class:"work-time"},ns={class:"work-company"},cs={class:"work-position"},ls=["innerHTML"],rs={key:2,class:"resume-section"},as=a(()=>e("div",{class:"section-header"},[e("h2",null,"项目经验")],-1)),ds={class:"work-content"},us={class:"work-header"},ms={class:"project-date"},_s={class:"project-title"},hs={class:"project-role"},ps=["innerHTML"],vs={key:3,class:"resume-section"},fs=a(()=>e("div",{class:"section-header"},[e("h2",null,"练手项目")],-1)),ys={class:"work-content"},ks={class:"work-header"},gs={class:"project-time"},bs={class:"project-title"},js={class:"project-role"},ws={class:"project-url"},Ts=["innerHTML"],Ls={key:4,class:"resume-section"},Hs=a(()=>e("div",{class:"section-header"},[e("h2",null,"技能特长")],-1)),Ms={class:"skills-content"},$s={key:0,class:"skills-description"},Ss=["innerHTML"],Vs={key:5,class:"resume-section"},xs=a(()=>e("div",{class:"section-header"},[e("h2",null,"荣誉证书")],-1)),Es={class:"certificate-content"},Ns={key:6,class:"resume-section"},Cs=a(()=>e("div",{class:"section-header"},[e("h2",null,"校园经历")],-1)),Is=["innerHTML"],Ds={key:7,class:"resume-section"},Os=a(()=>e("div",{class:"section-header"},[e("h2",null,"兴趣爱好")],-1)),Ws=["innerHTML"],Bs={key:8,class:"resume-section"},Fs=a(()=>e("div",{class:"section-header"},[e("h2",null,"个人评价")],-1)),Ps=["innerHTML"],qs={__name:"Template4",props:{resume:{type:Object,required:!0}},setup(f){le(s=>({"341f56bc":n(c).themeColor||"#3498db"}));const t=f,m=s=>!s||typeof s=="object"?"":s.includes("<")&&s.includes(">")?s:s.replace(/^### (.*?)$/gm,"<h3>$1</h3>").replace(/^## (.*?)$/gm,"<h2>$1</h2>").replace(/^# (.*?)$/gm,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^\- (.*?)$/gm,"<li>$1</li>").replace(/(<\/li>\n<li>)/g,"</li><li>").replace(/(<li>.*?<\/li>)/gs,"<ul>$1</ul>").replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>").replace(/^(.+)$/,"<p>$1</p>"),p=s=>{if(!s)return"";try{const h=new Date(s);if(isNaN(h.getTime())){const v=s.match(/^(\d{4}[-\.]\d{2})/);return v&&v[1]?v[1]:s}const g=h.getFullYear(),b=String(h.getMonth()+1).padStart(2,"0");return`${g}-${b}`}catch(h){return console.error("Error formatting date:",h),s}},{resume:c}=re(t);ae(()=>c.value,(s,h)=>{console.log("Template3 - resume prop changed")},{deep:!0});const U=d(()=>t.resume.modules&&t.resume.modules.education&&t.resume.modules.education.length>0),Y=d(()=>t.resume.modules&&t.resume.modules.work&&t.resume.modules.work.length>0),A=d(()=>t.resume.modules&&t.resume.modules.projects&&t.resume.modules.projects.length>0),G=d(()=>t.resume.modules&&t.resume.modules.skills&&t.resume.modules.skills.length>0),K=d(()=>!t.resume.modules||!t.resume.modules.skills?!1:t.resume.modules.skills.some(s=>s.description&&s.description.trim()!=="")),Q=d(()=>{if(!t.resume.modules||!t.resume.modules.certificates)return!1;const s=t.resume.modules.certificates;return typeof s=="string"?s.trim()!=="":typeof s=="object"&&s.certificateName&&s.certificateName.trim()!==""}),X=d(()=>{if(!t.resume.modules||!t.resume.modules.campus)return!1;const s=t.resume.modules.campus;return typeof s=="string"?s.trim()!=="":typeof s=="object"&&s.description&&s.description.trim()!==""}),Z=d(()=>{if(!t.resume.modules||!t.resume.modules.interests)return!1;const s=t.resume.modules.interests;return typeof s=="string"?s.trim()!=="":typeof s=="object"&&s.description&&s.description.trim()!==""}),ee=d(()=>{if(!t.resume.modules||!t.resume.modules.selfEvaluation)return!1;const s=t.resume.modules.selfEvaluation;return typeof s=="string"?s.trim()!=="":typeof s=="object"&&s.description&&s.description.trim()!==""}),se=d(()=>t.resume.modules&&t.resume.modules.practices&&t.resume.modules.practices.length>0),te=s=>{switch(s){case 1:return"了解";case 2:return"掌握";case 3:return"熟悉";case 4:return"精通";case 5:return"专家";default:return"了解"}};d(()=>!t.resume.modules||!t.resume.modules.skills||!t.resume.modules.skills.length?[]:t.resume.modules.skills.map(s=>({...s,levelText:te(s.level)})));const oe=d(()=>{if(!t.resume.modules||!t.resume.modules.campus)return"";const s=t.resume.modules.campus;return typeof s=="string"?m(s):typeof s=="object"&&s.description?m(s.description):""}),ie=d(()=>{if(!t.resume.modules||!t.resume.modules.interests)return"";const s=t.resume.modules.interests;return typeof s=="string"?m(s):typeof s=="object"&&s.description?m(s.description):""}),ne=d(()=>{if(!t.resume.modules||!t.resume.modules.selfEvaluation)return"";const s=t.resume.modules.selfEvaluation;return typeof s=="string"?m(s):typeof s=="object"&&s.description?m(s.description):""});return(s,h)=>{var g,b,v,j,w,T,L,H,M,$,S,V,x,E,N,C,I,D,O,W,B,F,P,q,z;return r(),l("div",he,[e("div",pe,[e("div",null,[e("div",ve,[e("div",fe,[e("div",ye,i(n(c)&&n(c).modules&&n(c).modules.basic&&n(c).modules.basic.name||"姓名"),1),e("div",ke,[e("div",ge,[e("div",be,[je,e("span",we,i(((v=(b=(g=n(c))==null?void 0:g.modules)==null?void 0:b.basic)==null?void 0:v.name)||""),1)]),e("div",Te,[Le,e("span",He,i(((T=(w=(j=n(c))==null?void 0:j.modules)==null?void 0:w.basic)==null?void 0:T.age)||""),1)]),e("div",Me,[$e,e("span",Se,i(((M=(H=(L=n(c))==null?void 0:L.modules)==null?void 0:H.basic)==null?void 0:M.phone)||""),1)]),e("div",Ve,[xe,e("span",Ee,i(((V=(S=($=n(c))==null?void 0:$.modules)==null?void 0:S.basic)==null?void 0:V.email)||""),1)])]),e("div",Ne,[e("div",Ce,[Ie,e("span",De,i(((N=(E=(x=n(c))==null?void 0:x.modules)==null?void 0:E.basic)==null?void 0:N.gender)||""),1)]),(D=(I=(C=n(c))==null?void 0:C.modules)==null?void 0:I.basic)!=null&&D.hometown?(r(),l("div",Oe,[We,e("span",Be,i((B=(W=(O=n(c))==null?void 0:O.modules)==null?void 0:W.basic)==null?void 0:B.hometown),1)])):u("",!0),e("div",Fe,[Pe,e("span",qe,i(((q=(P=(F=n(c))==null?void 0:F.modules)==null?void 0:P.basic)==null?void 0:q.jobObjective)||""),1)])])])]),(z=n(c).modules.basic)!=null&&z.avatar?(r(),l("div",ze,[e("img",{class:"photo",src:n(c).modules.basic.avatar,alt:"头像"},null,8,Je)])):u("",!0)])])]),U.value?(r(),l("div",Re,[Ue,e("div",Ye,[(r(!0),l(y,null,k(n(c).modules.education,(o,_)=>(r(),l("div",{key:_,class:"education-item"},[e("div",Ae,[e("div",Ge,i(p(o.time[0]))+" - "+i(p(o.time[1])||"至今"),1),e("div",Ke,i(o.school),1),e("div",null,i(o.degree)+"，"+i(o.major),1)]),e("div",Qe,[o.courses?(r(),l("div",Xe,[Ze,J(n(R),{modelValue:o.courses,previewOnly:""},null,8,["modelValue"])])):u("",!0)])]))),128))])])):u("",!0),Y.value?(r(),l("div",es,[ss,e("div",ts,[(r(!0),l(y,null,k(n(c).modules.work,(o,_)=>(r(),l("div",{key:_,class:"work-item"},[e("div",os,[e("span",is,i(p(o.time[0]))+" - "+i(p(o.time[1])||"至今"),1),e("div",ns,i(o.company),1),e("div",cs,i(o.position),1)]),e("div",{class:"work-description",innerHTML:m(o.description)},null,8,ls)]))),128))])])):u("",!0),A.value?(r(),l("div",rs,[as,e("div",ds,[(r(!0),l(y,null,k(n(c).modules.projects,(o,_)=>(r(),l("div",{key:_,class:"project-item"},[e("div",us,[e("div",ms,i(o.time[0])+" - "+i(o.time[1]||"至今"),1),e("div",_s,i(o.name),1),e("div",hs,i(o.role),1)]),e("div",{class:"project-description",innerHTML:m(o.description)},null,8,ps)]))),128))])])):u("",!0),se.value?(r(),l("div",vs,[fs,e("div",ys,[(r(!0),l(y,null,k(n(c).modules.practices,(o,_)=>(r(),l("div",{key:_,class:"project-item"},[e("div",ks,[e("div",gs,i(p(o.time[0]))+" - "+i(p(o.time[1])||"至今"),1),e("div",bs,i(o.name),1),e("div",js,i(o.role),1)]),e("div",ws,"项目地址："+i(o.url),1),e("div",{class:"project-description",innerHTML:m(o.description)},null,8,Ts)]))),128))])])):u("",!0),G.value?(r(),l("div",Ls,[Hs,e("div",Ms,[K.value?(r(),l("div",$s,[(r(!0),l(y,null,k(n(c).modules.skills,(o,_)=>de((r(),l("div",{key:"desc-"+_,class:"skill-description-item"},[e("div",{class:"skill-description-body",innerHTML:m(o.description)},null,8,Ss)])),[[ue,o.description]])),128))])):u("",!0)])])):u("",!0),Q.value?(r(),l("div",Vs,[xs,e("div",Es,[J(n(R),{modelValue:typeof n(c).modules.certificates=="string"?n(c).modules.certificates:typeof n(c).modules.certificates=="object"?n(c).modules.certificates.certificateName:"",previewOnly:""},null,8,["modelValue"])])])):u("",!0),X.value?(r(),l("div",Ns,[Cs,e("div",{class:"campus-content",innerHTML:oe.value},null,8,Is)])):u("",!0),Z.value?(r(),l("div",Ds,[Os,e("div",{class:"interests-content",innerHTML:ie.value},null,8,Ws)])):u("",!0),ee.value?(r(),l("div",Bs,[Fs,e("div",{class:"evaluation-content",innerHTML:ne.value},null,8,Ps)])):u("",!0)])}}},Rs=ce(qs,[["__scopeId","data-v-7077831a"]]);export{Rs as default};
