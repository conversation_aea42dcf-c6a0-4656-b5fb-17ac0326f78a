import request from '@/utils/request'

// 查询技能列表
export function listSkill(query) {
  return request({
    url: '/skill/page/list',
    method: 'get',
    params: query
  })
}

// 查询技能详细
export function getSkill(skiId) {
  return request({
    url: '/skill/' + skiId,
    method: 'get'
  })
}

// 新增技能
export function addSkill(data) {
  return request({
    url: '/skill/add',
    method: 'post',
    data: data
  })
}

// 修改技能
export function updateSkill(data) {
  return request({
    url: `/skill/${data.skiId}`,
    method: 'put',
    data: data
  })
}

// 删除技能
export function delSkill(skiId) {
  return request({
    url: '/skill/' + skiId,
    method: 'delete'
  })
}
