<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">题库管理</h1>
        <p class="page-subtitle">管理所有题目和题库</p>
      </div>
      <div class="header-actions">
        <el-upload 
          class="upload-btn" 
          :action="null"
          :http-request="handleImport"
          :show-file-list="false"
          :before-upload="beforeImport" 
          accept=".xlsx,.xls">
          <el-button type="primary" icon="Upload">导入题目</el-button>
        </el-upload>
        <el-button type="success" icon="Plus" @click="handleAdd">添加新题目</el-button>
      </div>
    </div>

    <!-- 过滤和搜索区域 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <div class="form-row">
          <el-form-item label="搜索题目">
            <el-input v-model="queryParams.content" placeholder="输入关键词搜索题目..." clearable prefix-icon="Search"
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="类目">
            <el-select v-model="queryParams.topicClassification" placeholder="全部类目" clearable class="filter-select">
              <el-option
                v-for="item in categoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="题型">
            <el-select v-model="queryParams.type" placeholder="全部题型" clearable class="filter-select">
              <el-option v-for="dict in questionTypeOptions" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-actions">
          <el-button @click="resetQuery">重置筛选</el-button>
          <el-button type="primary" @click="handleQuery">应用筛选</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 题目列表 -->
    <el-card class="table-container" shadow="hover">
      <div class="table-header">
        <h2 class="table-title">题目列表</h2>
        <div class="table-count">共 {{ total }} 个题目</div>
      </div>

      <el-table v-loading="loading" :data="questionList" row-key="questions_id" border style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column prop="questionId" label="ID" width="80" align="center" /> -->
        <el-table-column prop="content" label="题目" min-width="300" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="180" align="center">
          <template #default="scope">
            <el-tag :type="getTagType(scope.row.type)">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="topicClassification" label="类目" width="180" align="center">
          <template #default="scope">
            {{ scope.row.topicClassification }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" align="center" />
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <el-button type="primary" link icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="QuestionBank">
import { ref, reactive, onMounted, onActivated } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { listQuestions, delQuestions, getCategoryList, importQuestions } from '@/api/aqsystem/questions'
const router = useRouter()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  content: undefined,
  topicClassification: undefined,
  type: undefined
})

// 类目选项
const categoryOptions = ref([])

// 获取类目列表
const getCategoryOptions = async () => {
  try {
    const res = await getCategoryList()
    console.log('获取到的类目数据:', res)
    if (Array.isArray(res)) {
      categoryOptions.value = res.map(item => ({
        value: item.id,
        label: item.name
      }))
      console.log('转换后的类目选项:', categoryOptions.value)
    } else {
      console.error('类目数据格式不正确:', res)
      ElMessage.error('类目数据格式不正确')
    }
  } catch (error) {
    console.error('获取类目列表失败:', error)
    ElMessage.error('获取类目列表失败')
  }
}

// 题目类型选项
const questionTypeOptions = ref([
  { value: '填空题', label: '填空题' },
  { value: '单选题', label: '单选题' },
  { value: '多选题', label: '多选题' },
  { value: '不定项选择题', label: '不定项选择题' }
])

const loading = ref(false)
const total = ref(0)
const questionList = ref([])

// 获取题目列表
const getList = async () => {
  loading.value = true
  try {
    console.log('查询参数:', queryParams) // 添加日志
    const res = await listQuestions(queryParams)
    console.log('获取到的题目列表数据:', res)
    if (res.code === 200) {
      questionList.value = res.rows || []
      total.value = res.total || 0
    } else {
      ElMessage.error(res.msg || '获取题目列表失败')
    }
  } catch (error) {
    console.error('获取题目列表失败:', error)
    ElMessage.error('获取题目列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.pageNum = 1
  queryParams.pageSize = 10
  queryParams.content = undefined
  queryParams.topicClassification = undefined
  queryParams.type = undefined
  handleQuery()
}

// 处理文件导入
const handleImport = async (options) => {
  const file = options.file
  if (file) {
    // 检查文件类型
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel'
    if (!isExcel) {
      ElMessage.error('只能上传Excel文件!')
      return
    }
    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过2MB')
      return
    }
    // 上传到服务器
    const formData = new FormData()
    formData.append('file', file)
    try {
      const response = await importQuestions(formData)
      if (response.code === 0) {
        ElMessage.success('导入成功')
        getList() // 刷新列表
      } else {
        ElMessage.error(response.msg || '导入失败')
      }
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败，请重试')
    }
  }
}

// 导入前校验
const beforeImport = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('文件大小不能超过 2MB!')
    return false
  }
  return true
}

// 添加题目
const handleAdd = () => {
  router.push('/aqsystem/exams/questionForm')
}

// 修改题目
const handleUpdate = (row) => {
  console.log('编辑题目数据:', row) // 添加日志
  router.push({
    path: '/aqsystem/exams/questionForm',
    query: {
      id: row.questionId,
      type: row.type,
      topicClassification: row.topicClassification,
      content: row.content,
      options: row.options,
      analysis: row.analysis,
      answer: row.answer
    }
  })
}

// 删除题目
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该题目吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await delQuestions(row.questionId)
      ElMessage.success('删除成功')
      getList() // 刷新列表
    } catch (error) {
      console.error('删除题目失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }).catch(() => {
    // 取消删除操作
  })
}

const getTagType = (type) => {
  switch (type) {
    case '单选题':
      return 'success'  // 填空题 - 绿色
    case '简答题':
      return 'primary'  // 单选题 - 蓝色
    case '多选题':
      return 'warning'  // 多选题 - 黄色
    case '填空题':
      return 'danger'   // 不定项选择题 - 红色
    default:
      return 'info'     // 默认 - 灰色
  }
}

const getTypeLabel = (type) => {
  const option = questionTypeOptions.value.find(opt => opt.value === type)
  return option ? option.label : type
}

onMounted(() => {
  getList()
  getCategoryOptions() // 获取类目列表
})

// 添加 onActivated 钩子，在组件被激活时刷新数据
onActivated(() => {
  console.log('组件被激活，刷新列表数据')
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-subtitle {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-container {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 10px;
}

.filter-select {
  width: 200px;
}

.table-container {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.table-count {
  font-size: 14px;
  color: #909399;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-tag) {
  margin-right: 5px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button--text) {
  padding: 0;
  margin: 0 5px;
}
</style>
