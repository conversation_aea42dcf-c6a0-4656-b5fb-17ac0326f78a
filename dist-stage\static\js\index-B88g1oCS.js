import{_ as re,a as de,r as s,C as ce,F as pe,e as u,G as me,c as C,i as f,f as e,h as t,n as i,m as fe,o as v,H as N,I as z,j as Q,J as _e,t as M,K as g,v as ve,x as ge,E as be}from"./index-BJsoK47l.js";import{l as he,a as ye,d as we}from"./questionnaire-D6WvR6WF.js";const Ve=y=>(ve("data-v-d19e4051"),y=y(),ge(),y),ke={class:"app-container"},Ce={class:"page-header"},xe=Ve(()=>f("h1",{class:"page-title"},"问卷管理4",-1)),qe={class:"form-row"},Ie={class:"form-row"},Se={class:"form-row"},Te={class:"pagination-container"},Ue={class:"dialog-footer"},Ne={class:"dialog-footer"},ze={__name:"index",setup(y){const x=de(),b=s(!1),R=s(0),h=s([]),O=s(""),w=s(!1),V=s(!1),r=s({}),p=s({newTitle:"",copyQuestions:!0,originalId:null}),L=s({title:[{required:!0,message:"问卷名称不能为空",trigger:"blur"}],type:[{required:!0,message:"请选择问卷类型",trigger:"change"}],timeRange:[{required:!0,message:"请选择问卷时间",trigger:"change"}]}),Y=s({newTitle:[{required:!0,message:"新问卷名称不能为空",trigger:"blur"}]}),n=ce({pageNum:1,pageSize:10,title:void 0,status:void 0,labelId:void 0}),q=s([]),K=[{value:"进行中",label:"进行中"},{value:"未开始",label:"未开始"},{value:"已结束",label:"已结束"}];function P(o){switch(o){case"进行中":return"success";case"未开始":return"info";case"已结束":return"danger";default:return""}}function $(){ye().then(o=>{o.code===200?q.value=o.rows.map(a=>({value:a.labelId,label:a.labelName})):g.error(o.msg||"获取问卷类型失败")}).catch(o=>{console.error("获取问卷类型失败:",o),g.error("获取问卷类型失败")})}function _(){b.value=!0,he(n).then(o=>{h.value=o.rows,R.value=o.total,b.value=!1}).catch(()=>{b.value=!1})}function I(){n.pageNum=1,_()}function j(){n.title=void 0,n.labelId=void 0,n.status=void 0,n.classId=void 0,I()}const G=()=>{x.push("/aqsystem/questionnaires/edit")};function H(o){x.push({path:"/aqsystem/questionnaires/edit",query:{questionnaireId:o.questionnaireId}})}function J(o){x.push({path:"/aqsystem/questionnaires/view",query:{questionnaireId:o.questionnaireId}})}function A(o){be.confirm('是否确认删除问卷名称为"'+o.title+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{b.value=!0,we(o.questionnaireId).then(a=>{a.code===200?(g.success("删除成功"),_()):g.error(a.msg||"删除失败")}).catch(a=>{console.error("删除问卷失败:",a),g.error("删除失败")}).finally(()=>{b.value=!1})}).catch(()=>{})}function W(){r.value={id:void 0,title:void 0,type:void 0,description:void 0,timeRange:void 0}}function X(){w.value=!1,W()}function Z(){V.value=!1,p.value={newTitle:"",copyQuestions:!0,originalId:null}}function ee(){g.success("保存成功"),w.value=!1,_()}function le(){const o=h.value.find(a=>a.id===p.value.originalId);if(o){const a={...o,id:h.value.length+1,title:p.value.newTitle,createTime:new Date().toISOString().split("T")[0],participantCount:0};h.value.push(a)}g.success("复制成功"),V.value=!1,_()}function te(o){n.pageSize=o,_()}function ae(o){n.pageNum=o,_()}return pe(()=>{_(),$()}),(o,a)=>{const d=u("el-button"),k=u("el-input"),c=u("el-form-item"),S=u("el-option"),T=u("el-select"),U=u("el-form"),D=u("el-card"),m=u("el-table-column"),B=u("el-tag"),oe=u("el-table"),ne=u("el-pagination"),ue=u("el-date-picker"),F=u("el-dialog"),ie=u("el-checkbox"),se=me("loading");return v(),C("div",ke,[f("div",Ce,[xe,e(d,{type:"primary",icon:"Plus",class:"create-btn",onClick:G},{default:t(()=>[i("创建问卷")]),_:1})]),e(D,{class:"filter-container",shadow:"hover"},{default:t(()=>[e(U,{inline:!0,model:n,class:"search-form"},{default:t(()=>[f("div",qe,[e(c,{label:"问卷名称"},{default:t(()=>[e(k,{modelValue:n.title,"onUpdate:modelValue":a[0]||(a[0]=l=>n.title=l),placeholder:"搜索问卷名称...",clearable:"","prefix-icon":"Search",onKeyup:fe(I,["enter"])},null,8,["modelValue"])]),_:1}),e(c,{label:"问卷类型"},{default:t(()=>[e(T,{modelValue:n.labelId,"onUpdate:modelValue":a[1]||(a[1]=l=>n.labelId=l),placeholder:"所有类型",clearable:"",class:"filter-select"},{default:t(()=>[(v(!0),C(N,null,z(q.value,l=>(v(),Q(S,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),f("div",Ie,[e(c,{label:"问卷状态"},{default:t(()=>[e(T,{modelValue:n.status,"onUpdate:modelValue":a[2]||(a[2]=l=>n.status=l),placeholder:"所有状态",clearable:"",class:"filter-select"},{default:t(()=>[(v(),C(N,null,z(K,l=>e(S,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),f("div",Se,[e(c,{class:"search-buttons"},{default:t(()=>[e(d,{type:"primary",icon:"Search",onClick:I},{default:t(()=>[i("搜索")]),_:1}),e(d,{icon:"Refresh",onClick:j},{default:t(()=>[i("重置")]),_:1})]),_:1})])]),_:1},8,["model"])]),_:1}),e(D,{class:"box-card",shadow:"hover"},{default:t(()=>[_e((v(),Q(oe,{data:h.value,style:{width:"100%"}},{default:t(()=>[e(m,{type:"index",label:"序号",width:"50",align:"center"}),e(m,{prop:"title",label:"问卷名称","min-width":"180","show-overflow-tooltip":""}),e(m,{prop:"description",label:"问卷说明","min-width":"200","show-overflow-tooltip":""}),e(m,{prop:"lableName",label:"问卷类型",width:"120",align:"center"},{default:t(l=>[e(B,{type:l.row.lableName==="收集问卷"?"success":"warning"},{default:t(()=>[i(M(l.row.lableName),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(l=>[e(B,{type:P(l.row.status)},{default:t(()=>[i(M(l.row.status),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"startTime",label:"开始时间",width:"180",align:"center"}),e(m,{prop:"endTime",label:"结束时间",width:"180",align:"center"}),e(m,{label:"操作",width:"220",align:"center",fixed:"right"},{default:t(l=>[e(d,{type:"primary",link:"",icon:"Edit",onClick:E=>H(l.row)},{default:t(()=>[i("编辑")]),_:2},1032,["onClick"]),e(d,{type:"primary",link:"",icon:"View",onClick:E=>J(l.row)},{default:t(()=>[i("查看")]),_:2},1032,["onClick"]),e(d,{type:"danger",link:"",icon:"Delete",onClick:E=>A(l.row)},{default:t(()=>[i("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[se,b.value]]),f("div",Te,[e(ne,{"current-page":n.pageNum,"onUpdate:currentPage":a[3]||(a[3]=l=>n.pageNum=l),"page-size":n.pageSize,"onUpdate:pageSize":a[4]||(a[4]=l=>n.pageSize=l),"page-sizes":[10,20,30,50],total:R.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:te,onCurrentChange:ae,background:""},null,8,["current-page","page-size","total"])])]),_:1}),e(F,{title:O.value,modelValue:w.value,"onUpdate:modelValue":a[9]||(a[9]=l=>w.value=l),width:"500px","append-to-body":"",class:"questionnaire-dialog"},{footer:t(()=>[f("div",Ue,[e(d,{onClick:X},{default:t(()=>[i("取 消")]),_:1}),e(d,{type:"primary",onClick:ee},{default:t(()=>[i("确 定")]),_:1})])]),default:t(()=>[e(U,{ref:"questionnaireFormRef",model:r.value,rules:L.value,"label-width":"80px"},{default:t(()=>[e(c,{label:"问卷名称",prop:"title"},{default:t(()=>[e(k,{modelValue:r.value.title,"onUpdate:modelValue":a[5]||(a[5]=l=>r.value.title=l),placeholder:"请输入问卷名称"},null,8,["modelValue"])]),_:1}),e(c,{label:"问卷类型",prop:"type"},{default:t(()=>[e(T,{modelValue:r.value.type,"onUpdate:modelValue":a[6]||(a[6]=l=>r.value.type=l),placeholder:"请选择问卷类型",class:"full-width"},{default:t(()=>[(v(!0),C(N,null,z(q.value,l=>(v(),Q(S,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"问卷时间",prop:"timeRange"},{default:t(()=>[e(ue,{modelValue:r.value.timeRange,"onUpdate:modelValue":a[7]||(a[7]=l=>r.value.timeRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",class:"full-width"},null,8,["modelValue"])]),_:1}),e(c,{label:"问卷说明",prop:"description"},{default:t(()=>[e(k,{modelValue:r.value.description,"onUpdate:modelValue":a[8]||(a[8]=l=>r.value.description=l),type:"textarea",rows:4,placeholder:"请输入问卷说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),e(F,{title:"复制问卷",modelValue:V.value,"onUpdate:modelValue":a[12]||(a[12]=l=>V.value=l),width:"500px","append-to-body":"",class:"questionnaire-dialog"},{footer:t(()=>[f("div",Ne,[e(d,{onClick:Z},{default:t(()=>[i("取 消")]),_:1}),e(d,{type:"primary",onClick:le},{default:t(()=>[i("确 定")]),_:1})])]),default:t(()=>[e(U,{ref:"copyFormRef",model:p.value,rules:Y.value,"label-width":"100px"},{default:t(()=>[e(c,{label:"新问卷名称",prop:"newTitle"},{default:t(()=>[e(k,{modelValue:p.value.newTitle,"onUpdate:modelValue":a[10]||(a[10]=l=>p.value.newTitle=l),placeholder:"请输入新问卷名称"},null,8,["modelValue"])]),_:1}),e(c,{label:"复制题目"},{default:t(()=>[e(ie,{modelValue:p.value.copyQuestions,"onUpdate:modelValue":a[11]||(a[11]=l=>p.value.copyQuestions=l)},{default:t(()=>[i("复制原问卷的所有题目")]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}},De=re(ze,[["__scopeId","data-v-d19e4051"]]);export{De as default};
