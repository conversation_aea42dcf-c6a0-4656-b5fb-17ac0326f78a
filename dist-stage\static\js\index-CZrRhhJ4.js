import{Z as j,_ as ze,B as Be,d as Fe,r as v,C as Ge,a5 as Ke,w as Me,F as We,e as y,G as ce,c as f,o as c,J as x,f as a,a4 as M,l as u,h as s,m as He,n as h,j as D,D as F,i as m,t as q,H as L,I as R,k as U,v as Ze,x as Xe}from"./index-BJsoK47l.js";import{W as ne}from"./style-CI2cvTa4.js";function Ye(g){return j({url:"/project/page/list",method:"get",params:g})}function et(g){return j({url:"/project/"+g,method:"get"})}function tt(g){return j({url:"/project/add",method:"post",data:g})}function lt(g){return j({url:`/project/${g.proId}`,method:"put",data:g})}function ot(g){return j({url:`/project/delete/${g}`,method:"delete"})}function at(g){return j({url:"/project/selectByContent",method:"post",data:g})}function nt(g){return j({url:"/category/list",method:"get",params:g})}const A=g=>(Ze("data-v-6c090ca4"),g=g(),Xe(),g),st={class:"app-container"},ut={style:{"max-width":"400px","white-space":"pre-wrap"}},rt=A(()=>m("h4",null,"项目内容详情",-1)),it={key:0},ct={key:0,style:{"margin-left":"20px",color:"#666"}},dt={key:1},mt={class:"text-ellipsis"},pt={style:{"max-width":"400px","white-space":"pre-wrap"}},vt=A(()=>m("h4",null,"项目问题",-1)),ft={key:0},gt={key:0,style:{"margin-left":"20px",color:"#666"}},_t={key:1},ht={class:"text-ellipsis"},yt={class:"detail-container"},wt={class:"detail-input",style:{"margin-top":"20px"}},qt={key:0,class:"detail-list"},Ct=A(()=>m("h4",null,"已添加的内容：",-1)),kt={class:"detail-index"},bt={class:"detail-content"},It={key:0,class:"content-questions"},Nt=A(()=>m("h5",null,"相关问题：",-1)),St={class:"question-content"},$t={class:"question-text"},xt={key:0,class:"answer-text"},Ot={style:{"white-space":"pre-wrap"}},Et={class:"question-actions"},Vt={class:"detail-actions"},jt={class:"question-editor"},Qt={class:"editor-section"},Jt=A(()=>m("h4",null,"问题内容",-1)),Tt={class:"editor-section"},Pt=A(()=>m("h4",null,"答案内容",-1)),Dt={class:"dialog-footer"},Lt=Be({name:"Project"}),Rt=Object.assign(Lt,{setup(g){const{proxy:r}=Fe(),se=v(JSON.parse(JSON.stringify([]))),W=v(JSON.parse(JSON.stringify([]))),z=v(JSON.parse(JSON.stringify([]))),I=v(!1),Q=v(!0),G=v(!0),H=v([]),de=v(!0),me=v(!0),Z=v(0),X=v(""),N=v(JSON.parse(JSON.stringify(""))),k=v(JSON.parse(JSON.stringify(""))),S=v(JSON.parse(JSON.stringify(!1))),pe=v(JSON.parse(JSON.stringify([]))),J=v(null),Y=v("编辑问题"),O=v("question"),$=v(""),B=v(null),E=v(null),ve=Ge({form:{},tempContents:[],tempQuestions:[],queryParams:{pageNum:1,pageSize:10,keyword:null},rules:{name:[{required:!0,message:"项目名称不能为空",trigger:"blur"}],startTime:[{required:!0,message:"开始时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}],catName:[{required:!0,message:"职位类别不能为空",trigger:"blur"}]}}),{queryParams:C,form:d,rules:fe,tempContents:i,tempQuestions:ee}=Ke(ve);function V(){Q.value=!0,Ye(C.value).then(t=>{console.log("获取到的项目列表数据:",t),t.code===0?(se.value=t.data.records,W.value=se.value.map(e=>{const l=e.contents.reduce((p,n)=>(n.questions&&n.questions.length>0&&p.push(...n.questions),p),[]);return{...e,contents:e.contents||[],firstContent:e.contents&&e.contents.length>0?e.contents[0].text:"暂无详细说明",questionsList:l}}),console.log("处理后的表格数据:",W.value),Z.value=t.data.total||t.data.records.length,C.value.pageNum=t.data.current,C.value.pageSize=t.data.size):r.$modal.msgError(t.message||"获取项目列表失败"),Q.value=!1}).catch(t=>{console.error("获取项目列表错误:",t),r.$modal.msgError("获取项目列表失败"),Q.value=!1})}function ue(){nt().then(t=>{console.log("职位类别列表响应:",t),t.code===0?(z.value=t.data.map(e=>({value:e.catId,label:e.name})),console.log("处理后的选项:",z.value)):r.$modal.msgError(t.message||"获取职位类别列表失败")}).catch(t=>{console.error("获取职位类别列表错误:",t),r.$modal.msgError("获取职位类别列表失败")})}function ge(){I.value=!1,te()}function te(){console.log("重置表单前的数据:",{form:d.value,tempContents:i.value,tempQuestions:ee.value}),d.value={proId:null,name:null,startTime:null,endTime:null,catName:null,content:null,segments:[]},i.value=[],ee.value=[],N.value="",k.value="",S.value=!1,pe.value=[],console.log("重置表单后的数据:",{form:d.value,tempContents:i.value,tempQuestions:ee.value}),r.resetForm("projectRef")}function le(){C.value.pageNum=1,V()}function _e(){r.resetForm("queryRef"),le()}function he(t){H.value=t.map(e=>e.proId),de.value=t.length!=1,me.value=!t.length}function ye(){te(),I.value=!0,X.value="添加项目"}async function we(t){console.log("开始修改操作，行数据:",t),te();const e=t.proId||H.value;console.log("要查询的项目ID:",e);try{await ue();const l=await et(e);if(console.log("获取到的项目数据:",l),l.code===0){if(d.value={proId:l.data.proId,name:l.data.name,startTime:l.data.startTime,endTime:l.data.endTime,catId:l.data.catId,catName:l.data.catName},console.log("设置form后的数据:",d.value),l.data.catId){const p=z.value.find(n=>n.value===l.data.catId);p?(d.value.catId=p.value,d.value.catName=p.label,console.log("设置的职位类别:",d.value.catId,d.value.catName)):console.warn("未找到对应的职位类别:",l.data.catId)}l.data.contents&&l.data.contents.length>0&&(i.value=l.data.contents.map(p=>({conId:p.conId,text:p.text,contentOrder:p.contentOrder,questions:p.questions?p.questions.map(n=>({queId:n.queId,question:n.question,answer:n.answer||"",questionOrder:n.questionOrder,conId:n.conId})):[]})),console.log("设置的详细说明数据:",i.value)),I.value=!0,X.value="修改项目"}else r.$modal.msgError(l.message||"获取项目信息失败")}catch(l){console.error("获取数据错误:",l),r.$modal.msgError("获取数据失败")}}function qe(){r.$refs.projectRef.validate(t=>{if(t){const e={proId:d.value.proId,name:d.value.name,startTime:d.value.startTime,endTime:d.value.endTime,catId:d.value.catId,catName:d.value.catName,contents:i.value.map((l,p)=>({conId:l.conId,text:l.text,contentOrder:p+1,questions:l.questions?l.questions.map((n,b)=>({queId:n.queId,question:n.question,answer:n.answer||"",questionOrder:b+1,conId:n.conId})):[]}))};console.log("提交的数据:",e),d.value.proId!=null?lt(e).then(l=>{l.code===0?(r.$modal.msgSuccess("修改成功"),I.value=!1,V()):r.$modal.msgError(l.message||"修改失败")}):tt(e).then(l=>{l.code===0?(r.$modal.msgSuccess("新增成功"),I.value=!1,V()):r.$modal.msgError(l.message||"新增失败")})}})}function Ce(t){const e=t.proId||H.value;r.$modal.confirm('是否确认删除项目编号为"'+e+'"的数据项？').then(function(){return ot(e)}).then(()=>{V(),r.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ke(){if(!N.value.trim()){r.$modal.msgError("项目内容不能为空");return}i.value.push({text:N.value,contentOrder:i.value.length+1}),N.value=""}function re(t){r.$modal.confirm("是否确认删除该条项目内容？").then(()=>{i.value.splice(t,1),i.value.forEach((e,l)=>{e.contentOrder=l+1}),r.$modal.msgSuccess("删除成功")}).catch(()=>{})}function be(t){S.value=!0,J.value=t,Y.value="编辑问题",O.value="question",k.value="",$.value=""}function Ie(t,e,l){S.value=!0,Y.value="编辑问题",O.value="question",k.value=t.question||t.text,B.value=e,E.value=l}function Ne(){if(!k.value.trim()){r.$modal.msgError("项目问题不能为空");return}const t={question:k.value,answer:"",questionOrder:0};typeof J.value=="number"&&i.value[J.value]&&(i.value[J.value].questions||(i.value[J.value].questions=[]),i.value[J.value].questions.push(t)),k.value="",r.$modal.msgSuccess("添加问题成功"),S.value=!1}function Se(t,e,l){S.value=!0,Y.value="编辑答案",O.value="answer",$.value=t.answer||"",B.value=e,E.value=l}function $e(){if(typeof B.value=="number"&&typeof E.value=="number"&&i.value[E.value]&&i.value[E.value].questions){const t=i.value[E.value].questions[B.value];t.answer=$.value,r.$modal.msgSuccess("答案保存成功"),S.value=!1,O.value="question",$.value=""}}Me(S,t=>{t||(O.value="question",k.value="",$.value="",B.value=null,E.value=null)});function xe(t,e){r.$modal.confirm("是否确认删除该条问题？").then(()=>{i.value[e]&&i.value[e].questions&&(i.value[e].questions.splice(t,1),r.$modal.msgSuccess("删除成功"))}).catch(()=>{})}function Oe(t){console.log("选择的职位类别ID:",t);const e=z.value.find(l=>l.value===t);e&&(d.value.catId=e.value,d.value.catName=e.label,console.log("设置的职位类别:",d.value.catId,d.value.catName))}function Ee(t,e){N.value=t.text,re(e)}function Ve(t){console.log("开始生成问题，内容:",t),r.$modal.confirm("是否确认生成项目问题？").then(()=>{Q.value=!0,at(t.text).then(e=>{if(console.log("生成问题接口返回数据:",e),e.code===0&&e.data)try{const l=JSON.parse(e.data[0]);if(l.questions&&Array.isArray(l.questions)){const p=l.questions.map((b,oe)=>({question:b.question,answer:b.answer||"",questionOrder:oe+1})),n=i.value.findIndex(b=>b.text===t.text);n!==-1&&(i.value[n].questions||(i.value[n].questions=[]),console.log("tempContents.value[contentIndex].questions isExtensible before push:",Object.isExtensible(i.value[n].questions)),i.value[n].questions.push(...p)),r.$modal.msgSuccess("生成问题成功")}else console.log("解析后的数据格式不正确:",l),r.$modal.msgError("生成问题失败：数据格式不正确")}catch(l){console.error("解析响应数据失败:",l),r.$modal.msgError("生成问题失败：数据解析错误")}else console.log("返回的数据格式不正确，response:",e),r.$modal.msgError(e.msg||"生成问题失败")}).catch(e=>{console.error("生成问题错误:",e),e.code==="ECONNABORTED"?r.$modal.msgError("生成问题超时，请稍后重试"):r.$modal.msgError("生成问题失败")}).finally(()=>{Q.value=!1})}).catch(()=>{})}return We(()=>{ue()}),V(),(t,e)=>{const l=y("el-input"),p=y("el-form-item"),n=y("el-button"),b=y("el-form"),oe=y("el-col"),je=y("right-toolbar"),Qe=y("el-row"),T=y("el-table-column"),ie=y("el-tooltip"),Je=y("el-table"),Te=y("pagination"),Pe=y("el-option"),De=y("el-select"),Le=y("el-collapse-item"),Re=y("el-collapse"),Ue=y("el-dialog"),ae=ce("hasPermi"),Ae=ce("loading");return c(),f("div",st,[x(a(b,{model:u(C),ref:"queryRef",inline:!0,"label-width":"68px"},{default:s(()=>[a(p,{label:"项目名称",prop:"name"},{default:s(()=>[a(l,{modelValue:u(C).name,"onUpdate:modelValue":e[0]||(e[0]=o=>u(C).name=o),placeholder:"请输入项目名称",clearable:"",onKeyup:He(le,["enter"])},null,8,["modelValue"])]),_:1}),a(p,null,{default:s(()=>[a(n,{type:"primary",icon:"Search",onClick:le},{default:s(()=>[h("搜索")]),_:1}),a(n,{icon:"Refresh",onClick:_e},{default:s(()=>[h("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[M,u(G)]]),a(Qe,{gutter:10,class:"mb8"},{default:s(()=>[a(oe,{span:1.5},{default:s(()=>[x((c(),D(n,{type:"primary",plain:"",icon:"Plus",onClick:ye},{default:s(()=>[h("新增")]),_:1})),[[ae,["system:project:add"]]])]),_:1}),a(je,{showSearch:u(G),"onUpdate:showSearch":e[1]||(e[1]=o=>F(G)?G.value=o:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),x((c(),D(Je,{data:u(W),onSelectionChange:he},{default:s(()=>[a(T,{type:"selection",width:"30",align:"center"}),a(T,{label:"项目名称",align:"center",prop:"name",width:"140"}),a(T,{label:"职位类别",width:"160",align:"center",prop:"catName"}),a(T,{label:"项目内容",align:"center",width:"300"},{default:s(o=>[a(ie,{effect:"dark",placement:"top"},{content:s(()=>[m("div",ut,[rt,o.row.contents&&o.row.contents.length>0?(c(),f("div",it,[(c(!0),f(L,null,R(o.row.contents,(_,w)=>(c(),f("div",{key:w},[h(q(w+1)+". "+q(_.text)+" ",1),_.questions&&_.questions.length>0?(c(),f("div",ct,[(c(!0),f(L,null,R(_.questions,(P,K)=>(c(),f("div",{key:K}," - "+q(P.question),1))),128))])):U("",!0)]))),128))])):(c(),f("div",dt,"暂无详细说明"))])]),default:s(()=>[m("div",mt,q(o.row.firstContent),1)]),_:2},1024)]),_:1}),a(T,{label:"项目问题",align:"center",width:"300"},{default:s(o=>[a(ie,{effect:"dark",placement:"top"},{content:s(()=>[m("div",pt,[vt,o.row.questionsList&&o.row.questionsList.length>0?(c(),f("div",ft,[(c(!0),f(L,null,R(o.row.questionsList,(_,w)=>(c(),f("div",{key:w},[h(q(w+1)+". "+q(_.question)+" ",1),_.answer?(c(),f("div",gt," 答案: "+q(_.answer),1)):U("",!0)]))),128))])):(c(),f("div",_t,"暂无问题"))])]),default:s(()=>[m("div",ht,q(o.row.questionsList&&o.row.questionsList.length>0?o.row.questionsList[0].question:"暂无问题"),1)]),_:2},1024)]),_:1}),a(T,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:s(o=>[x((c(),D(n,{link:"",type:"primary",icon:"Edit",onClick:_=>we(o.row)},{default:s(()=>[h("修改")]),_:2},1032,["onClick"])),[[ae,["system:project:edit"]]]),x((c(),D(n,{link:"",type:"primary",icon:"Delete",onClick:_=>Ce(o.row)},{default:s(()=>[h("删除")]),_:2},1032,["onClick"])),[[ae,["system:project:remove"]]])]),_:1})]),_:1},8,["data"])),[[Ae,u(Q)]]),x(a(Te,{total:u(Z),page:u(C).pageNum,"onUpdate:page":e[2]||(e[2]=o=>u(C).pageNum=o),limit:u(C).pageSize,"onUpdate:limit":e[3]||(e[3]=o=>u(C).pageSize=o),onPagination:V},null,8,["total","page","limit"]),[[M,u(Z)>0]]),a(Ue,{title:u(X),modelValue:u(I),"onUpdate:modelValue":e[9]||(e[9]=o=>F(I)?I.value=o:null),width:"1080px","append-to-body":""},{footer:s(()=>[m("div",Dt,[a(n,{type:"primary",onClick:qe},{default:s(()=>[h("确 定")]),_:1}),a(n,{onClick:ge},{default:s(()=>[h("取 消")]),_:1})])]),default:s(()=>[a(b,{ref:"projectRef",model:u(d),rules:u(fe),"label-width":"80px"},{default:s(()=>[a(p,{label:"项目名称",prop:"name"},{default:s(()=>[a(l,{modelValue:u(d).name,"onUpdate:modelValue":e[4]||(e[4]=o=>u(d).name=o),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1}),a(p,{label:"职位类别",prop:"catName"},{default:s(()=>[a(De,{modelValue:u(d).catName,"onUpdate:modelValue":e[5]||(e[5]=o=>u(d).catName=o),placeholder:"请选择职位类别",clearable:"",onChange:Oe},{default:s(()=>[(c(!0),f(L,null,R(u(z),o=>(c(),D(Pe,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"项目内容",prop:"contents"},{default:s(()=>[m("div",yt,[m("div",wt,[a(u(ne),{modelValue:u(N),"onUpdate:modelValue":e[6]||(e[6]=o=>F(N)?N.value=o:null),language:"zh-CN","preview-theme":"default","code-theme":"github",style:{height:"200px"}},null,8,["modelValue"]),a(n,{type:"primary",onClick:ke,style:{"margin-top":"10px"}},{default:s(()=>[h("添加说明")]),_:1})])]),u(i)&&u(i).length>0?(c(),f("div",qt,[Ct,(c(!0),f(L,null,R(u(i),(o,_)=>(c(),f("div",{key:_,class:"detail-item"},[m("div",kt,q(_+1)+".",1),m("div",bt,[m("div",null,q(o.text),1),o.questions&&o.questions.length>0?(c(),f("div",It,[Nt,(c(!0),f(L,null,R(o.questions,(w,P)=>(c(),f("div",{key:P,class:"question-item"},[m("div",St,[m("div",$t,q(w.question),1),w.answer?(c(),f("div",xt,[a(Re,null,{default:s(()=>[a(Le,{title:"查看答案"},{default:s(()=>[m("div",Ot,q(w.answer),1)]),_:2},1024)]),_:2},1024)])):U("",!0)]),m("div",Et,[a(n,{link:"",type:"primary",onClick:K=>Ie(w,P,_)},{default:s(()=>[h("编辑")]),_:2},1032,["onClick"]),a(n,{link:"",type:"primary",onClick:K=>Se(w,P,_)},{default:s(()=>[h("编辑答案")]),_:2},1032,["onClick"]),a(n,{link:"",type:"danger",onClick:K=>xe(P,_)},{default:s(()=>[h("删除")]),_:2},1032,["onClick"])])]))),128))])):U("",!0)]),m("div",Vt,[a(n,{link:"",type:"primary",onClick:w=>Ee(o,_)},{default:s(()=>[h("编辑")]),_:2},1032,["onClick"]),a(n,{link:"",type:"primary",onClick:w=>be(_)},{default:s(()=>[h("编辑问题")]),_:2},1032,["onClick"]),a(n,{link:"",type:"primary",onClick:w=>Ve(o)},{default:s(()=>[h("生成问题")]),_:2},1032,["onClick"]),a(n,{link:"",type:"danger",onClick:w=>re(_)},{default:s(()=>[h("删除")]),_:2},1032,["onClick"])])]))),128))])):U("",!0)]),_:1}),u(S)?(c(),D(p,{key:0,label:"项目问题",prop:"questions"},{default:s(()=>[m("div",jt,[x(m("div",Qt,[Jt,a(u(ne),{modelValue:u(k),"onUpdate:modelValue":e[7]||(e[7]=o=>F(k)?k.value=o:null),language:"zh-CN","preview-theme":"default","code-theme":"github",style:{height:"200px"}},null,8,["modelValue"]),a(n,{type:"primary",onClick:Ne,style:{"margin-top":"10px"}},{default:s(()=>[h("添加问题")]),_:1})],512),[[M,u(O)==="question"]]),x(m("div",Tt,[Pt,a(u(ne),{modelValue:u($),"onUpdate:modelValue":e[8]||(e[8]=o=>F($)?$.value=o:null),language:"zh-CN","preview-theme":"default","code-theme":"github",style:{height:"200px"}},null,8,["modelValue"]),a(n,{type:"primary",onClick:$e,style:{"margin-top":"10px"}},{default:s(()=>[h("保存答案")]),_:1})],512),[[M,u(O)==="answer"]])])]),_:1})):U("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),zt=ze(Rt,[["__scopeId","data-v-6c090ca4"]]);export{zt as default};
