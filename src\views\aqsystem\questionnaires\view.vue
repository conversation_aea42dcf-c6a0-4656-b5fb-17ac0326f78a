<template>
  <div class="app-container">
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ questionnaireInfo.title }}</h1>
        <p class="page-subtitle">
          <span class="info-item">班级：{{ questionnaireInfo.className }}</span>
          <span class="info-item">类型：{{ questionnaireInfo.lableName }}</span>
          <span class="info-item">状态：<el-tag :type="getStatusType(questionnaireInfo.status)">{{ questionnaireInfo.status }}</el-tag></span>
        </p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>导出数据
        </el-button>
      </div>
    </div>

    <!-- 问卷概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>总人数</span>
              <el-icon><User /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ statistics.totalCount }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>已提交</span>
              <el-icon><Check /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ statistics.submittedCount }}</div>
          <div class="card-footer">
            <el-progress 
              :percentage="questionnaireInfo.completionRate" 
              :color="getProgressColor(statistics.submittedCount, statistics.totalCount)"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>完成率</span>
              <el-icon><DataLine /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ questionnaireInfo.completionRate }}%</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="overview-card">
          <template #header>
            <div class="card-header">
              <span>平均用时</span>
              <el-icon><Timer /></el-icon>
            </div>
          </template>
          <div class="card-value">{{ questionnaireInfo.averageTime }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 答题记录列表 -->
    <el-card class="box-card" shadow="hover">
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="studentList"
          style="width: 100%"
        >
          <el-table-column type="index" label="编号" width="80" fixed="left" align="center" />
          <el-table-column label="姓名" prop="studentName" width="120" align="center" />
          <el-table-column label="手机号" prop="phone" width="150" align="center" />
          <el-table-column label="开始答题时间" prop="startTime" width="180" align="center" />
          <el-table-column label="结束答题时间" prop="endTime" width="180" align="center" />
          <el-table-column label="答题时长" prop="duration" width="120" align="center" />
          <el-table-column 
            v-for="(question, index) in questionnaireInfo.questions" 
            :key="question.questionId"
            :label="`${index + 1}. ${question.content}`"
            :prop="'answers.' + index"
            min-width="120"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column label="操作" width="100" fixed="right" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handlePreview(scope.row)"
              >
                <el-icon><View /></el-icon>预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="questionnaireInfo.title + ' - 问卷预览'"
      width="700px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="preview-content">
        <div class="questionnaire-header">
          <div class="header-title">{{ questionnaireInfo.title }}</div>
          <div class="info-tag">信息已加密</div>
        </div>

        <div class="question-list">
          <div v-for="(question, index) in questionnaireInfo.questions" :key="question.questionId" class="question-item">
            <div class="question-title">
              <span class="required">*</span>
              <span class="number">{{ String(index + 1).padStart(2, '0') }}</span>
              {{ question.content }}
            </div>
            <div class="answer text-answer">{{ previewData.answers?.[index] }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back, Download, User, Check, DataLine, Timer, View } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { getQuestionnaireDetail } from '@/api/aqsystem/questionnaire'

const route = useRoute()
const router = useRouter()

// 问卷信息
const questionnaireInfo = ref({
  questionnaireId: '',
  title: '',
  lableName: '',
  status: '',
  className: '',
  totalCount: 0,
  submittedCount: 0,
  completionRate: 0,
  averageTime: '',
  questions: [],
  scoresVos: [],
  answerRows: []
})

// 统计数据
const statistics = ref({
  totalCount: 0,
  submittedCount: 0,
  averageDuration: 0
})

// 遮罩层
const loading = ref(false)
// 总条数
const total = ref(0)
// 学生列表数据
const studentList = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 预览对话框
const previewDialogVisible = ref(false)
const previewData = ref({})

/** 获取问卷类型文本 */
function getTypeText(type) {
  return type || '未知类型'
}

/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case '进行中':
      return 'success'
    case '未开始':
      return 'info'
    case '已结束':
      return 'danger'
    default:
      return ''
  }
}

/** 获取状态文本 */
function getStatusText(status) {
  return status || '未知状态'
}

/** 获取进度条颜色 */
function getProgressColor(current, total) {
  const percentage = (current / total) * 100
  if (percentage < 30) {
    return '#f56c6c'
  } else if (percentage < 70) {
    return '#e6a23c'
  } else {
    return '#67c23a'
  }
}

/** 格式化答题用时 */
function formatDuration(duration) {
  return duration || '0秒'
}

/** 获取问卷详情 */
async function getQuestionnaireDetailData(questionnaireId) {
  loading.value = true
  try {
    const response = await getQuestionnaireDetail(questionnaireId)
    if (response.code === 200 && response.data) {
      const data = response.data
      // 设置问卷基本信息
      questionnaireInfo.value = {
        questionnaireId: data.questionnaireId || '',
        title: data.title || '',
        lableName: data.lableName || '',
        status: data.status || '',
        className: data.className || '',
        totalCount: data.totalCount || 0,
        submittedCount: data.submittedCount || 0,
        completionRate: data.completionRate || 0,
        averageTime: data.averageTime || '0秒',
        questions: data.questions || [],
        scoresVos: data.scoresVos || [],
        answerRows: data.answerRows || []
      }
      
      // 更新统计数据
      statistics.value = {
        totalCount: data.totalCount || 0,
        submittedCount: data.submittedCount || 0,
        averageDuration: data.averageTime || '0秒'
      }

      // 处理学生列表数据
      if (Array.isArray(data.answerRows)) {
        studentList.value = data.answerRows.map((row, index) => {
          const score = (data.scoresVos && data.scoresVos[index]) || {}
          return {
            index: row.index || index + 1,
            startTime: score.startTime || '',
            endTime: score.submitTime || '',
            duration: score.duration || '0秒',
            studentName: row.studentName || '',
            studentId: (row.answers && row.answers[1]) || '',
            phone: row.phone || '',
            gender: (row.answers && row.answers[3] === '男') ? 'male' : 'female',
            classPosition: (row.answers && row.answers[4] !== '无') || false,
            classRank: (row.answers && row.answers[5]) || '',
            javaSkill: getSkillLevel(row.answers && row.answers[6]),
            algorithmSkill: getSkillLevel(row.answers && row.answers[7]),
            careerExpectation: (row.answers && row.answers[8]) || '',
            answers: row.answers || []
          }
        })
        total.value = studentList.value.length
      } else {
        studentList.value = []
        total.value = 0
      }
    } else {
      ElMessage.error(response.msg || '获取问卷详情失败')
      questionnaireInfo.value = {
        questionnaireId: '',
        title: '',
        lableName: '',
        status: '',
        className: '',
        totalCount: 0,
        submittedCount: 0,
        completionRate: 0,
        averageTime: '0秒',
        questions: [],
        scoresVos: [],
        answerRows: []
      }
      statistics.value = {
        totalCount: 0,
        submittedCount: 0,
        averageDuration: '0秒'
      }
      studentList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取问卷详情失败:', error)
    ElMessage.error('获取问卷详情失败')
    // 重置所有数据为默认值
    questionnaireInfo.value = {
      questionnaireId: '',
      title: '',
      lableName: '',
      status: '',
      className: '',
      totalCount: 0,
      submittedCount: 0,
      completionRate: 0,
      averageTime: '0秒',
      questions: [],
      scoresVos: [],
      answerRows: []
    }
    statistics.value = {
      totalCount: 0,
      submittedCount: 0,
      averageDuration: '0秒'
    }
    studentList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 获取技能等级 */
function getSkillLevel(answer) {
  switch (answer) {
    case 'A': return 5
    case 'B': return 4
    case 'C': return 3
    case 'D': return 2
    case 'E': return 1
    default: return 0
  }
}

/** 获取Java技能等级文本 */
function getJavaSkillText(level) {
  switch (level) {
    case 5: return 'A - 精通'
    case 4: return 'B - 熟练'
    case 3: return 'C - 良好'
    case 2: return 'D - 一般'
    case 1: return 'E - 较差'
    default: return '未评估'
  }
}

/** 获取算法能力等级文本 */
function getAlgorithmSkillText(level) {
  switch (level) {
    case 5: return 'A - 精通'
    case 4: return 'B - 熟练'
    case 3: return 'C - 良好'
    case 2: return 'D - 一般'
    case 1: return 'E - 较差'
    default: return '未评估'
  }
}

/** 导出数据 */
function handleExport() {
  try {
    // 准备导出数据
    const exportData = studentList.value.map(item => {
      const row = {
        '编号': item.index,
        '姓名': item.studentName,
        '学号': item.studentId,
        '手机号': item.phone,
        '开始答题时间': item.startTime,
        '结束答题时间': item.endTime,
        '答题时长': item.duration
      }

      // 添加问卷问题答案
      if (item.answers && Array.isArray(item.answers)) {
        item.answers.forEach((answer, index) => {
          const question = questionnaireInfo.value.questions[index]
          if (question) {
            row[`${index + 1}. ${question.content}`] = answer || ''
          }
        })
      }

      return row
    })

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(exportData)
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '问卷数据')
    
    // 设置列宽
    const colWidths = [
      { wch: 8 },  // 编号
      { wch: 10 }, // 姓名
      { wch: 12 }, // 学号
      { wch: 15 }, // 手机号
      { wch: 20 }, // 开始答题时间
      { wch: 20 }, // 结束答题时间
      { wch: 12 }, // 答题时长
    ]

    // 为问题答案添加列宽
    if (questionnaireInfo.value.questions) {
      questionnaireInfo.value.questions.forEach(() => {
        colWidths.push({ wch: 30 }) // 每个问题的列宽
      })
    }

    ws['!cols'] = colWidths

    // 导出文件
    const fileName = `${questionnaireInfo.value.title}_${new Date().toLocaleDateString()}.xlsx`
    XLSX.writeFile(wb, fileName)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

/** 预览问卷 */
function handlePreview(row) {
  previewData.value = { ...row }
  previewDialogVisible.value = true
}

onMounted(() => {
  // 从路由参数中获取问卷ID
  const questionnaireId = route.query.questionnaireId
  if (questionnaireId) {
    getQuestionnaireDetailData(questionnaireId)
  } else {
    ElMessage.warning('未获取到问卷ID')
  }
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  color: #606266;
  margin: 4px 0 0;
  display: flex;
  gap: 16px;
}

.info-item {
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  border-radius: 8px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin: 16px 0;
}

.card-footer {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.box-card {
  border-radius: 8px;
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #ebeef5;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 40px;
}

.column-title {
  font-weight: 500;
  color: #606266;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 20px;
}

.preview-content {
  padding: 20px;
  background-color: #f5f7fa;
  max-height: 80vh;
  overflow-y: auto;
}

.questionnaire-header {
  text-align: center;
  margin-bottom: 30px;
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.header-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 16px;
  font-weight: bold;
}

.header-desc {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.info-tag {
  display: inline-block;
  padding: 4px 12px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-size: 13px;
}

.question-list {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.question-item {
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: 1px solid #ebeef5;
}

.question-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.question-title {
  font-size: 16px;
  color: #303133;
  margin-bottom: 20px;
  font-weight: 500;
  line-height: 1.4;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.number {
  color: #409eff;
  margin-right: 8px;
  font-weight: bold;
}

.answer {
  color: #606266;
  line-height: 1.6;
  text-align: left;
}

.text-answer {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
}

.question-desc {
  margin-top: 8px;
  color: #909399;
  font-size: 13px;
  padding-left: 4px;
  border-left: 2px solid #dcdfe6;
}

.vertical-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  text-align: left;
}

:deep(.el-radio) {
  margin: 0;
  padding: 12px 16px;
  border-radius: 4px;
  transition: all 0.3s;
  width: 100%;
  display: flex;
  align-items: flex-start;
}

:deep(.el-radio__label) {
  padding-left: 8px;
  white-space: normal;
  line-height: 1.5;
}

:deep(.el-radio__input) {
  margin-top: 2px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
}

@media (max-width: 768px) {
  .page-subtitle {
    flex-direction: column;
    gap: 4px;
  }
  
  .overview-cards .el-col {
    margin-bottom: 16px;
  }
}
</style> 