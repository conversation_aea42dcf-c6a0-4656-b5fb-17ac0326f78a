import{r as n,z as l,F as u,G as d,J as m,l as e,c as p,o as h,i as _,L as g}from"./index-BJsoK47l.js";const f=["src"],w={__name:"index",props:{src:{type:String,required:!0}},setup(s){const r=s,t=n(document.documentElement.clientHeight-94.5+"px;"),o=n(!0),i=l(()=>r.src);return u(()=>{setTimeout(()=>{o.value=!1},300),window.onresize=function(){t.value=document.documentElement.clientHeight-94.5+"px;"}}),(c,v)=>{const a=d("loading");return m((h(),p("div",{style:g("height:"+e(t))},[_("iframe",{src:e(i),frameborder:"no",style:{width:"100%",height:"100%"},scrolling:"auto"},null,8,f)],4)),[[a,e(o)]])}}};export{w as _};
