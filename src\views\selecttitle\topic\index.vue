<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键字" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入关键字/导师/标签"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:topic:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:topic:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:topic:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:topic:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="topicList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="topicId" width="50" />
      <el-table-column label="题目" align="center" prop="title" width="260"/>
      <el-table-column label="导师" align="center" prop="supervisorName" />
      <el-table-column label="类别" align="center" prop="categoryName" />
      <el-table-column label="标签" align="center" prop="tag" />


      <el-table-column label="课题状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
            {{ scope.row.status === '1' ? '可选' : '已满' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:topic:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:topic:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改选题信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="topicRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="题目" prop="title">
          <el-input v-model="form.title" placeholder="请输入题目" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="导师" prop="supervisorId">
          <el-select v-model="form.supervisorId" placeholder="请选择导师">
            <el-option
              v-for="item in supervisorOptions"
              :key="item.supervisorId"
              :label="item.supervisorName"
              :value="item.supervisorId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类别" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择类别">
            <el-option
              v-for="item in categoryOptions"
              :key="item.categoryId"
              :label="item.categoryName"
              :value="item.categoryId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="标签" prop="tag">
          <el-input v-model="form.tag" placeholder="请输入标签" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Topic">
import { listTopic, getTopic, delTopic, addTopic, updateTopic, listSupervisors, listCategories } from "@/api/selecttitle/topic"

const { proxy } = getCurrentInstance()

const topicList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const supervisorOptions = ref([])
const categoryOptions = ref([])
const submitLoading = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    tag: null,
    status: null,
  },
  rules: {
    title: [
      { required: true, message: "题目不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "描述不能为空", trigger: "blur" }
    ],
    supervisorId: [
      { required: true, message: "导师不能为空", trigger: "change" }
    ],
    categoryId: [
      { required: true, message: "类别不能为空", trigger: "change" }
    ],
    tag: [
      { required: true, message: "标签不能为空", trigger: "blur" }
    ],
    publishDate: [
      { required: true, message: "发布时间不能为空", trigger: "change" }
    ],
    deadline: [
      { required: true, message: "截止时间不能为空", trigger: "change" }
    ],
    quota: [
      { required: true, message: "名额不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询选题信息列表 */
function getList() {
  loading.value = true
  // 直接获取课题列表，后端已提供 categoryName
  listTopic(queryParams.value).then(response => {
    if (!response.data || !response.data.topics) {
      console.error('课题列表数据格式不正确:', response)
      loading.value = false
      return
    }

    const topics = response.data.topics
    console.log('原始课题列表数据:', topics)
    total.value = response.data.total

    // 对课题列表进行去重，基于 topicId
    const uniqueTopics = []
    const seenTopicIds = new Set()
    topics.forEach(topic => {
      if (!seenTopicIds.has(topic.topicId)) {
        seenTopicIds.add(topic.topicId)
        uniqueTopics.push(topic)
      }
    })

    // 直接使用后端返回的 categoryName
    topicList.value = uniqueTopics
    console.log('处理后的课题列表(直接使用后端categoryName): ', topicList.value)
    loading.value = false
  }).catch(error => {
    console.error('获取课题列表失败:', error)
    loading.value = false
  })
}

/** 获取导师和类目列表 */
function getOptions() {
  return Promise.all([
    // 获取导师列表
    listSupervisors().then(response => {
      supervisorOptions.value = response.data.topics
    }),
    // 获取类目列表
    listCategories().then(response => {
      categoryOptions.value = response.data.topics
    })
  ])
}

// 在组件挂载时获取选项数据
onMounted(() => {
  getOptions()
})

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    topicId: null,
    title: null,
    description: null,
    supervisorId: null,
    categoryId: null,
    tag: null,
    publishDate: null,
    deadline: null,
    quota: 1,
    status: 1,
    createTime: null,
    editTime: null,
    isDelete: 0
  }
  proxy.resetForm("topicRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.topicId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加选题信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _topicId = row.topicId || ids.value
  loading.value = true // 添加加载状态
  getTopic(_topicId).then(response => {
    // 确保导师和类目列表已加载
    if (supervisorOptions.value.length === 0 || categoryOptions.value.length === 0) {
      getOptions().then(() => {
        setFormData(response.data)
      }).catch(() => {
        proxy.$modal.msgError("获取导师或类目列表失败")
      }).finally(() => {
        loading.value = false // 结束加载状态
      })
    } else {
      setFormData(response.data)
      loading.value = false // 结束加载状态
    }
  }).catch(() => {
    proxy.$modal.msgError("获取课题信息失败")
    loading.value = false // 结束加载状态
  })
}

/** 设置表单数据 */
function setFormData(data) {
  console.log('Setting form data. Topic Category ID from backend:', data.categoryId, 'Type:', typeof data.categoryId)
  form.value = {
    topicId: data.topicId,
    title: data.title,
    description: data.description,
    supervisorId: data.supervisorId,
    // 将从后端获取的 categoryId 转换为数字类型，以匹配 categoryOptions 中的类型
    categoryId: data.categoryId !== null ? Number(data.categoryId) : null, 
    tag: data.tag,
    publishDate: data.publishDate,
    deadline: data.deadline,
    quota: data.quota,
    status: data.status,
    createTime: data.createTime,
    editTime: data.editTime,
    isDelete: data.isDelete
  }
  open.value = true
  title.value = "修改选题信息"
  console.log('Form data set.', form.value)
}

/** 提交按钮 */
function submitForm() {
  if (submitLoading.value) return  // 如果正在提交，直接返回
  proxy.$refs["topicRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true  // 开始提交，设置loading状态
      const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      
      // 格式化日期时间为后端所需的 "yyyy-MM-dd HH:mm:ss" 格式
      const formatDateTime = (date) => {
        if (!date) return null;
        // 确保输入是Date对象或能转换为Date对象
        const d = new Date(date);
        const year = d.getFullYear();
        const month = ('0' + (d.getMonth() + 1)).slice(-2);
        const day = ('0' + d.getDate()).slice(-2);
        const hours = ('0' + d.getHours()).slice(-2);
        const minutes = ('0' + d.getMinutes()).slice(-2);
        const seconds = ('0' + d.getSeconds()).slice(-2);
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };

      if (form.value.topicId != null) {
        // 修改操作
        const updateData = {
          ...form.value,
          editTime: currentTime,
          // 使用新的格式化函数处理日期时间
          publishDate: formatDateTime(form.value.publishDate),
          deadline: formatDateTime(form.value.deadline)
        }
        updateTopic(updateData).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        }).catch(error => {
          proxy.$modal.msgError(error.message || "修改失败")
        }).finally(() => {
          submitLoading.value = false  // 无论成功失败，都关闭loading状态
        })
      } else {
        // 新增操作
        const addData = {
          ...form.value,
          createTime: currentTime,
          editTime: currentTime,
           // 使用新的格式化函数处理日期时间
          publishDate: formatDateTime(form.value.publishDate),
          deadline: formatDateTime(form.value.deadline),
          isDelete: 0
        }
        addTopic(addData).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        }).catch(error => {
          proxy.$modal.msgError(error.message || "新增失败")
        }).finally(() => {
          submitLoading.value = false  // 无论成功失败，都关闭loading状态
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _topicIds = row.topicId || ids.value
  proxy.$modal.confirm('是否确认删除选题信息编号为"' + _topicIds + '"的数据项？').then(function() {
    return delTopic(_topicIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/topic/export', {
    ...queryParams.value
  }, `topic_${new Date().getTime()}.xlsx`)
}

getList()
</script>
