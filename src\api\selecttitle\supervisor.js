import request from '@/utils/request'

// 查询导师信息列表
export function listSupervisor(query) {
  return request({
    url: '/system/supervisor/list',
    method: 'post',
    data: {
      keyword: query.supervisorName || '',
      pageNum: query.pageNum,
      pageSize: query.pageSize
    }
  })
}

// 查询导师信息，用于记录导师的基本资料详细
export function getSupervisor(supervisorId) {
  return request({
    url: '/system/supervisor/' + supervisorId,
    method: 'get'
  })
}

// 新增导师信息
export function addSupervisor(data) {
  return request({
    url: '/system/supervisor/add',
    method: 'post',
    data: {
      supervisorName: data.supervisorName,
      department: data.department,
      title: data.title,
      email: data.email,
      phoneNumber: data.phoneNumber,
      createTime: data.createTime,
      editTime: data.editTime,
      isDelete: data.isDelete || 0
    }
  })
}

// 修改导师信息
export function updateSupervisor(data) {
  return request({
    url: '/system/supervisor/edit',
    method: 'put',
    data: {
      supervisorId: data.supervisorId,
      supervisorName: data.supervisorName,
      department: data.department,
      title: data.title,
      email: data.email,
      phoneNumber: data.phoneNumber,
      editTime: data.editTime,
      isDelete: data.isDelete || 0
    }
  })
}

// 删除导师信息，用于记录导师的基本资料
export function delSupervisor(supervisorId) {
  return request({
    url: '/system/supervisor/remove/' + supervisorId,
    method: 'delete'
  })
}
