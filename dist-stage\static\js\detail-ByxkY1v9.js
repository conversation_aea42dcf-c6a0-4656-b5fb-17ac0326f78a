import{_ as V,B as $,u as j,a as R,r as f,F as z,e as o,G as F,c as g,f as e,h as t,i,n as s,t as r,J as G,o as _,j as y,v as J,x as L}from"./index-BJsoK47l.js";import{g as M}from"./supervisor-CpA1uLH3.js";const w=d=>(J("data-v-79952ff1"),d=d(),L(),d),O={class:"app-container"},T={class:"card-header"},q=w(()=>i("span",null,"导师信息",-1)),A={class:"supervisor-info"},H=w(()=>i("div",{class:"card-header"},[i("span",null,"毕设题目列表")],-1)),K=$({name:"SupervisorDetail"}),P=Object.assign(K,{setup(d){const x=j(),v=R(),u=f(!1),n=f({}),p=f([]);function k(){u.value=!0;const m=x.params.supervisorId;if(!m){proxy.$modal.msgError("获取导师ID失败"),v.go(-1);return}M(m).then(a=>{a.code===200?(console.log("获取到的数据:",a.data),n.value=a.data.supervisor,p.value=a.data.topics||[],console.log("处理后的题目列表:",p.value)):proxy.$modal.msgError(a.message||"获取导师详情失败")}).catch(a=>{console.error("获取导师详情错误:",a),proxy.$modal.msgError("获取导师详情失败")}).finally(()=>{u.value=!1})}function I(){v.go(-1)}return z(()=>{k()}),(m,a)=>{const D=o("el-button"),c=o("el-descriptions-item"),B=o("el-descriptions"),h=o("el-card"),l=o("el-table-column"),N=o("el-tag"),S=o("el-table"),C=o("el-empty"),E=F("loading");return _(),g("div",O,[e(h,{class:"box-card"},{header:t(()=>[i("div",T,[q,e(D,{onClick:I},{default:t(()=>[s("返回")]),_:1})])]),default:t(()=>[i("div",A,[e(B,{column:3,border:""},{default:t(()=>[e(c,{label:"导师姓名"},{default:t(()=>[s(r(n.value.supervisorName),1)]),_:1}),e(c,{label:"所属院系"},{default:t(()=>[s(r(n.value.department),1)]),_:1}),e(c,{label:"职称"},{default:t(()=>[s(r(n.value.title),1)]),_:1}),e(c,{label:"邮箱"},{default:t(()=>[s(r(n.value.email),1)]),_:1}),e(c,{label:"电话"},{default:t(()=>[s(r(n.value.phoneNumber),1)]),_:1})]),_:1})])]),_:1}),e(h,{class:"box-card",style:{"margin-top":"20px"}},{header:t(()=>[H]),default:t(()=>[G((_(),g("div",null,[p.value.length>0?(_(),y(S,{key:0,data:p.value,style:{width:"100%"},border:""},{default:t(()=>[e(l,{prop:"nickname",label:"选题学生",width:"120"}),e(l,{prop:"title",label:"题目名称","min-width":"200"}),e(l,{prop:"description",label:"题目描述","min-width":"250"}),e(l,{prop:"categoryName",label:"类别",width:"120"}),e(l,{prop:"tag",label:"标签",width:"150"}),e(l,{prop:"publishDate",label:"发布时间",width:"160"}),e(l,{label:"状态",width:"100"},{default:t(b=>[e(N,{type:b.row.status===1?"success":"info",size:"small"},{default:t(()=>[s(r(b.row.status===1?"可选":"已选"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])):(_(),y(C,{key:1,description:"暂无题目"}))])),[[E,u.value]])]),_:1})])}}}),W=V(P,[["__scopeId","data-v-79952ff1"]]);export{W as default};
