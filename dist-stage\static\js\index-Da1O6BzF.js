import{Z as N,B as ae,d as le,r as i,C as oe,a5 as ne,e as u,G as F,c as re,o as v,J as y,f as t,a4 as L,l as a,h as l,m as se,n as g,j as h,D as Q,i as ce}from"./index-BJsoK47l.js";function ue(n){return N({url:"/system/cate/list",method:"post",data:{keyword:n.categoryName||"",pageNum:n.pageNum,pageSize:n.pageSize}})}function ie(n){return N({url:"/system/cate/selectById/"+n,method:"get"})}function de(n){return N({url:"/system/cate/add",method:"post",data:n})}function me(n){return N({url:"/system/cate/update",method:"put",data:{categoryId:n.categoryId,categoryName:n.categoryName}})}function ge(n){return N({url:`/system/cate/delete/${n}`,method:"delete"})}const pe={class:"app-container"},fe={class:"dialog-footer"},ye=ae({name:"Category"}),ve=Object.assign(ye,{setup(n){const{proxy:c}=le(),U=i([]),p=i(!1),w=i(!0),k=i(!0),I=i([]),B=i(!0),P=i(!0),V=i(0),$=i(""),b=i(!1),j=oe({form:{},queryParams:{pageNum:1,pageSize:10,categoryName:null},rules:{categoryName:[{required:!0,message:"类别名称不能为空",trigger:"blur"}]}}),{queryParams:d,form:f,rules:K}=ne(j);function _(){w.value=!0,ue(d.value).then(r=>{U.value=r.data.topics,V.value=r.data.total,w.value=!1}).catch(()=>{w.value=!1})}function A(){p.value=!1,x()}function x(){f.value={categoryId:null,categoryName:null,createTime:null,editTime:null,isDelete:null},c.resetForm("categoryRef")}function D(){d.value.pageNum=1,_()}function G(){c.resetForm("queryRef"),D()}function J(r){I.value=r.map(e=>e.categoryId),B.value=r.length!=1,P.value=!r.length}function O(){x(),p.value=!0,$.value="添加选题类别"}function z(r){x();const e=r.categoryId||I.value;ie(e).then(s=>{f.value={categoryId:s.data.categoryId,categoryName:s.data.categoryName},p.value=!0,$.value="修改选题类别"}).catch(s=>{c.$modal.msgError(s.message||"获取类目信息失败")})}function Z(){b.value||c.$refs.categoryRef.validate(r=>{if(r)if(b.value=!0,f.value.categoryId!=null){const e={...f.value};me(e).then(s=>{c.$modal.msgSuccess("修改成功"),p.value=!1,_()}).catch(s=>{c.$modal.msgError(s.message||"修改失败")}).finally(()=>{b.value=!1})}else de(f.value).then(e=>{c.$modal.msgSuccess("新增成功"),p.value=!1,_()}).catch(e=>{c.$modal.msgError(e.message||"新增失败")}).finally(()=>{b.value=!1})})}function T(r){const e=r.categoryId||I.value;c.$modal.confirm('是否确认删除选题类别编号为"'+e+'"的数据项？').then(function(){return ge(e)}).then(()=>{_(),c.$modal.msgSuccess("删除成功")}).catch(s=>{c.$modal.msgError(s.message||"删除失败")})}return _(),(r,e)=>{const s=u("el-input"),R=u("el-form-item"),m=u("el-button"),q=u("el-form"),E=u("el-col"),H=u("right-toolbar"),M=u("el-row"),S=u("el-table-column"),W=u("el-table"),X=u("pagination"),Y=u("el-dialog"),C=F("hasPermi"),ee=F("loading");return v(),re("div",pe,[y(t(q,{model:a(d),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[t(R,{label:"类别名称",prop:"categoryName"},{default:l(()=>[t(s,{modelValue:a(d).categoryName,"onUpdate:modelValue":e[0]||(e[0]=o=>a(d).categoryName=o),placeholder:"请输入类别名称",clearable:"",onKeyup:se(D,["enter"])},null,8,["modelValue"])]),_:1}),t(R,null,{default:l(()=>[t(m,{type:"primary",icon:"Search",onClick:D},{default:l(()=>[g("搜索")]),_:1}),t(m,{icon:"Refresh",onClick:G},{default:l(()=>[g("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[L,a(k)]]),t(M,{gutter:10,class:"mb8"},{default:l(()=>[t(E,{span:1.5},{default:l(()=>[y((v(),h(m,{type:"primary",plain:"",icon:"Plus",onClick:O},{default:l(()=>[g("新增")]),_:1})),[[C,["system:category:add"]]])]),_:1}),t(E,{span:1.5},{default:l(()=>[y((v(),h(m,{type:"success",plain:"",icon:"Edit",disabled:a(B),onClick:z},{default:l(()=>[g("修改")]),_:1},8,["disabled"])),[[C,["system:category:edit"]]])]),_:1}),t(E,{span:1.5},{default:l(()=>[y((v(),h(m,{type:"danger",plain:"",icon:"Delete",disabled:a(P),onClick:T},{default:l(()=>[g("删除")]),_:1},8,["disabled"])),[[C,["system:category:remove"]]])]),_:1}),t(H,{showSearch:a(k),"onUpdate:showSearch":e[1]||(e[1]=o=>Q(k)?k.value=o:null),onQueryTable:_},null,8,["showSearch"])]),_:1}),y((v(),h(W,{data:a(U),onSelectionChange:J},{default:l(()=>[t(S,{type:"selection",width:"55",align:"center"}),t(S,{label:"类别ID",align:"center",prop:"categoryId"}),t(S,{label:"类别名称",align:"center",prop:"categoryName"}),t(S,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[y((v(),h(m,{link:"",type:"primary",icon:"Edit",onClick:te=>z(o.row)},{default:l(()=>[g("修改")]),_:2},1032,["onClick"])),[[C,["system:category:edit"]]]),y((v(),h(m,{link:"",type:"primary",icon:"Delete",onClick:te=>T(o.row)},{default:l(()=>[g("删除")]),_:2},1032,["onClick"])),[[C,["system:category:remove"]]])]),_:1})]),_:1},8,["data"])),[[ee,a(w)]]),y(t(X,{total:a(V),page:a(d).pageNum,"onUpdate:page":e[2]||(e[2]=o=>a(d).pageNum=o),limit:a(d).pageSize,"onUpdate:limit":e[3]||(e[3]=o=>a(d).pageSize=o),onPagination:_},null,8,["total","page","limit"]),[[L,a(V)>0]]),t(Y,{title:a($),modelValue:a(p),"onUpdate:modelValue":e[5]||(e[5]=o=>Q(p)?p.value=o:null),width:"500px","append-to-body":""},{footer:l(()=>[ce("div",fe,[t(m,{type:"primary",loading:a(b),onClick:Z},{default:l(()=>[g("确 定")]),_:1},8,["loading"]),t(m,{onClick:A},{default:l(()=>[g("取 消")]),_:1})])]),default:l(()=>[t(q,{ref:"categoryRef",model:a(f),rules:a(K),"label-width":"80px"},{default:l(()=>[t(R,{label:"类别名称",prop:"categoryName"},{default:l(()=>[t(s,{modelValue:a(f).categoryName,"onUpdate:modelValue":e[4]||(e[4]=o=>a(f).categoryName=o),placeholder:"请输入类别名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ve as default};
