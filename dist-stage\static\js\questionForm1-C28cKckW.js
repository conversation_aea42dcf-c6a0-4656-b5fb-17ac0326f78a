import{_ as J,u as M,a as H,r as w,C as K,w as T,F as $,e as p,c as f,i as d,t as U,f as o,h as n,n as V,k as z,o as u,H as k,I as q,j as C,l as x,U as A,V as G,K as c,v as P,x as W}from"./index-BJsoK47l.js";import{a as X,f as Y,h as Z,i as ee}from"./questionnaire-D6WvR6WF.js";const te=b=>(P("data-v-461fdfb1"),b=b(),W(),b),oe={class:"app-container"},le={class:"page-header"},ae={class:"page-title"},se={class:"header-actions"},ne={class:"form-content"},re={key:0,class:"options-section"},ie={class:"options-header"},ue=te(()=>d("span",{class:"options-title"},"选项列表",-1)),ce={class:"option-item"},de={class:"option-label"},pe={__name:"questionForm1",setup(b){const I=M(),B=H(),v=w(null),e=K({questions_id:void 0,type:"",label_name:"",content:"",options:[],analysis:"",cueword:""}),F={type:[{required:!0,message:"请选择题目类型",trigger:"change"}],label_name:[{required:!0,message:"请选择问卷类型",trigger:"change"}],content:[{required:!0,message:"请输入题目内容",trigger:"blur"}]},Q=w([{value:"单选题",label:"单选题"},{value:"多选题",label:"多选题"},{value:"判断题",label:"判断题"},{value:"填空题",label:"填空题"},{value:"简答题",label:"简答题"},{value:"排序题",label:"排序题"}]),g=w([]),j=async()=>{try{const a=await X();a.code===200?a.rows?g.value=a.rows.map(t=>({value:t.labelId,label:t.labelName})):a.data?g.value=a.data.map(t=>({value:t.labelId,label:t.labelName})):c.warning("问卷类型列表数据格式不正确"):c.error(a.msg||"获取问卷类型列表失败")}catch(a){console.error("获取问卷类型列表失败:",a),c.error("获取问卷类型列表失败")}},D=async a=>{try{const t=await Y(a);if(t.code===200&&t.data){const s=t.data;if(e.questions_id=s.questionsId,e.type=s.type,e.label_name=s.labelId,e.content=s.content,e.analysis=s.analysis,e.cueword=s.cueword||"",s.options)try{const r=JSON.parse(s.options);e.options=Object.entries(r).map(([_,i])=>({content:i}))}catch(r){console.error("解析选项数据失败:",r),e.options=[]}else s.type==="判断题"?e.options=[{content:"正确"},{content:"错误"}]:e.options=[]}else c.error(t.msg||"获取题目详情失败")}catch(t){console.error("获取题目详情失败:",t),c.error("获取题目详情失败")}};T(()=>e.type,a=>{e.questions_id&&e.options.length>0||(["单选题","多选题","排序题"].includes(a)?(e.options=[],h(),h()):a==="判断题"?e.options=[{content:"正确"},{content:"错误"}]:e.options=[])},{immediate:!0});const h=()=>{e.options.push({content:""})},L=a=>{e.options.splice(a,1)},R=async()=>{v.value&&await v.value.validate(async a=>{if(a)try{const t={};e.options.forEach((_,i)=>{const m=String.fromCharCode(65+i);t[m]=_.content});const s={questionsId:e.questions_id,content:e.content,image:null,cueword:e.cueword||"",type:e.type,labelId:e.label_name,points:1,options:e.options.length>0?JSON.stringify(t):null,answer:null,analysis:e.analysis||null,randomly:0,required:1,questionnaireId:"Q002"};let r;e.questions_id?r=await Z(s):r=await ee(s),r.code===200?(c.success("保存成功"),O()):c.error(r.msg||"保存失败")}catch(t){console.error("保存题目失败:",t),c.error("保存失败")}})},O=()=>{B.back()};return $(()=>{j(),I.query.id&&D(I.query.id)}),(a,t)=>{const s=p("el-button"),r=p("el-option"),_=p("el-select"),i=p("el-form-item"),m=p("el-input"),N=p("el-icon"),E=p("el-form");return u(),f("div",oe,[d("div",le,[d("h1",ae,U(e.questions_id?"编辑题目":"添加题目"),1),d("div",se,[o(s,{onClick:O},{default:n(()=>[V("返回")]),_:1})])]),d("div",ne,[o(E,{ref_key:"formRef",ref:v,model:e,rules:F,"label-width":"100px",class:"custom-form"},{default:n(()=>[o(i,{label:"题目类型",prop:"type"},{default:n(()=>[o(_,{modelValue:e.type,"onUpdate:modelValue":t[0]||(t[0]=l=>e.type=l),placeholder:"请选择题目类型",class:"form-select"},{default:n(()=>[(u(!0),f(k,null,q(Q.value,l=>(u(),C(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"问卷类型",prop:"label_name"},{default:n(()=>[o(_,{modelValue:e.label_name,"onUpdate:modelValue":t[1]||(t[1]=l=>e.label_name=l),placeholder:"请选择问卷类型",class:"form-select"},{default:n(()=>[(u(!0),f(k,null,q(g.value,l=>(u(),C(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"题目内容",prop:"content"},{default:n(()=>[o(m,{modelValue:e.content,"onUpdate:modelValue":t[2]||(t[2]=l=>e.content=l),type:"textarea",rows:3,placeholder:"请输入题目内容",class:"content-input"},null,8,["modelValue"])]),_:1}),o(i,{label:"提示文字",prop:"cueword"},{default:n(()=>[o(m,{modelValue:e.cueword,"onUpdate:modelValue":t[3]||(t[3]=l=>e.cueword=l),type:"textarea",rows:2,placeholder:"请输入提示文字",class:"cueword-input"},null,8,["modelValue"])]),_:1}),["单选题","多选题","判断题","排序题"].includes(e.type)?(u(),f("div",re,[d("div",ie,[ue,o(s,{type:"primary",link:"",onClick:h,class:"add-option-btn"},{default:n(()=>[o(N,null,{default:n(()=>[o(x(A))]),_:1}),V("添加选项 ")]),_:1})]),(u(!0),f(k,null,q(e.options,(l,y)=>(u(),C(i,{key:y,prop:"options."+y+".content",rules:{required:!0,message:"选项内容不能为空",trigger:"blur"},class:"option-form-item"},{default:n(()=>[d("div",ce,[d("div",de,U(String.fromCharCode(65+y)),1),o(m,{modelValue:l.content,"onUpdate:modelValue":S=>l.content=S,placeholder:"请输入选项内容",class:"option-input"},null,8,["modelValue","onUpdate:modelValue"]),o(s,{type:"danger",link:"",onClick:S=>L(y),class:"delete-option-btn"},{default:n(()=>[o(N,null,{default:n(()=>[o(x(G))]),_:1})]),_:2},1032,["onClick"])])]),_:2},1032,["prop"]))),128))])):z("",!0),o(i,{label:"答案解析",prop:"analysis"},{default:n(()=>[o(m,{modelValue:e.analysis,"onUpdate:modelValue":t[4]||(t[4]=l=>e.analysis=l),type:"textarea",rows:3,placeholder:"请输入答案解析",class:"analysis-input"},null,8,["modelValue"])]),_:1}),o(i,{class:"form-footer"},{default:n(()=>[o(s,{type:"primary",onClick:R,class:"submit-btn"},{default:n(()=>[V("保存题目")]),_:1})]),_:1})]),_:1},8,["model"])])])}}},fe=J(pe,[["__scopeId","data-v-461fdfb1"]]);export{fe as default};
