<template>
  <div class="app-container">
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ examInfo.className }} </h1>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>总人数</span>
              <el-icon><User /></el-icon>
            </div>
          </template>
          <div class="stat-value">{{ statistics.totalStudents }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>已答题</span>
              <el-icon><Check /></el-icon>
            </div>
          </template>
          <div class="stat-value">{{ statistics.submittedCount }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>及格人数</span>
              <el-icon><Star /></el-icon>
            </div>
          </template>
          <div class="stat-value">{{ statistics.passedCount }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>平均分</span>
              <el-icon><DataLine /></el-icon>
            </div>
          </template>
          <div class="stat-value">{{ statistics.averageScore }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选区域 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="姓名">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入姓名"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="学号">
          <el-input
            v-model="queryParams.studentNo"
            placeholder="请输入学号"
            clearable
            prefix-icon="Search"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="答题状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="filter-select">
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="box-card" shadow="hover">
      <el-table
        v-loading="loading"
        :data="studentList"
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="studentName" label="姓名" min-width="100" align="center" />
        <el-table-column prop="studentNo" label="学号" min-width="120" align="center" />
        <el-table-column prop="status" label="答题状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="得分" width="100" align="center" sortable>
          <template #default="scope">
            <span :class="{ 'text-success': scope.row.score >= examInfo.passingScore, 'text-danger': scope.row.score < examInfo.passingScore }">
              {{ scope.row.score || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间" min-width="160" align="center" />
        <el-table-column prop="duration" label="答题用时" width="120" align="center" sortable>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleViewDetail(scope.row)"
            >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {  Download, User, Check, Star, DataLine } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { getExams } from '@/api/aqsystem/exams'
import { selectAssignments } from '@/api/aqsystem/zuoye'
const route = useRoute()
const router = useRouter()

// 考试信息
const examInfo = ref({
  examId: '',
  examName: '',
  className: '',
  passingScore: 60,
  totalScore: 0
})

// 统计数据
const statistics = ref({
  totalStudents: 0,
  submittedCount: 0,
  passedCount: 0,
  averageScore: 0
})

// 遮罩层
const loading = ref(false)
// 总条数
const total = ref(0)
// 学生列表数据
const studentList = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  studentName: undefined,
  studentId: undefined,
  status: undefined,
  orderByColumn: undefined,
  isAsc: undefined
})

// 状态选项
const statusOptions = [
  { value: "未开始", label: "未开始" },
  { value: "进行中", label: "进行中" },
  { value: "已完成", label: "已完成" }
]

/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case '未开始':
      return 'info'     // 未开始 - 灰色
    case '进行中':
      return 'warning'  // 进行中 - 黄色
    case '已完成':
      return 'success'  // 已完成 - 绿色
    default:
      return 'info'
  }
}

/** 获取状态文本 */
function getStatusText(status) {
  return status
}

/** 查询学生列表和考试详情 */
async function fetchExamAndStudents(id) {
  loading.value = true
  try {
    if (route.query.examId) {
      const res = await getExams(id)
      const data = res.data
      // 考试信息
      examInfo.value = {
        examId: data.examId,
        examName: data.examName,
        className: data.className,
        passingScore: 60,
        totalScore: data.totalScore
      }
      // 统计信息
      statistics.value = {
        totalStudents: data.totalCount,
        submittedCount: data.submittedCount,
        passedCount: data.passCount,
        averageScore: data.averageScore
      }
      // 学生列表
      studentList.value = data.students
    } else if (route.query.assignmentId) {
      const res = await selectAssignments(id)
      const data = res.data
      // 作业信息
      examInfo.value = {
        examId: data.assignmentId,
        examName: data.name,
        className: data.className,
        passingScore: 60,
        totalScore: data.totalScore
      }
      // 统计信息
      statistics.value = {
        totalStudents: data.totalCount,
        submittedCount: data.submittedCount,
        passedCount: data.passCount,
        averageScore: data.averageScore
      }
      // 学生列表
      studentList.value = data.students.map(student => ({
        ...student,
        studentNo: student.studentNo || student.studentId // 优先使用studentNo，如果没有则使用studentId
      }))
    }
    total.value = studentList.value.length
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  const id = route.query.examId || route.query.assignmentId
  if (id) {
    fetchExamAndStudents(id)
  } else {
    ElMessage.warning('未获取到考试/作业信息')
  }
})

/** 搜索按钮操作 */
function handleQuery() {
  // 前端过滤
  if (!studentList.value.length) return
  let filtered = [...studentList.value]
  if (queryParams.studentName) {
    filtered = filtered.filter(s => s.studentName.includes(queryParams.studentName))
  }
  if (queryParams.studentNo) {
    filtered = filtered.filter(s => s.studentNo.includes(queryParams.studentNo))
  }
  if (queryParams.status) {
    filtered = filtered.filter(s => s.status === queryParams.status)
  }
  studentList.value = filtered
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.studentName = undefined
  queryParams.studentId = undefined
  queryParams.status = undefined
  fetchExamAndStudents(examInfo.value.examId)
}

/** 导出数据 */
function handleExport() {
  const exportData = studentList.value.map(item => ({
    '序号': item.index,
    '姓名': item.studentName,
    '手机号': item.studentNo,
    '答题状态': item.status,
    '得分': item.score || '-',
    '提交时间': item.submitTime || '-',
    '答题用时': item.duration || '-'
  }))
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.json_to_sheet(exportData)
  ws['!cols'] = [
    { wch: 8 }, { wch: 10 }, { wch: 15 }, { wch: 10 }, { wch: 8 }, { wch: 20 }, { wch: 15 }
  ]
  XLSX.utils.book_append_sheet(wb, ws, '学生答题情况')
  const fileName = `${examInfo.value.className}-学生答题情况.xlsx`
  XLSX.writeFile(wb, fileName)
  ElMessage.success('导出成功')
}

/** 查看详情 */
function handleViewDetail(row) {
  router.push({
    path: '/aqsystem/exams/student-detail',
    query: {
      examId: examInfo.value.examId,
      examName: examInfo.value.examName,
      className: examInfo.value.className,
      studentId: row.studentNo,
      studentName: row.studentName
    }
  })
}


</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  color: #606266;
  margin: 4px 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stat-cards {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.filter-container {
  margin-bottom: 20px;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-select {
  width: 160px;
}

.search-buttons {
  margin-left: auto;
}

.box-card {
  border-radius: 8px;
}

.text-success {
  color: #67c23a;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c;
  font-weight: 500;
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-buttons {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .filter-select {
    width: 100%;
  }
}
</style> 