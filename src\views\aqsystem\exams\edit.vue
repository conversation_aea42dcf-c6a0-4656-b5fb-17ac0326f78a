<template>
  <div class="app-container">
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑试卷' : '创建新试卷' }}</h1>
      </div>
    </div>

    <el-card class="form-card" shadow="hover">
      <!-- 基本信息 -->
      <div class="section-title">基本信息</div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="form-content"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="试卷名称" prop="examName">
              <el-input v-model="form.examName" placeholder="输入试卷名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型" class="full-width" @change="handleTypeChange">
                <el-option label="作业" value="homework" />
                <el-option label="试卷" value="exam" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属班级" prop="classId">
              <el-select v-model="form.classId" placeholder="请选择班级" class="full-width">
                <el-option
                  v-for="item in classOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="试卷描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="输入试卷描述..."
          />
        </el-form-item>

        <el-row v-if="form.type === 'exam'" :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                class="full-width"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                class="full-width"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.type === 'exam'" :gutter="20">
          <el-col :span="8">
            <el-form-item label="试卷时长" prop="duration">
              <el-input-number
                v-model="form.duration"
                :min="30"
                :max="180"
                :step="30"
                class="full-width"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="及格分数" prop="passingScore">
              <el-input-number
                v-model="form.passingScore"
                :min="0"
                :max="100"
                :step="5"
                class="full-width"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="form-card" shadow="hover">
      <!-- 试题选择 -->
      <div class="section-header">
        <div class="section-title">试题选择</div>
        <div class="question-stats">
          已选择 <span class="highlight">{{ selectedQuestions.length }}</span> 题 / 
          总分 <span class="highlight">{{ totalScore }}</span> 分
        </div>
      </div>

      <el-row :gutter="20">
        <!-- 题库区域 -->
        <el-col :span="16">
          <div class="question-bank">
            <div class="question-bank-header">
              <h3>题库</h3>
              <div class="question-bank-actions">
                <el-select 
                  v-model="selectedSubject" 
                  placeholder="题目类型" 
                  size="small" 
                  class="subject-select custom-select"
                >
                  <el-option
                    v-for="subject in subjectOptions"
                    :key="subject.value"
                    :label="subject.label"
                    :value="subject.value"
                  />
                </el-select>
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索题目..."
                  prefix-icon="Search"
                  clearable
                  class="search-input"
                />
              </div>
            </div>

            <div class="question-list">
              <el-empty v-if="!loading && questionBank.length === 0" description="暂无题目" />
              <el-skeleton :loading="loading" animated :count="3" v-else-if="loading">
                <template #template>
                  <div class="question-item">
                    <el-skeleton-item variant="text" style="width: 30%" />
                    <el-skeleton-item variant="text" style="width: 100%" />
                  </div>
                </template>
              </el-skeleton>
              <div
                v-else
                v-for="question in filteredQuestions"
                :key="question.questionId"
                class="question-item"
                :class="{ selected: isQuestionSelected(question.questionId) }"
                @click="toggleQuestion(question)"
              >
                <div class="question-item-header">
                  <div class="question-type">
                    <el-tag
                      :type="getQuestionTypeTag(question.type)"
                      size="small"
                      class="clickable-tag"
                      @click="filterByType(question.type)"
                    >{{ getQuestionTypeText(question.type) }}</el-tag>
                    
                    <el-tag size="small" type="info" class="topic-tag">{{ question.topicClassification }}</el-tag>
                    <span class="question-id">ID: #{{ question.questionId }}</span>
                  </div>
                  <div class="question-meta">
                 
                  </div>
                </div>
                <div class="question-content">{{ question.content }}</div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 已选题目区域 -->
        <el-col :span="8">
          <div class="selected-questions">
            <div class="selected-questions-header">
              <h3>已选试题</h3>
            </div>

            <div v-if="selectedQuestions.length === 0" class="empty-state">
              <el-icon :size="48"><Document /></el-icon>
              <p>还没有选择任何试题</p>
              <p class="hint-text">从左侧题库中点击题目添加</p>
            </div>

            <draggable
              v-else
              v-model="selectedQuestions"
              item-key="questionId"
              class="selected-questions-list"
            >
              <template #item="{ element }">
                <div class="selected-question-item">
                  <div class="selected-question-content">
                    <el-tag size="small" class="question-type-tag">
                      {{ getQuestionTypeText(element.type) }}
                    </el-tag>
                    <el-select 
                      v-model="element.score" 
                      size="small" 
                      class="score-select"
                      @change="handleScoreChange(element)"
                    >
                      <el-option label="3分" :value="3" />
                      <el-option label="5分" :value="5" />
                      <el-option label="10分" :value="10" />
                    </el-select>
                    <el-button
                      type="danger"
                      link
                      icon="Delete"
                      class="delete-btn"
                      @click="removeQuestion(element)"
                    />
                  </div>
                  <div class="selected-question-title">{{ element.content }}</div>
                </div>
              </template>
            </draggable>

            <div class="selected-questions-actions">
              <el-button type="danger" plain @click="clearSelectedQuestions">
                清空
              </el-button>
              <el-button type="primary" plain @click="randomizeQuestions">
                随机排序
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 底部按钮 -->
    <div class="form-actions">
      <el-button type="primary" @click="handlePreview">预览</el-button>
      <el-button type="success" @click="handlePublish">保存并发布</el-button>
    </div>

    <el-dialog v-model="previewVisible" title="试卷预览" width="600px" :close-on-click-modal="false">
      <div v-if="selectedQuestions.length === 0">暂无试题</div>
      <div v-else>
        <div v-for="(q, idx) in selectedQuestions" :key="q.id" class="preview-question">
          <div class="preview-question-title">
            <span class="preview-q-index">{{ idx + 1 }}.</span> {{ q.content }}
          </div>
          <div v-if="q.type === 'single'">
            <el-radio-group>
              <el-radio v-for="(opt, i) in q.options" :key="i" :label="opt">{{ opt }}</el-radio>
            </el-radio-group>
          </div>
          <div v-else-if="q.type === 'multiple'">
            <el-checkbox-group>
              <el-checkbox v-for="(opt, i) in q.options" :key="i" :label="opt">{{ opt }}</el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-else>
            <el-input type="textarea" :rows="2" placeholder="请输入答案..." />
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="previewVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref,computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { listQuestions,getCategoryList } from '@/api/aqsystem/questions'
import { addExams,getClassList, selectExams,updateExams } from '@/api/aqsystem/exams'
import { ass,updateAssignments} from '@/api/aqsystem/zuoye'
const route = useRoute()
const router = useRouter()

// 是否为编辑模式
const isEdit = ref(false)

// 表单数据
const form = ref({
  examName: '',
  type: 'exam', // 默认为试卷类型
  description: '',
  startTime: '',
  endTime: '',
  duration: 120,
  passingScore: 60,
  status: '1',
  classId: undefined // 添加 classId 字段
})

// 表单校验规则
const rules = {
  examName: [
    { required: true, message: '请输入试卷名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择所属类型', trigger: 'change' }
  ],
  classId: [
    { required: true, message: '请选择所属班级', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入试卷描述', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请输入试卷时长', trigger: 'blur' }
  ],
  passingScore: [
    { required: true, message: '请输入及格分数', trigger: 'blur' }
  ]
}

// 学科选项
const subjectOptions = ref([])

// 获取学科分类列表
const getSubjectList = async () => {
  try {
    const response = await getCategoryList()
    subjectOptions.value = response.map(item => ({
      value: item.id,
      label: item.name
    }))
  } catch (error) {
    console.error('获取学科分类失败:', error)
    ElMessage.error('获取学科分类失败')
  }
}

// 选中的学科
const selectedSubject = ref('')

// 题目类型选项
const questionTypes = {
  single: { text: '单选题', type: 'success' },
  multiple: { text: '多选题', type: 'primary' },
  fill: { text: '填空题', type: 'warning' },
  variable: { text: '不定项', type: 'danger' }
}



// 搜索查询
const searchQuery = ref('')

// 已选题目
const selectedQuestions = ref([])

// 题库数据
const questionBank = ref([])
const loading = ref(false)

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  topicClassification: '',
  questionType: '',
  difficulty: '',
  content: ''
})

// 在 queryParams 定义后添加
const selectedType = ref('')

// 获取题库列表
const getQuestionList = async () => {
  try {
    loading.value = true
    console.log('查询参数:', queryParams.value)
    const response = await listQuestions(queryParams.value)
    questionBank.value = response.rows || []
  } catch (error) {
    console.error('获取题库列表失败:', error)
    ElMessage.error('获取题库列表失败')
  } finally {
    loading.value = false
  }
}

// 监听搜索条件变化
watch([selectedSubject, searchQuery, selectedType], () => {
  queryParams.value.topicClassification = selectedSubject.value
  queryParams.value.content = searchQuery.value
  queryParams.value.questionType = selectedType.value
  getQuestionList()
}, { immediate: true })

// 过滤后的题目列表
const filteredQuestions = computed(() => {
  return questionBank.value
})

// 总分
const totalScore = computed(() => {
  return selectedQuestions.value.reduce((sum, q) => sum + q.score, 0)
})

// 获取题目类型标签样式
function getQuestionTypeTag(type) {
  return questionTypes[type]?.type || 'info'
}

// 获取题目类型文本
function getQuestionTypeText(type) {
  return questionTypes[type]?.text || type
}



// 判断题目是否已选
function isQuestionSelected(questionId) {
  return selectedQuestions.value.some(q => q.questionId === questionId)
}

// 切换题目选择状态
function toggleQuestion(question) {
  const index = selectedQuestions.value.findIndex(q => q.questionId === question.questionId)
  if (index === -1) {
    // 如果题目不在已选列表中，则添加
    selectedQuestions.value.push({
      ...question,
      score: question.score || 5 // 如果没有分数，默认5分
    })
  } else {
    // 如果题目已在列表中，则移除
    selectedQuestions.value.splice(index, 1)
  }
}

// 移除已选题目
function removeQuestion(question) {
  const index = selectedQuestions.value.findIndex(q => q.questionId === question.questionId)
  if (index !== -1) {
    selectedQuestions.value.splice(index, 1)
  }
}

// 清空已选题目
function clearSelectedQuestions() {
  selectedQuestions.value = []
}

// 随机排序已选题目
function randomizeQuestions() {
  const questions = [...selectedQuestions.value]
  for (let i = questions.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[questions[i], questions[j]] = [questions[j], questions[i]]
  }
  selectedQuestions.value = questions
}



// 预览弹窗显示状态
const previewVisible = ref(false)

// 修改预览按钮事件
function handlePreview() {
  previewVisible.value = true
}

// 修改保存并发布方法
async function handlePublish() {
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 检查是否选择了题目
    if (selectedQuestions.value.length === 0) {
      ElMessage.warning('请至少选择一道题目')
      return
    }

    // 构建请求数据
    const requestData = {
      examId: route.query.id, // 添加examId用于更新
      assignmentId: route.query.assignmentId, // 添加assignmentId用于作业更新
      distinguish: form.value.type === 'exam' ? '试卷' : '作业',
      title: form.value.examName,
      description: form.value.description,
      startTime: form.value.startTime,
      endTime: form.value.endTime,
      duration: form.value.duration,
      passingMark: form.value.passingScore,
      totalScore: totalScore.value,
      classId: form.value.classId,
      labelId: form.value.type === 'exam' ? 1 : 2, // 1是试卷，2是作业
      questions: selectedQuestions.value.map((question, index) => ({
        questionId: question.questionId,
        score: question.score,
        orderNum: index + 1
      }))
    }

    // 根据是否为编辑模式调用不同的接口
    if (isEdit.value) {
      if (form.value.type === 'homework') {
        // 如果是作业，调用作业更新接口
        await updateAssignments(requestData)
        ElMessage.success('作业更新成功')
      } else {
        // 如果是试卷，调用试卷更新接口
        await updateExams(requestData)
        ElMessage.success('试卷更新成功')
      }
    } else {
      // 新增时统一使用addExams接口
      await addExams(requestData)
      ElMessage.success(form.value.type === 'exam' ? '试卷创建成功' : '作业创建成功')
    }
    
    // 跳转到列表页
    router.push('/aqsystem/exams/exams')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败：' + (error.message || '未知错误'))
  }
}

// 添加表单引用
const formRef = ref(null)

// 获取试卷详情
const getExamDetail = async () => {
  const examId = route.query.id
  if (!examId) return
  
  isEdit.value = true
  try {
    const response = await selectExams(examId)
    if (response.data && response.data.length > 0) {
      const examData = response.data[0]
      // 将接口返回的数据映射到表单
      form.value = {
        examName: examData.title,
        type: examData.labelId === 1 ? 'exam' : 'homework',
        classId: examData.classId,
        description: examData.description,
        startTime: examData.startTime,
        endTime: examData.endTime,
        duration: 60, // 默认值，如果后端有返回则使用后端值
        passingScore: 60 // 默认值，如果后端有返回则使用后端值
      }

      // 设置已选题目
      if (examData.questionsList && examData.questionsList.length > 0) {
        selectedQuestions.value = examData.questionsList.map(question => ({
          questionId: question.questionId,
          content: question.content,
          type: question.type,
          topicClassification: question.topicClassification,
          score: question.points || 5, // 使用题目分值，如果没有则默认5分
          options: question.options ? JSON.parse(question.options) : null,
          answer: question.answer,
          analysis: question.analysis
        }))
      }
    }
  } catch (error) {
    console.error('获取试卷详情失败:', error)
    ElMessage.error('获取试卷详情失败')
  }
}

// 获取作业详情
const getAssignmentDetail = async () => {
  const assignmentId = route.query.assignmentId
  if (!assignmentId) return
  isEdit.value = true
  try {
    const response = await ass(assignmentId)
    const data = response.data
    form.value = {
      examName: data.title,
      type: 'homework',
      classId: data.classId,
      description: data.description,
      startTime: '',
      endTime: '',
      duration: 0,
      passingScore: 0
    }
    if (data.questionsList && data.questionsList.length > 0) {
      selectedQuestions.value = data.questionsList.map(question => ({
        questionId: question.questionId,
        content: question.content,
        type: question.type,
        topicClassification: question.topicClassification,
        score: question.points || 5,
        options: question.options ? JSON.parse(question.options) : null,
        answer: question.answer,
        analysis: question.analysis
      }))
    }
  } catch (error) {
    ElMessage.error('获取作业详情失败')
  }
}

// 处理类型变化
function handleTypeChange(value) {
  if (value === 'homework') {
    // 清除试卷相关字段
    form.value.startTime = ''
    form.value.endTime = ''
    form.value.duration = 120
    form.value.passingScore = 60
    form.value.status = '1'
  }
}

// 添加筛选方法
function filterByType(type) {
  selectedType.value = selectedType.value === type ? '' : type
  queryParams.value.questionType = selectedType.value
  getQuestionList()
}

// 处理分数变化
function handleScoreChange(question) {
  console.log('分数已更新:', question.score)
}

// 班级选项
const classOptions = ref([])

// 获取班级列表
const getClassOptions = async () => {
  try {
    const response = await getClassList()
    console.log('班级列表响应:', response)
    classOptions.value = response.map(item => ({
      id: item.classId,
      name: item.className
    }))
    console.log('处理后的班级选项:', classOptions.value)
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  }
}

onMounted(() => {
  if (route.query.id) {
    getExamDetail()
  } else if (route.query.assignmentId) {
    getAssignmentDetail()
  }
  getSubjectList()
  getClassOptions()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-subtitle {
  color: #606266;
  margin: 4px 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.form-content {
  max-width: 100%;
}

.full-width {
  width: 100%;
}

.unit-text {
  margin-left: 8px;
  color: #606266;
}

.question-stats {
  font-size: 14px;
  color: #606266;
}

.highlight {
  color: #409EFF;
  font-weight: 500;
}

.question-bank {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.question-bank-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-bank-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.question-bank-actions {
  display: flex;
  gap: 8px;
}

.subject-select {
  width: 120px;
}

.subject-select :deep(.el-select--small .el-select__wrapper) {
  font-size: 12px;
  gap: 4px;
  line-height: 28px;
  min-height: 32px;
  padding: 2px 8px;
}

.custom-select :deep(.el-select__wrapper) {
  height: 32px !important;
  min-height: 32px !important;
  line-height: 32px !important;
}

.custom-select :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

.search-input {
  width: 200px;
}

.question-list {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  max-height: 600px;
  overflow-y: auto;
}

.question-item {
  padding: 12px;
  border-bottom: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s;
}

.question-item:last-child {
  border-bottom: none;
}

.question-item:hover {
  background-color: #f5f7fa;
}

.question-item.selected {
  background-color: #ecf5ff;
  border-color: #409EFF;
}

.question-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.question-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-id {
  font-size: 12px;
  color: #909399;
}

.question-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.question-score {
  font-size: 12px;
  color: #606266;
}

.question-content {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
}

.selected-questions {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.selected-questions-header {
  margin-bottom: 16px;
}

.selected-questions-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.hint-text {
  font-size: 12px;
  color: #909399;
  margin: 4px 0 0;
}

.empty-state {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 32px;
  text-align: center;
  color: #909399;
}

.empty-state p {
  margin: 8px 0 0;
}

.selected-questions-list {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.selected-question-item {
  padding: 12px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-question-item:last-child {
  border-bottom: none;
}

.selected-question-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.selected-question-title {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.delete-btn {
  margin-left: auto;
  flex-shrink: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.delete-btn :deep(.el-icon) {
  font-size: 16px;
  margin-top: 35px;
}

.question-type-tag {
  flex-shrink: 0;
}

.question-score {
  flex-shrink: 0;
}

.selected-questions-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .form-content {
    max-width: 100%;
  }
}

/* 预览弹窗样式 */
.preview-question {
  margin-bottom: 24px;
}
.preview-question-title {
  font-weight: 500;
  margin-bottom: 8px;
}
.preview-q-index {
  color: #409EFF;
  font-weight: bold;
  margin-right: 4px;
}

.topic-tag {
  margin-left: 8px;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-tag:hover {
  opacity: 0.8;
}

.score-select {
  width: 80px;
  margin: 0 8px;
}

.score-select :deep(.el-input__wrapper) {
  padding: 0 8px;
}

.score-select :deep(.el-input__inner) {
  height: 24px;
  line-height: 24px;
}

</style> 