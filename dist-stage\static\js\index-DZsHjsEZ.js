import{_ as se,a as ie,r as f,C as ue,F as re,e as i,G as de,c as b,f as e,h as l,o as _,H as k,m as C,n as c,J as me,j as K,k as ce,t as S,l as q,W as pe,T as fe,V as _e,i as y,N as ge,v as ve,x as he,E as ye,K as I}from"./index-BJsoK47l.js";import{l as xe,a as be,d as Ne,b as we}from"./zuoye-v_1cayzW.js";const Ve=N=>(ve("data-v-47e840bb"),N=N(),he(),N),ke={class:"app-container"},Ce=Ve(()=>y("div",{class:"page-header"},[y("h1",{class:"page-title"},"试卷管理")],-1)),Se={class:"card-header"},qe={class:"card-title"},Ie={class:"header-buttons"},Te={class:"dialog-footer"},Ue={__name:"index",setup(N){const x=ie(),g=f(!1),w=f(0),E=f([]),B=f([]),M=f(""),V=f(JSON.parse(JSON.stringify(!1))),m=f(JSON.parse(JSON.stringify({}))),O=f(JSON.parse(JSON.stringify({examName:[{required:!0,message:"试卷名称不能为空",trigger:"blur"}],className:[{required:!0,message:"请输入班级名称",trigger:"blur"}],examTime:[{required:!0,message:"请选择试卷时间",trigger:"change"}]}))),o=ue({pageNum:1,pageSize:10,title:void 0,name:void 0,status:void 0,className:void 0}),s=f(!0);function v(){if(g.value=!0,s.value)xe(o).then(n=>{console.log("查询参数:",o),console.log("响应数据:",n),E.value=n.rows,w.value=n.total,g.value=!1}).catch(n=>{console.error("获取列表失败:",n),g.value=!1});else{const n={pageNum:o.pageNum,pageSize:o.pageSize,title:o.name,className:o.className};be(n).then(t=>{console.log("查询作业参数:",n),console.log("作业响应数据:",t),B.value=t.rows,w.value=t.total,g.value=!1}).catch(t=>{console.error("获取作业列表失败:",t),g.value=!1})}}function h(){o.pageNum=1,v()}function z(){s.value?(o.title=void 0,o.status=void 0):o.name=void 0,o.className=void 0,h()}function P(n){switch(n){case"1":return"进行中";case"0":return"未开始";case"2":return"已完成";default:return n}}function F(n,t){const r=n/t*100;return r<30?"#f56c6c":r<70?"#e6a23c":"#67c23a"}function Y(){x.push({path:"/aqsystem/exams/edit",query:{type:s.value?"exam":"homework"}})}function H(n){s.value?x.push({path:"/aqsystem/exams/edit",query:{id:n.examId}}):x.push({path:"/aqsystem/exams/edit",query:{assignmentId:n.assignmentId}})}function L(n){s.value?x.push({path:"/aqsystem/exams/student-list",query:{examId:n.examId,examName:n.examName,className:n.className}}):x.push({path:"/aqsystem/exams/student-list",query:{assignmentId:n.assignmentId}})}function R(n){const t=s.value?"试卷":"作业",r=s.value?n.title:n.name;ye.confirm(`是否确认删除${t}名称为"${r}"的数据项?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{s.value?await Ne(n.examId):await we(n.assignmentId),I.success("删除成功"),v()}catch(u){console.error("删除失败:",u),I.error("删除失败："+(u.message||"未知错误"))}}).catch(()=>{I.info("已取消删除")})}function A(){m.value={examId:void 0,examName:void 0,className:void 0,examTime:void 0,description:void 0}}function Q(){V.value=!1,A()}function j(){I.success("保存成功"),V.value=!1,v()}function G(n){switch(n){case"未开始":return"info";case"进行中":return"warning";case"已完成":return"success";default:return"info"}}function W(){s.value=!s.value,v()}return re(()=>{v()}),(n,t)=>{const r=i("el-input"),u=i("el-form-item"),T=i("el-option"),X=i("el-select"),p=i("el-button"),J=i("el-form"),$=i("el-card"),d=i("el-table-column"),Z=i("el-tag"),ee=i("el-progress"),U=i("el-icon"),ae=i("el-table"),le=i("pagination"),te=i("el-date-picker"),ne=i("el-dialog"),oe=de("loading");return _(),b("div",ke,[Ce,e($,{class:"filter-container",shadow:"hover"},{default:l(()=>[e(J,{inline:!0,model:o,class:"search-form"},{default:l(()=>[s.value?(_(),b(k,{key:0},[e(u,{label:"试卷名称"},{default:l(()=>[e(r,{modelValue:o.title,"onUpdate:modelValue":t[0]||(t[0]=a=>o.title=a),placeholder:"请输入试卷名称",clearable:"","prefix-icon":"Search",onKeyup:C(h,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"试卷状态"},{default:l(()=>[e(X,{modelValue:o.status,"onUpdate:modelValue":t[1]||(t[1]=a=>o.status=a),placeholder:"请选择状态",clearable:"",class:"filter-select"},{default:l(()=>[e(T,{label:"未开始",value:"未开始"}),e(T,{label:"进行中",value:"进行中"}),e(T,{label:"已完成",value:"已完成"})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"班级"},{default:l(()=>[e(r,{modelValue:o.className,"onUpdate:modelValue":t[2]||(t[2]=a=>o.className=a),placeholder:"请输入班级名称",clearable:"","prefix-icon":"Search",onKeyup:C(h,["enter"])},null,8,["modelValue"])]),_:1})],64)):(_(),b(k,{key:1},[e(u,{label:"作业名称"},{default:l(()=>[e(r,{modelValue:o.name,"onUpdate:modelValue":t[3]||(t[3]=a=>o.name=a),placeholder:"请输入作业名称",clearable:"","prefix-icon":"Search",onKeyup:C(h,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"班级"},{default:l(()=>[e(r,{modelValue:o.className,"onUpdate:modelValue":t[4]||(t[4]=a=>o.className=a),placeholder:"请输入班级名称",clearable:"","prefix-icon":"Search",onKeyup:C(h,["enter"])},null,8,["modelValue"])]),_:1})],64)),e(u,{class:"search-buttons"},{default:l(()=>[e(p,{type:"primary",icon:"Search",onClick:h},{default:l(()=>[c("搜索")]),_:1}),e(p,{icon:"Refresh",onClick:z},{default:l(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e($,{class:"box-card",shadow:"hover"},{header:l(()=>[y("div",Se,[y("span",qe,S(s.value?"试卷列表":"作业列表"),1),y("div",Ie,[e(p,{type:s.value?"primary":"default",icon:q(ge),onClick:W},{default:l(()=>[c(S(s.value?"作业":"试卷"),1)]),_:1},8,["type","icon"]),e(p,{type:"primary",icon:"Plus",onClick:Y},{default:l(()=>[c("新增"+S(s.value?"试卷":"作业"),1)]),_:1})])])]),default:l(()=>[me((_(),K(ae,{data:s.value?E.value:B.value,border:"",style:{width:"100%"}},{default:l(()=>[e(d,{type:"index",label:"序号",width:"80",align:"center"}),s.value?(_(),b(k,{key:0},[e(d,{prop:"title",label:"试卷名称","min-width":"120","show-overflow-tooltip":""}),e(d,{prop:"className",label:"班级","min-width":"120",align:"center"}),e(d,{prop:"startTime",label:"开始时间","min-width":"160",align:"center"}),e(d,{prop:"endTime",label:"结束时间","min-width":"160",align:"center"}),e(d,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(a=>[e(Z,{type:G(a.row.status)},{default:l(()=>[c(S(P(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"participantCount",label:"参与人数",width:"120",align:"center"},{default:l(a=>[e(ee,{percentage:Math.round(a.row.participantCount/a.row.totalCount*100),format:()=>`${a.row.participantCount}/${a.row.totalCount}`,"stroke-width":8,color:F(a.row.participantCount,a.row.totalCount)},null,8,["percentage","format","color"])]),_:1})],64)):(_(),b(k,{key:1},[e(d,{prop:"name",label:"作业名称","min-width":"120","show-overflow-tooltip":""}),e(d,{prop:"className",label:"班级","min-width":"120",align:"center"}),e(d,{prop:"description",label:"作业描述","min-width":"150","show-overflow-tooltip":"",align:"center"}),e(d,{prop:"totalScore",label:"总分",width:"120",align:"center"})],64)),e(d,{label:"操作",width:"220",fixed:"right"},{default:l(({row:a})=>[e(p,{type:"primary",link:"",onClick:D=>H(a)},{default:l(()=>[e(U,null,{default:l(()=>[e(q(pe))]),_:1}),c(" 编辑 ")]),_:2},1032,["onClick"]),e(p,{type:"primary",link:"",onClick:D=>L(a)},{default:l(()=>[e(U,null,{default:l(()=>[e(q(fe))]),_:1}),c(" 查看 ")]),_:2},1032,["onClick"]),e(p,{type:"danger",link:"",onClick:D=>R(a)},{default:l(()=>[e(U,null,{default:l(()=>[e(q(_e))]),_:1}),c(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[oe,g.value]]),w.value>0?(_(),K(le,{key:0,total:w.value,page:o.pageNum,"onUpdate:page":t[5]||(t[5]=a=>o.pageNum=a),limit:o.pageSize,"onUpdate:limit":t[6]||(t[6]=a=>o.pageSize=a),onPagination:v},null,8,["total","page","limit"])):ce("",!0)]),_:1}),e(ne,{title:M.value,modelValue:V.value,"onUpdate:modelValue":t[11]||(t[11]=a=>V.value=a),width:"500px","append-to-body":""},{footer:l(()=>[y("div",Te,[e(p,{type:"primary",onClick:j},{default:l(()=>[c("确 定")]),_:1}),e(p,{onClick:Q},{default:l(()=>[c("取 消")]),_:1})])]),default:l(()=>[e(J,{ref:"examFormRef",model:m.value,rules:O.value,"label-width":"80px"},{default:l(()=>[e(u,{label:"试卷名称",prop:"examName"},{default:l(()=>[e(r,{modelValue:m.value.examName,"onUpdate:modelValue":t[7]||(t[7]=a=>m.value.examName=a),placeholder:"请输入试卷名称"},null,8,["modelValue"])]),_:1}),e(u,{label:"班级",prop:"className"},{default:l(()=>[e(r,{modelValue:m.value.className,"onUpdate:modelValue":t[8]||(t[8]=a=>m.value.className=a),placeholder:"请输入班级名称"},null,8,["modelValue"])]),_:1}),e(u,{label:"试卷时间",prop:"examTime"},{default:l(()=>[e(te,{modelValue:m.value.examTime,"onUpdate:modelValue":t[9]||(t[9]=a=>m.value.examTime=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),e(u,{label:"试卷说明",prop:"description"},{default:l(()=>[e(r,{modelValue:m.value.description,"onUpdate:modelValue":t[10]||(t[10]=a=>m.value.description=a),type:"textarea",placeholder:"请输入试卷说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}},Je=se(Ue,[["__scopeId","data-v-47e840bb"]]);export{Je as default};
