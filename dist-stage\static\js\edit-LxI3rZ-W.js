import{_ as Te,u as Ce,a as Se,r as _,w as Ne,z as W,F as Ye,e as r,c as p,i as n,f as a,t as v,h as l,j as y,k as X,o as d,H as I,I as q,n as m,M as De,l as Z,N as Me,K as f,v as Ue,x as Qe}from"./index-BJsoK47l.js";import{d as Le}from"./vuedraggable.umd-eTiRusPO.js";import{g as ze,l as He}from"./questions-BTIDeXeU.js";import{s as Ee,c as Be,g as Oe,u as $e,e as je,f as Re}from"./zuoye-v_1cayzW.js";import"./sortable.esm-BzewootV.js";const x=N=>(Ue("data-v-71d29135"),N=N(),Qe(),N),Pe={class:"app-container"},Ae={class:"page-header"},Fe={class:"page-title"},Je=x(()=>n("div",{class:"section-title"},"基本信息",-1)),Ke={class:"section-header"},Ge=x(()=>n("div",{class:"section-title"},"试题选择",-1)),We={class:"question-stats"},Xe={class:"highlight"},Ze={class:"highlight"},et={class:"question-bank"},tt={class:"question-bank-header"},st=x(()=>n("h3",null,"题库",-1)),at={class:"question-bank-actions"},lt={class:"question-list"},ot={class:"question-item"},nt=["onClick"],it={class:"question-item-header"},ut={class:"question-type"},dt={class:"question-id"},rt=x(()=>n("div",{class:"question-meta"},null,-1)),ct={class:"question-content"},pt={class:"selected-questions"},mt=x(()=>n("div",{class:"selected-questions-header"},[n("h3",null,"已选试题")],-1)),_t={key:0,class:"empty-state"},vt=x(()=>n("p",null,"还没有选择任何试题",-1)),ft=x(()=>n("p",{class:"hint-text"},"从左侧题库中点击题目添加",-1)),gt={class:"selected-question-item"},yt={class:"selected-question-content"},ht={class:"selected-question-title"},kt={class:"selected-questions-actions"},xt={class:"form-actions"},bt={key:0},wt={key:1},Vt={class:"preview-question-title"},It={class:"preview-q-index"},qt={key:0},Tt={key:1},Ct={key:2},St={__name:"edit",setup(N){const b=Ce(),ee=Se(),Y=_(!1),o=_({examName:"",type:"exam",description:"",startTime:"",endTime:"",duration:120,passingScore:60,status:"1",classId:void 0}),te={examName:[{required:!0,message:"请输入试卷名称",trigger:"blur"}],type:[{required:!0,message:"请选择所属类型",trigger:"change"}],classId:[{required:!0,message:"请选择所属班级",trigger:"change"}],description:[{required:!0,message:"请输入试卷描述",trigger:"blur"}],startTime:[{required:!0,message:"请选择开始时间",trigger:"change"}],endTime:[{required:!0,message:"请选择结束时间",trigger:"change"}],duration:[{required:!0,message:"请输入试卷时长",trigger:"blur"}],passingScore:[{required:!0,message:"请输入及格分数",trigger:"blur"}]},O=_([]),se=async()=>{try{const s=await ze();O.value=s.map(e=>({value:e.id,label:e.name}))}catch(s){console.error("获取学科分类失败:",s),f.error("获取学科分类失败")}},D=_(""),$={single:{text:"单选题",type:"success"},multiple:{text:"多选题",type:"primary"},fill:{text:"填空题",type:"warning"},variable:{text:"不定项",type:"danger"}},M=_(""),c=_([]),z=_([]),T=_(!1),w=_({pageNum:1,pageSize:10,topicClassification:"",questionType:"",difficulty:"",content:""}),C=_(""),j=async()=>{try{T.value=!0,console.log("查询参数:",w.value);const s=await He(w.value);z.value=s.rows||[]}catch(s){console.error("获取题库列表失败:",s),f.error("获取题库列表失败")}finally{T.value=!1}};Ne([D,M,C],()=>{w.value.topicClassification=D.value,w.value.content=M.value,w.value.questionType=C.value,j()},{immediate:!0});const ae=W(()=>z.value),R=W(()=>c.value.reduce((s,e)=>s+e.score,0));function le(s){var e;return((e=$[s])==null?void 0:e.type)||"info"}function P(s){var e;return((e=$[s])==null?void 0:e.text)||s}function oe(s){return c.value.some(e=>e.questionId===s)}function ne(s){const e=c.value.findIndex(i=>i.questionId===s.questionId);e===-1?c.value.push({...s,score:s.score||5}):c.value.splice(e,1)}function ie(s){const e=c.value.findIndex(i=>i.questionId===s.questionId);e!==-1&&c.value.splice(e,1)}function ue(){c.value=[]}function de(){const s=[...c.value];for(let e=s.length-1;e>0;e--){const i=Math.floor(Math.random()*(e+1));[s[e],s[i]]=[s[i],s[e]]}c.value=s}const U=_(!1);function re(){U.value=!0}async function ce(){try{if(await A.value.validate(),c.value.length===0){f.warning("请至少选择一道题目");return}const s={examId:b.query.id,assignmentId:b.query.assignmentId,distinguish:o.value.type==="exam"?"试卷":"作业",title:o.value.examName,description:o.value.description,startTime:o.value.startTime,endTime:o.value.endTime,duration:o.value.duration,passingMark:o.value.passingScore,totalScore:R.value,classId:o.value.classId,labelId:o.value.type==="exam"?1:2,questions:c.value.map((e,i)=>({questionId:e.questionId,score:e.score,orderNum:i+1}))};Y.value?o.value.type==="homework"?(await $e(s),f.success("作业更新成功")):(await je(s),f.success("试卷更新成功")):(await Re(s),f.success(o.value.type==="exam"?"试卷创建成功":"作业创建成功")),ee.push("/aqsystem/exams/exams")}catch(s){console.error("保存失败:",s),f.error("保存失败："+(s.message||"未知错误"))}}const A=_(null),pe=async()=>{const s=b.query.id;if(s){Y.value=!0;try{const e=await Ee(s);if(e.data&&e.data.length>0){const i=e.data[0];o.value={examName:i.title,type:i.labelId===1?"exam":"homework",classId:i.classId,description:i.description,startTime:i.startTime,endTime:i.endTime,duration:60,passingScore:60},i.questionsList&&i.questionsList.length>0&&(c.value=i.questionsList.map(u=>({questionId:u.questionId,content:u.content,type:u.type,topicClassification:u.topicClassification,score:u.points||5,options:u.options?JSON.parse(u.options):null,answer:u.answer,analysis:u.analysis})))}}catch(e){console.error("获取试卷详情失败:",e),f.error("获取试卷详情失败")}}},me=async()=>{const s=b.query.assignmentId;if(s){Y.value=!0;try{const i=(await Be(s)).data;o.value={examName:i.title,type:"homework",classId:i.classId,description:i.description,startTime:"",endTime:"",duration:0,passingScore:0},i.questionsList&&i.questionsList.length>0&&(c.value=i.questionsList.map(u=>({questionId:u.questionId,content:u.content,type:u.type,topicClassification:u.topicClassification,score:u.points||5,options:u.options?JSON.parse(u.options):null,answer:u.answer,analysis:u.analysis})))}catch{f.error("获取作业详情失败")}}};function _e(s){s==="homework"&&(o.value.startTime="",o.value.endTime="",o.value.duration=120,o.value.passingScore=60,o.value.status="1")}function ve(s){C.value=C.value===s?"":s,w.value.questionType=C.value,j()}function fe(s){console.log("分数已更新:",s.score)}const H=_([]),ge=async()=>{try{const s=await Oe();console.log("班级列表响应:",s),H.value=s.map(e=>({id:e.classId,name:e.className})),console.log("处理后的班级选项:",H.value)}catch(s){console.error("获取班级列表失败:",s),f.error("获取班级列表失败")}};return Ye(()=>{b.query.id?pe():b.query.assignmentId&&me(),se(),ge()}),(s,e)=>{const i=r("el-input"),u=r("el-form-item"),g=r("el-col"),h=r("el-option"),Q=r("el-select"),L=r("el-row"),F=r("el-date-picker"),J=r("el-input-number"),ye=r("el-form"),K=r("el-card"),he=r("el-empty"),G=r("el-skeleton-item"),ke=r("el-skeleton"),E=r("el-tag"),xe=r("el-icon"),V=r("el-button"),be=r("el-radio"),we=r("el-radio-group"),Ve=r("el-checkbox"),Ie=r("el-checkbox-group"),qe=r("el-dialog");return d(),p("div",Pe,[n("div",Ae,[n("div",null,[n("h1",Fe,v(Y.value?"编辑试卷":"创建新试卷"),1)])]),a(K,{class:"form-card",shadow:"hover"},{default:l(()=>[Je,a(ye,{ref_key:"formRef",ref:A,model:o.value,rules:te,"label-width":"100px",class:"form-content"},{default:l(()=>[a(L,{gutter:20},{default:l(()=>[a(g,{span:8},{default:l(()=>[a(u,{label:"试卷名称",prop:"examName"},{default:l(()=>[a(i,{modelValue:o.value.examName,"onUpdate:modelValue":e[0]||(e[0]=t=>o.value.examName=t),placeholder:"输入试卷名称"},null,8,["modelValue"])]),_:1})]),_:1}),a(g,{span:8},{default:l(()=>[a(u,{label:"所属类型",prop:"type"},{default:l(()=>[a(Q,{modelValue:o.value.type,"onUpdate:modelValue":e[1]||(e[1]=t=>o.value.type=t),placeholder:"请选择类型",class:"full-width",onChange:_e},{default:l(()=>[a(h,{label:"作业",value:"homework"}),a(h,{label:"试卷",value:"exam"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(g,{span:8},{default:l(()=>[a(u,{label:"所属班级",prop:"classId"},{default:l(()=>[a(Q,{modelValue:o.value.classId,"onUpdate:modelValue":e[2]||(e[2]=t=>o.value.classId=t),placeholder:"请选择班级",class:"full-width"},{default:l(()=>[(d(!0),p(I,null,q(H.value,t=>(d(),y(h,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(u,{label:"试卷描述",prop:"description"},{default:l(()=>[a(i,{modelValue:o.value.description,"onUpdate:modelValue":e[3]||(e[3]=t=>o.value.description=t),type:"textarea",rows:3,placeholder:"输入试卷描述..."},null,8,["modelValue"])]),_:1}),o.value.type==="exam"?(d(),y(L,{key:0,gutter:20},{default:l(()=>[a(g,{span:12},{default:l(()=>[a(u,{label:"开始时间",prop:"startTime"},{default:l(()=>[a(F,{modelValue:o.value.startTime,"onUpdate:modelValue":e[4]||(e[4]=t=>o.value.startTime=t),type:"datetime",placeholder:"选择开始时间",class:"full-width","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1}),a(g,{span:12},{default:l(()=>[a(u,{label:"结束时间",prop:"endTime"},{default:l(()=>[a(F,{modelValue:o.value.endTime,"onUpdate:modelValue":e[5]||(e[5]=t=>o.value.endTime=t),type:"datetime",placeholder:"选择结束时间",class:"full-width","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):X("",!0),o.value.type==="exam"?(d(),y(L,{key:1,gutter:20},{default:l(()=>[a(g,{span:8},{default:l(()=>[a(u,{label:"试卷时长",prop:"duration"},{default:l(()=>[a(J,{modelValue:o.value.duration,"onUpdate:modelValue":e[6]||(e[6]=t=>o.value.duration=t),min:30,max:180,step:30,class:"full-width"},null,8,["modelValue"])]),_:1})]),_:1}),a(g,{span:8},{default:l(()=>[a(u,{label:"及格分数",prop:"passingScore"},{default:l(()=>[a(J,{modelValue:o.value.passingScore,"onUpdate:modelValue":e[7]||(e[7]=t=>o.value.passingScore=t),min:0,max:100,step:5,class:"full-width"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):X("",!0)]),_:1},8,["model"])]),_:1}),a(K,{class:"form-card",shadow:"hover"},{default:l(()=>[n("div",Ke,[Ge,n("div",We,[m(" 已选择 "),n("span",Xe,v(c.value.length),1),m(" 题 / 总分 "),n("span",Ze,v(R.value),1),m(" 分 ")])]),a(L,{gutter:20},{default:l(()=>[a(g,{span:16},{default:l(()=>[n("div",et,[n("div",tt,[st,n("div",at,[a(Q,{modelValue:D.value,"onUpdate:modelValue":e[8]||(e[8]=t=>D.value=t),placeholder:"题目类型",size:"small",class:"subject-select custom-select"},{default:l(()=>[(d(!0),p(I,null,q(O.value,t=>(d(),y(h,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(i,{modelValue:M.value,"onUpdate:modelValue":e[9]||(e[9]=t=>M.value=t),placeholder:"搜索题目...","prefix-icon":"Search",clearable:"",class:"search-input"},null,8,["modelValue"])])]),n("div",lt,[!T.value&&z.value.length===0?(d(),y(he,{key:0,description:"暂无题目"})):T.value?(d(),y(ke,{key:1,loading:T.value,animated:"",count:3},{template:l(()=>[n("div",ot,[a(G,{variant:"text",style:{width:"30%"}}),a(G,{variant:"text",style:{width:"100%"}})])]),_:1},8,["loading"])):(d(!0),p(I,{key:2},q(ae.value,t=>(d(),p("div",{key:t.questionId,class:De(["question-item",{selected:oe(t.questionId)}]),onClick:k=>ne(t)},[n("div",it,[n("div",ut,[a(E,{type:le(t.type),size:"small",class:"clickable-tag",onClick:k=>ve(t.type)},{default:l(()=>[m(v(P(t.type)),1)]),_:2},1032,["type","onClick"]),a(E,{size:"small",type:"info",class:"topic-tag"},{default:l(()=>[m(v(t.topicClassification),1)]),_:2},1024),n("span",dt,"ID: #"+v(t.questionId),1)]),rt]),n("div",ct,v(t.content),1)],10,nt))),128))])])]),_:1}),a(g,{span:8},{default:l(()=>[n("div",pt,[mt,c.value.length===0?(d(),p("div",_t,[a(xe,{size:48},{default:l(()=>[a(Z(Me))]),_:1}),vt,ft])):(d(),y(Z(Le),{key:1,modelValue:c.value,"onUpdate:modelValue":e[10]||(e[10]=t=>c.value=t),"item-key":"questionId",class:"selected-questions-list"},{item:l(({element:t})=>[n("div",gt,[n("div",yt,[a(E,{size:"small",class:"question-type-tag"},{default:l(()=>[m(v(P(t.type)),1)]),_:2},1024),a(Q,{modelValue:t.score,"onUpdate:modelValue":k=>t.score=k,size:"small",class:"score-select",onChange:k=>fe(t)},{default:l(()=>[a(h,{label:"3分",value:3}),a(h,{label:"5分",value:5}),a(h,{label:"10分",value:10})]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),a(V,{type:"danger",link:"",icon:"Delete",class:"delete-btn",onClick:k=>ie(t)},null,8,["onClick"])]),n("div",ht,v(t.content),1)])]),_:1},8,["modelValue"])),n("div",kt,[a(V,{type:"danger",plain:"",onClick:ue},{default:l(()=>[m(" 清空 ")]),_:1}),a(V,{type:"primary",plain:"",onClick:de},{default:l(()=>[m(" 随机排序 ")]),_:1})])])]),_:1})]),_:1})]),_:1}),n("div",xt,[a(V,{type:"primary",onClick:re},{default:l(()=>[m("预览")]),_:1}),a(V,{type:"success",onClick:ce},{default:l(()=>[m("保存并发布")]),_:1})]),a(qe,{modelValue:U.value,"onUpdate:modelValue":e[12]||(e[12]=t=>U.value=t),title:"试卷预览",width:"600px","close-on-click-modal":!1},{footer:l(()=>[a(V,{onClick:e[11]||(e[11]=t=>U.value=!1)},{default:l(()=>[m("关闭")]),_:1})]),default:l(()=>[c.value.length===0?(d(),p("div",bt,"暂无试题")):(d(),p("div",wt,[(d(!0),p(I,null,q(c.value,(t,k)=>(d(),p("div",{key:t.id,class:"preview-question"},[n("div",Vt,[n("span",It,v(k+1)+".",1),m(" "+v(t.content),1)]),t.type==="single"?(d(),p("div",qt,[a(we,null,{default:l(()=>[(d(!0),p(I,null,q(t.options,(S,B)=>(d(),y(be,{key:B,label:S},{default:l(()=>[m(v(S),1)]),_:2},1032,["label"]))),128))]),_:2},1024)])):t.type==="multiple"?(d(),p("div",Tt,[a(Ie,null,{default:l(()=>[(d(!0),p(I,null,q(t.options,(S,B)=>(d(),y(Ve,{key:B,label:S},{default:l(()=>[m(v(S),1)]),_:2},1032,["label"]))),128))]),_:2},1024)])):(d(),p("div",Ct,[a(i,{type:"textarea",rows:2,placeholder:"请输入答案..."})]))]))),128))]))]),_:1},8,["modelValue"])])}}},Qt=Te(St,[["__scopeId","data-v-71d29135"]]);export{Qt as default};
