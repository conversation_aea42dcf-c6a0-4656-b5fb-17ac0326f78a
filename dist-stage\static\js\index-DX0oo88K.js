import{Z as ie,_ as ll,r as y,C as ae,F as tl,e as _,G as al,c as d,o,f as t,h as a,i as s,n as p,H as V,I as S,J as sl,j as k,t as c,k as b,M as Te,p as se,l as oe,ad as ne,K as g,v as ol,x as nl}from"./index-BJsoK47l.js";import Oe from"./InterviewFeedbackDetail-Cpu6oh8i.js";function il(N){return ie({url:"/interview/page",method:"post",data:N})}function xe(N){return ie({url:"/interview/detail/"+N,method:"get"})}function rl(N){return ie({url:"/interview/feedback",method:"PUT",data:N})}function Je(N){return ie({url:"/interview/transcription/"+N,method:"get"})}const O=N=>(ol("data-v-b9baeb86"),N=N(),nl(),N),dl={class:"interview-manage-container"},ul={class:"filter-header"},cl=O(()=>s("span",{class:"filter-title"},"筛选查询",-1)),pl={class:"filter-buttons"},ml=O(()=>s("span",{class:"time-separator"},"至",-1)),vl={key:0},_l={key:1},fl={class:"pagination-container"},hl={class:"dialog-footer"},yl={key:0,class:"media-container"},gl=O(()=>s("div",{class:"media-header"},[s("h3",null,"媒体播放")],-1)),bl={key:0},kl={controls:"",style:{width:"100%","max-height":"400px"}},wl=["src","type"],Vl={key:1},Sl={class:"audio-container"},Nl={controls:"",style:{width:"100%"}},Ul=["src","type"],Tl={key:2,class:"media-url"},Ol=["href"],xl={class:"speech-content-container"},Jl=O(()=>s("div",{class:"speech-header"},[s("h3",null,"语音转文字内容")],-1)),Cl={class:"speech-content"},ql={class:"speech-chapters"},zl={class:"speech-chapters-header"},Il=O(()=>s("h4",null,"章节段落",-1)),Dl={key:0,class:"chapter-tip"},$l=["onClick"],Rl={class:"chapter-title"},Fl={class:"chapter-time-container"},Ml={class:"chapter-time"},Yl={class:"speech-records"},Hl={class:"speech-records-header"},Ll={key:0,class:"chapter-summary"},El=O(()=>s("div",{class:"summary-title"},"章节总结：",-1)),Wl={class:"summary-content"},jl=["onClick"],Pl={class:"record-header"},Bl={class:"record-speaker"},Ql={class:"record-time"},Gl={class:"record-content"},Kl={class:"qa-content"},Zl={key:0,class:"loading-container"},Al={key:1,class:"empty-container"},Xl={key:2,class:"qa-list"},et={class:"question"},lt={class:"question-label"},tt={class:"question-content"},at={class:"answer"},st=O(()=>s("div",{class:"answer-label"},"回答:",-1)),ot={class:"answer-content"},nt={class:"dialog-footer"},it={key:0,class:"media-container"},rt=O(()=>s("div",{class:"media-header"},[s("h3",null,"媒体播放")],-1)),dt={key:0},ut={controls:"",style:{width:"100%","max-height":"400px"}},ct=["src","type"],pt={key:1},mt={controls:"",style:{width:"100%"}},vt=["src","type"],_t={key:2,class:"media-url"},ft=["href"],ht={class:"speech-content-container"},yt=O(()=>s("div",{class:"speech-header"},[s("h3",null,"语音转文字内容")],-1)),gt={class:"speech-content"},bt={class:"speech-chapters"},kt={class:"speech-chapters-header"},wt=O(()=>s("h4",null,"章节段落",-1)),Vt=["onClick"],St={class:"chapter-title"},Nt={class:"chapter-time-container"},Ut={class:"chapter-time"},Tt={class:"speech-records"},Ot={class:"speech-records-header"},xt={key:0,class:"chapter-summary"},Jt=O(()=>s("div",{class:"summary-title"},"章节总结：",-1)),Ct={class:"summary-content"},qt=["onClick"],zt={class:"record-header"},It={class:"record-speaker"},Dt={class:"record-time"},$t={class:"record-content"},Rt={class:"qa-content"},Ft={key:0,class:"loading-container"},Mt={key:1,class:"empty-container"},Yt={key:2,class:"qa-list"},Ht={class:"question"},Lt={class:"question-label"},Et={class:"question-content"},Wt={class:"answer"},jt=O(()=>s("div",{class:"answer-label"},"回答:",-1)),Pt={class:"answer-content"},Bt={__name:"index",setup(N){const z=r=>{if(r==null)return"00:00.000";const l=Math.floor(r/1e3),n=Math.floor(l/60),T=l%60,f=r%1e3;return`${n.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}.${f.toString().padStart(3,"0")}`},pe=y(JSON.parse(JSON.stringify([]))),R=y(JSON.parse(JSON.stringify(!1))),me=y(JSON.parse(JSON.stringify(0))),E=y(JSON.parse(JSON.stringify(1))),G=y(JSON.parse(JSON.stringify(10))),v=ae({candidateName:"",type:"",position:"",stage:"",status:null,startTime:"",endTime:""}),W=y(JSON.parse(JSON.stringify(!1))),Ce=y(JSON.parse(JSON.stringify("添加面试"))),qe=y(JSON.parse(JSON.stringify("add"))),u=ae({id:null,userId:null,type:"mock",candidateName:"",company:"",position:"",stage:"tech",experience:"fresh",status:0,overallScore:null,feedback:"",strengths:"",improvements:"",videoUrl:"",interviewTime:"",questions:[]}),j=y(JSON.parse(JSON.stringify(!1))),i=ae({id:"",score:0,feedback:"",strengths:"",improvements:"",videoUrl:"",isTranslating:!1,result:null}),U=y(JSON.parse(JSON.stringify([]))),M=y(JSON.parse(JSON.stringify([]))),h=y(JSON.parse(JSON.stringify(null))),C=y(JSON.parse(JSON.stringify([]))),P=y(JSON.parse(JSON.stringify(!1))),B=y(JSON.parse(JSON.stringify(!1))),ve=y(JSON.parse(JSON.stringify("mock"))),x=y(JSON.parse(JSON.stringify([]))),F=y(JSON.parse(JSON.stringify(!1))),Y=y(JSON.parse(JSON.stringify("chapters")));y(JSON.parse(JSON.stringify(null)));const m=ae({score:0,feedback:"",strengths:"",improvements:"",videoUrl:"",type:"mock",result:null}),re=y(JSON.parse(JSON.stringify(!1))),ze={type:[{required:!0,message:"请选择面试类型",trigger:"change"}],candidateName:[{required:!0,message:"请输入应聘者姓名",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],position:[{required:!0,message:"请输入面试职位",trigger:"blur"}],stage:[{required:!0,message:"请选择面试阶段",trigger:"change"}],experience:[{required:!0,message:"请选择工作经验",trigger:"change"}],interviewTime:[{required:!0,message:"请选择面试时间",trigger:"change"}]},_e=[{value:"mock",label:"模拟面试"},{value:"formal",label:"正式面试"}],fe=[{value:"hr",label:"HR面试"},{value:"tech",label:"技术面试"}],he=[{value:"fresh",label:"应届生"},{value:"1-31",label:"1-3年"},{value:"3-5",label:"3-5年"},{value:"5+",label:"5年以上"}],de=[{value:"1",label:"开发"},{value:"2",label:"测试"},{value:"3",label:"技术支持"}],Ie=[{value:0,label:"进行中"},{value:1,label:"等待结果"},{value:2,label:"已完成"}],De={0:"进行中",1:"等待结果",2:"已完成"},K=y(JSON.parse(JSON.stringify(null))),ye=y(JSON.parse(JSON.stringify(null))),I=async()=>{R.value=!0;try{const r={pageNum:E.value,pageSize:G.value,candidateName:v.candidateName,type:v.type,position:v.position,stage:v.stage,status:v.status,startTime:v.startTime,endTime:v.endTime},l=await il(r);l.code===0?(pe.value=l.data.records||[],me.value=l.data.total||0):g.error(l.message||"获取面试列表失败")}catch(r){console.error("获取面试列表失败:",r),g.error("获取面试列表失败")}finally{R.value=!1}},$e=()=>{v.candidateName="",v.type="",v.position="",v.stage="",v.status=null,v.startTime="",v.endTime="",I()},Re=()=>{E.value=1,I()},Fe=r=>{G.value=r,I()},Me=r=>{E.value=r,I()},Ye=r=>{const l=de.find(n=>n.value===r);return l?l.label:r},He=r=>{const l=he.find(n=>n.value===r);return l?l.label:r},Le=r=>De[r]||"未知",Ee=r=>({0:"warning",1:"danger",2:"success"})[r]||"info",We=async r=>{i.id="",i.score=0,i.feedback="",i.strengths="",i.improvements="",i.videoUrl="",i.isTranslating=!1,i.result=null,U.value=[],M.value=[],x.value=[],h.value=null,C.value=[],P.value=!1,B.value=!1,F.value=!1;try{R.value=!0,ve.value=r.type;const l=await xe(r.id);l.code===0&&l.data?(i.id=l.data.id,i.score=l.data.overallScore||0,i.feedback=l.data.feedback||"",i.strengths=l.data.strengths||"",i.improvements=l.data.improvements||"",i.videoUrl=l.data.videoUrl||"",i.isTranslating=!1,i.result=l.data.result,U.value=l.data.chapters||[],M.value=l.data.records||[],M.value.forEach(n=>{n.speakerName=n.speakerId!==void 0&&n.speakerId!==null?`发言人${n.speakerId}`:"未知发言人"}),U.value.length>0?H(U.value[0]):(h.value=null,C.value=[])):g.error(l.message||"获取面试详情失败"),F.value=!0;try{const n=await Je(r.id);n.code===0&&n.data?x.value=n.data:(x.value=[],g.error(n.message||"获取问答回顾失败"))}catch(n){x.value=[],console.error("获取问答回顾失败:",n),g.error("获取问答回顾失败")}finally{F.value=!1}Y.value=r.type==="formal"?"chapters":"qa"}catch(l){console.error("获取面试详情失败:",l),g.error("获取面试详情失败")}finally{R.value=!1,j.value=!0}},H=r=>{h.value=r,je(),Q(r.startTime)},je=()=>{if(!h.value){C.value=[];return}C.value=M.value.filter(r=>r.startTime>=h.value.startTime&&r.endTime<=h.value.endTime)},Q=r=>{const l=document.querySelector("video")||document.querySelector("audio");l&&r!==void 0&&(l.currentTime=r/1e3)},ge=r=>r==null?"info":"primary",Pe=()=>{ye.value.validate(async r=>{if(r)try{const l={id:i.id,overallScore:i.score,feedback:i.feedback,strengths:i.strengths,improvements:i.improvements,result:i.result},n=await rl(l);n.code===0?(g.success("评分和反馈已更新"),j.value=!1,I()):g.error(n.message||"更新评分和反馈失败")}catch(l){console.error("更新评分和反馈失败:",l),g.error("更新评分和反馈失败")}})},Be=()=>{K.value.validate(async r=>{if(r)try{const l=JSON.parse(JSON.stringify(u));if(qe.value==="add"){const n=await addInterview(l);n.code===0?(g.success("添加成功"),W.value=!1,I()):g.error(n.message||"添加失败")}else{const n=await updateInterview(l);n.code===0?(g.success("编辑成功"),W.value=!1,I()):g.error(n.message||"编辑失败")}}catch(l){console.error("保存面试失败:",l),g.error("保存失败")}})},Qe=()=>{K.value&&K.value.resetFields(),u.id=null,u.userId=null,u.type="mock",u.candidateName="",u.company="",u.position="",u.stage="tech",u.experience="fresh",u.status=0,u.overallScore=null,u.feedback="",u.strengths="",u.improvements="",u.videoUrl="",u.interviewTime="",u.questions=[]},Z=r=>r?r.replace("T"," ").substring(0,19):"",Ge=async r=>{try{R.value=!0;const l=await xe(r.id);if(l.code===0&&l.data){m.score=l.data.overallScore||0,m.feedback=l.data.feedback||"",m.strengths=l.data.strengths||"",m.improvements=l.data.improvements||"",m.videoUrl=l.data.videoUrl||"",m.type=l.data.type||"mock",m.result=l.data.result,U.value=l.data.chapters||[],M.value=l.data.records||[],M.value.forEach(n=>{n.speakerName=n.speakerId!==void 0&&n.speakerId!==null?`发言人${n.speakerId}`:"未知发言人"}),U.value.length>0?H(U.value[0]):(h.value=null,C.value=[]),Y.value=l.data.type==="formal"?"chapters":"qa",F.value=!0;try{const n=await Je(r.id);n.code===0&&n.data?x.value=n.data:(x.value=[],g.error(n.message||"获取问答回顾失败"))}catch(n){x.value=[],console.error("获取问答回顾失败:",n),g.error("获取问答回顾失败")}finally{F.value=!1}re.value=!0}else g.error(l.message||"获取面试详情失败")}catch(l){console.error("获取面试详情失败:",l),g.error("获取面试详情失败")}finally{R.value=!1}};return tl(()=>{I()}),(r,l)=>{const n=_("el-button"),T=_("el-input"),f=_("el-form-item"),D=_("el-option"),$=_("el-select"),ue=_("el-date-picker"),A=_("el-form"),X=_("el-card"),J=_("el-table-column"),q=_("el-tag"),Ke=_("el-table"),Ze=_("el-pagination"),ce=_("el-dialog"),Ae=_("el-alert"),ee=_("el-icon"),L=_("el-empty"),le=_("el-scrollbar"),te=_("el-tab-pane"),be=_("el-skeleton"),ke=_("el-timeline-item"),we=_("el-timeline"),Ve=_("el-tabs"),Se=_("el-slider"),Ne=_("el-radio"),Xe=_("el-radio-group"),Ue=_("el-divider"),el=al("loading");return o(),d("div",dl,[t(X,{class:"filter-card"},{default:a(()=>[s("div",ul,[cl,s("div",pl,[t(n,{type:"primary",onClick:Re},{default:a(()=>[p("查询")]),_:1}),t(n,{onClick:$e},{default:a(()=>[p("重置")]),_:1})])]),t(A,{model:v,inline:""},{default:a(()=>[t(f,{label:"应聘者姓名"},{default:a(()=>[t(T,{modelValue:v.candidateName,"onUpdate:modelValue":l[0]||(l[0]=e=>v.candidateName=e),placeholder:"请输入应聘者姓名",clearable:""},null,8,["modelValue"])]),_:1}),t(f,{label:"面试类型"},{default:a(()=>[t($,{modelValue:v.type,"onUpdate:modelValue":l[1]||(l[1]=e=>v.type=e),placeholder:"请选择面试类型",clearable:"",style:{width:"180px"}},{default:a(()=>[(o(),d(V,null,S(_e,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试职位"},{default:a(()=>[t($,{modelValue:v.position,"onUpdate:modelValue":l[2]||(l[2]=e=>v.position=e),placeholder:"请选择面试职位",clearable:"",style:{width:"180px"}},{default:a(()=>[(o(),d(V,null,S(de,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试阶段"},{default:a(()=>[t($,{modelValue:v.stage,"onUpdate:modelValue":l[3]||(l[3]=e=>v.stage=e),placeholder:"请选择面试阶段",clearable:"",style:{width:"180px"}},{default:a(()=>[(o(),d(V,null,S(fe,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试状态"},{default:a(()=>[t($,{modelValue:v.status,"onUpdate:modelValue":l[4]||(l[4]=e=>v.status=e),placeholder:"请选择面试状态",clearable:"",style:{width:"180px"}},{default:a(()=>[(o(),d(V,null,S(Ie,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试时间"},{default:a(()=>[t(ue,{modelValue:v.startTime,"onUpdate:modelValue":l[5]||(l[5]=e=>v.startTime=e),type:"datetime",placeholder:"开始时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"180px"}},null,8,["modelValue"]),ml,t(ue,{modelValue:v.endTime,"onUpdate:modelValue":l[6]||(l[6]=e=>v.endTime=e),type:"datetime",placeholder:"结束时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"180px"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),t(X,{class:"data-card"},{default:a(()=>[sl((o(),k(Ke,{data:pe.value,border:"",stripe:"",style:{width:"100%"},"row-key":"id"},{default:a(()=>[t(J,{type:"index",width:"50",align:"center",label:"序号"}),t(J,{prop:"candidateName",label:"应聘者姓名",width:"120"}),t(J,{label:"面试类型",width:"100"},{default:a(e=>[t(q,{type:e.row.type==="mock"?"info":"primary"},{default:a(()=>[p(c(e.row.type==="mock"?"模拟面试":"正式面试"),1)]),_:2},1032,["type"])]),_:1}),t(J,{label:"面试职位",width:"150"},{default:a(e=>[p(c(Ye(e.row.position)),1)]),_:1}),t(J,{label:"面试阶段",width:"100"},{default:a(e=>[t(q,{type:e.row.stage==="hr"?"success":"warning"},{default:a(()=>[p(c(e.row.stage==="hr"?"HR面试":"技术面试"),1)]),_:2},1032,["type"])]),_:1}),t(J,{label:"工作经验",width:"100"},{default:a(e=>[p(c(He(e.row.experience)),1)]),_:1}),t(J,{label:"面试状态",width:"100"},{default:a(e=>[t(q,{type:Ee(e.row.status)},{default:a(()=>[p(c(Le(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(J,{label:"评分",width:"80"},{default:a(e=>[e.row.overallScore?(o(),d("span",vl,c(e.row.overallScore),1)):(o(),d("span",_l,"-"))]),_:1}),t(J,{label:"面试时间",width:"180"},{default:a(e=>[p(c(Z(e.row.interviewTime)),1)]),_:1}),t(J,{label:"创建时间",width:"180"},{default:a(e=>[p(c(Z(e.row.createTime)),1)]),_:1}),t(J,{label:"操作",width:"200",align:"center"},{default:a(e=>[e.row.status===0||e.row.status===1?(o(),k(n,{key:0,size:"small",type:"primary",onClick:w=>We(e.row)},{default:a(()=>[p("评分反馈")]),_:2},1032,["onClick"])):e.row.status===2?(o(),k(n,{key:1,size:"small",type:"info",onClick:w=>Ge(e.row)},{default:a(()=>[p("查看详情")]),_:2},1032,["onClick"])):b("",!0)]),_:1})]),_:1},8,["data"])),[[el,R.value]]),s("div",fl,[t(Ze,{"current-page":E.value,"onUpdate:currentPage":l[7]||(l[7]=e=>E.value=e),"page-size":G.value,"onUpdate:pageSize":l[8]||(l[8]=e=>G.value=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:me.value,onSizeChange:Fe,onCurrentChange:Me},null,8,["current-page","page-size","total"])])]),_:1}),t(ce,{modelValue:W.value,"onUpdate:modelValue":l[18]||(l[18]=e=>W.value=e),title:Ce.value,width:"650px",onClose:Qe},{footer:a(()=>[s("div",hl,[t(n,{onClick:l[17]||(l[17]=e=>W.value=!1)},{default:a(()=>[p("取消")]),_:1}),t(n,{type:"primary",onClick:Be},{default:a(()=>[p("确定")]),_:1})])]),default:a(()=>[t(A,{ref_key:"formRef",ref:K,model:u,rules:ze,"label-width":"100px"},{default:a(()=>[t(f,{label:"面试类型",prop:"type"},{default:a(()=>[t($,{modelValue:u.type,"onUpdate:modelValue":l[9]||(l[9]=e=>u.type=e),placeholder:"请选择面试类型",style:{width:"100%"}},{default:a(()=>[(o(),d(V,null,S(_e,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"应聘者姓名",prop:"candidateName"},{default:a(()=>[t(T,{modelValue:u.candidateName,"onUpdate:modelValue":l[10]||(l[10]=e=>u.candidateName=e),placeholder:"请输入应聘者姓名"},null,8,["modelValue"])]),_:1}),u.type==="formal"?(o(),k(f,{key:0,label:"面试公司",prop:"company"},{default:a(()=>[t(T,{modelValue:u.company,"onUpdate:modelValue":l[11]||(l[11]=e=>u.company=e),placeholder:"请输入面试公司"},null,8,["modelValue"])]),_:1})):b("",!0),t(f,{label:"面试职位",prop:"position"},{default:a(()=>[t($,{modelValue:u.position,"onUpdate:modelValue":l[12]||(l[12]=e=>u.position=e),placeholder:"请选择面试职位",style:{width:"100%"}},{default:a(()=>[(o(),d(V,null,S(de,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试阶段",prop:"stage"},{default:a(()=>[t($,{modelValue:u.stage,"onUpdate:modelValue":l[13]||(l[13]=e=>u.stage=e),placeholder:"请选择面试阶段",style:{width:"100%"}},{default:a(()=>[(o(),d(V,null,S(fe,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"工作经验",prop:"experience"},{default:a(()=>[t($,{modelValue:u.experience,"onUpdate:modelValue":l[14]||(l[14]=e=>u.experience=e),placeholder:"请选择工作经验",style:{width:"100%"}},{default:a(()=>[(o(),d(V,null,S(he,e=>t(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试时间",prop:"interviewTime"},{default:a(()=>[t(ue,{modelValue:u.interviewTime,"onUpdate:modelValue":l[15]||(l[15]=e=>u.interviewTime=e),type:"datetime",placeholder:"请选择面试时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(f,{label:"视频URL",prop:"videoUrl"},{default:a(()=>[t(T,{modelValue:u.videoUrl,"onUpdate:modelValue":l[16]||(l[16]=e=>u.videoUrl=e),placeholder:"请输入面试视频URL"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(ce,{modelValue:j.value,"onUpdate:modelValue":l[26]||(l[26]=e=>j.value=e),title:"评分与反馈",width:"800px"},{footer:a(()=>[s("div",nt,[t(n,{onClick:l[25]||(l[25]=e=>j.value=!1)},{default:a(()=>[p("取消")]),_:1}),t(n,{type:"primary",onClick:Pe},{default:a(()=>[p("确定")]),_:1})])]),default:a(()=>[i.videoUrl?(o(),d("div",yl,[gl,i.videoUrl.endsWith(".mp4")||i.videoUrl.endsWith(".webm")?(o(),d("div",bl,[s("video",kl,[s("source",{src:i.videoUrl,type:"video/"+i.videoUrl.split(".").pop()},null,8,wl),p(" 您的浏览器不支持视频播放 ")])])):i.videoUrl.endsWith(".mp3")||i.videoUrl.endsWith(".wav")?(o(),d("div",Vl,[s("div",Sl,[s("audio",Nl,[s("source",{src:i.videoUrl,type:"audio/"+i.videoUrl.split(".").pop()},null,8,Ul),p(" 您的浏览器不支持音频播放 ")])])])):(o(),d("div",Tl,[s("p",null,[p("媒体链接: "),s("a",{href:i.videoUrl,target:"_blank"},c(i.videoUrl),9,Ol)])]))])):b("",!0),s("div",xl,[Jl,t(Ve,{modelValue:Y.value,"onUpdate:modelValue":l[19]||(l[19]=e=>Y.value=e),class:"speech-tabs"},{default:a(()=>[ve.value==="formal"?(o(),k(te,{key:0,label:"章节速览",name:"chapters"},{default:a(()=>[s("div",Cl,[s("div",ql,[s("div",zl,[Il,P.value?(o(),k(q,{key:0,type:"info",size:"small"},{default:a(()=>[p("加载中...")]),_:1})):b("",!0)]),i.videoUrl&&U.value.length>0?(o(),d("div",Dl,[t(Ae,{title:"点击章节段落或对话记录可跳转到视频/音频对应时间位置（时间单位：毫秒）",type:"info",closable:!1,"show-icon":""})])):b("",!0),t(le,{height:"400px"},{default:a(()=>[(o(!0),d(V,null,S(U.value,e=>(o(),d("div",{key:e.id,class:Te(["speech-chapter-item",{active:h.value&&h.value.id===e.id}]),onClick:w=>H(e)},[s("div",Rl,c(e.chapterTitle),1),s("div",Fl,[s("div",Ml,c(z(e.startTime))+" - "+c(z(e.endTime)),1),t(n,{type:"primary",size:"small",circle:"",class:"jump-button",onClick:se(w=>H(e),["stop"])},{default:a(()=>[t(ee,null,{default:a(()=>[t(oe(ne))]),_:1})]),_:2},1032,["onClick"])])],10,$l))),128)),!P.value&&U.value.length===0?(o(),k(L,{key:0,description:"暂无章节段落"})):b("",!0)]),_:1})]),s("div",Yl,[s("div",Hl,[s("h4",null,c(h.value?h.value.chapterTitle:"对话内容"),1),B.value?(o(),k(q,{key:0,type:"info",size:"small"},{default:a(()=>[p("加载中...")]),_:1})):b("",!0)]),t(le,{height:"400px"},{default:a(()=>[h.value&&h.value.chapterSummary?(o(),d("div",Ll,[El,s("div",Wl,c(h.value.chapterSummary),1)])):b("",!0),(o(!0),d(V,null,S(C.value,e=>(o(),d("div",{key:e.id,class:"speech-record-item",onClick:w=>Q(e.startTime)},[s("div",Pl,[s("div",Bl,[t(q,{size:"small",type:ge(e.speakerId)},{default:a(()=>[p(c(e.speakerName||"发言人0"),1)]),_:2},1032,["type"])]),s("div",Ql,[p(c(z(e.startTime))+" - "+c(z(e.endTime))+" ",1),t(n,{type:"primary",size:"small",circle:"",class:"jump-button-small",onClick:se(w=>Q(e.startTime),["stop"])},{default:a(()=>[t(ee,null,{default:a(()=>[t(oe(ne))]),_:1})]),_:2},1032,["onClick"])])]),s("div",Gl,c(e.content),1)],8,jl))),128)),!B.value&&(!h.value||C.value.length===0)?(o(),k(L,{key:1,description:"暂无对话内容"})):b("",!0)]),_:1})])])]),_:1})):b("",!0),t(te,{label:"问答回顾",name:"qa"},{default:a(()=>[s("div",Kl,[F.value?(o(),d("div",Zl,[t(be,{rows:5,animated:""})])):x.value.length===0?(o(),d("div",Al,[t(L,{description:"暂无问答记录"})])):(o(),d("div",Xl,[t(we,null,{default:a(()=>[(o(!0),d(V,null,S(x.value,(e,w)=>(o(),k(ke,{key:w,timestamp:Z(e.createTime),placement:"top",type:w%2===0?"primary":"success"},{default:a(()=>[t(X,{class:"qa-card"},{default:a(()=>[s("div",et,[s("div",lt,"问题 "+c(e.questionIndex+1)+":",1),s("div",tt,c(e.question),1)]),s("div",at,[st,s("div",ot,c(e.transcription),1)])]),_:2},1024)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]))])]),_:1})]),_:1},8,["modelValue"])]),t(A,{ref_key:"feedbackFormRef",ref:ye,model:i,"label-width":"100px"},{default:a(()=>[t(f,{label:"总体评分",prop:"score"},{default:a(()=>[t(Se,{modelValue:i.score,"onUpdate:modelValue":l[20]||(l[20]=e=>i.score=e),min:0,max:100,step:1,"show-input":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(f,{label:"面试结果",prop:"result"},{default:a(()=>[t(Xe,{modelValue:i.result,"onUpdate:modelValue":l[21]||(l[21]=e=>i.result=e)},{default:a(()=>[t(Ne,{label:1},{default:a(()=>[p("面试通过")]),_:1}),t(Ne,{label:0},{default:a(()=>[p("面试未通过")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"面试反馈",prop:"feedback"},{default:a(()=>[t(T,{modelValue:i.feedback,"onUpdate:modelValue":l[22]||(l[22]=e=>i.feedback=e),type:"textarea",rows:3,placeholder:"请输入面试整体反馈"},null,8,["modelValue"])]),_:1}),t(f,{label:"优势表现",prop:"strengths"},{default:a(()=>[t(T,{modelValue:i.strengths,"onUpdate:modelValue":l[23]||(l[23]=e=>i.strengths=e),type:"textarea",rows:3,placeholder:"请输入应聘者的优势表现"},null,8,["modelValue"])]),_:1}),t(f,{label:"改进建议",prop:"improvements"},{default:a(()=>[t(T,{modelValue:i.improvements,"onUpdate:modelValue":l[24]||(l[24]=e=>i.improvements=e),type:"textarea",rows:3,placeholder:"请输入应聘者的改进建议"},null,8,["modelValue"])]),_:1}),t(Ue,null,{default:a(()=>[p("预览")]),_:1}),t(Oe,{score:i.score,feedback:i.feedback,strengths:i.strengths,improvements:i.improvements,result:i.result},null,8,["score","feedback","strengths","improvements","result"])]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(ce,{modelValue:re.value,"onUpdate:modelValue":l[32]||(l[32]=e=>re.value=e),title:"面试详情",width:"800px","close-on-click-modal":!1},{default:a(()=>[m.videoUrl?(o(),d("div",it,[rt,m.videoUrl.endsWith(".mp4")||m.videoUrl.endsWith(".webm")?(o(),d("div",dt,[s("video",ut,[s("source",{src:m.videoUrl,type:"video/"+m.videoUrl.split(".").pop()},null,8,ct),p(" 您的浏览器不支持视频播放 ")])])):m.videoUrl.endsWith(".mp3")||m.videoUrl.endsWith(".wav")?(o(),d("div",pt,[s("audio",mt,[s("source",{src:m.videoUrl,type:"audio/"+m.videoUrl.split(".").pop()},null,8,vt),p(" 您的浏览器不支持音频播放 ")])])):(o(),d("div",_t,[s("p",null,[p("媒体链接: "),s("a",{href:m.videoUrl,target:"_blank"},c(m.videoUrl),9,ft)])]))])):b("",!0),s("div",ht,[yt,t(Ve,{modelValue:Y.value,"onUpdate:modelValue":l[27]||(l[27]=e=>Y.value=e),class:"speech-tabs"},{default:a(()=>[m.type==="formal"?(o(),k(te,{key:0,label:"章节速览",name:"chapters"},{default:a(()=>[s("div",gt,[s("div",bt,[s("div",kt,[wt,P.value?(o(),k(q,{key:0,type:"info",size:"small"},{default:a(()=>[p("加载中...")]),_:1})):b("",!0)]),t(le,{height:"400px"},{default:a(()=>[(o(!0),d(V,null,S(U.value,e=>(o(),d("div",{key:e.id,class:Te(["speech-chapter-item",{active:h.value&&h.value.id===e.id}]),onClick:w=>H(e)},[s("div",St,c(e.chapterTitle),1),s("div",Nt,[s("div",Ut,c(z(e.startTime))+" - "+c(z(e.endTime)),1),t(n,{type:"primary",size:"small",circle:"",class:"jump-button",onClick:se(w=>H(e),["stop"])},{default:a(()=>[t(ee,null,{default:a(()=>[t(oe(ne))]),_:1})]),_:2},1032,["onClick"])])],10,Vt))),128)),!P.value&&U.value.length===0?(o(),k(L,{key:0,description:"暂无章节段落"})):b("",!0)]),_:1})]),s("div",Tt,[s("div",Ot,[s("h4",null,c(h.value?h.value.chapterTitle:"对话内容"),1),B.value?(o(),k(q,{key:0,type:"info",size:"small"},{default:a(()=>[p("加载中...")]),_:1})):b("",!0)]),t(le,{height:"400px"},{default:a(()=>[h.value&&h.value.chapterSummary?(o(),d("div",xt,[Jt,s("div",Ct,c(h.value.chapterSummary),1)])):b("",!0),(o(!0),d(V,null,S(C.value,e=>(o(),d("div",{key:e.id,class:"speech-record-item",onClick:w=>Q(e.startTime)},[s("div",zt,[s("div",It,[t(q,{size:"small",type:ge(e.speakerId)},{default:a(()=>[p(c(e.speakerName||"发言人0"),1)]),_:2},1032,["type"])]),s("div",Dt,[p(c(z(e.startTime))+" - "+c(z(e.endTime))+" ",1),t(n,{type:"primary",size:"small",circle:"",class:"jump-button-small",onClick:se(w=>Q(e.startTime),["stop"])},{default:a(()=>[t(ee,null,{default:a(()=>[t(oe(ne))]),_:1})]),_:2},1032,["onClick"])])]),s("div",$t,c(e.content),1)],8,qt))),128)),!B.value&&(!h.value||C.value.length===0)?(o(),k(L,{key:1,description:"暂无对话内容"})):b("",!0)]),_:1})])])]),_:1})):b("",!0),t(te,{label:"问答回顾",name:"qa"},{default:a(()=>[s("div",Rt,[F.value?(o(),d("div",Ft,[t(be,{rows:5,animated:""})])):x.value.length===0?(o(),d("div",Mt,[t(L,{description:"暂无问答记录"})])):(o(),d("div",Yt,[t(we,null,{default:a(()=>[(o(!0),d(V,null,S(x.value,(e,w)=>(o(),k(ke,{key:w,timestamp:Z(e.createTime),placement:"top",type:w%2===0?"primary":"success"},{default:a(()=>[t(X,{class:"qa-card"},{default:a(()=>[s("div",Ht,[s("div",Lt,"问题 "+c(e.questionIndex+1)+":",1),s("div",Et,c(e.question),1)]),s("div",Wt,[jt,s("div",Pt,c(e.transcription),1)])]),_:2},1024)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]))])]),_:1})]),_:1},8,["modelValue"])]),t(A,{"label-width":"100px"},{default:a(()=>[t(f,{label:"总体评分"},{default:a(()=>[t(Se,{modelValue:m.score,"onUpdate:modelValue":l[28]||(l[28]=e=>m.score=e),min:0,max:100,step:1,"show-input":"",style:{width:"100%"},disabled:""},null,8,["modelValue"])]),_:1}),t(f,{label:"面试反馈"},{default:a(()=>[t(T,{modelValue:m.feedback,"onUpdate:modelValue":l[29]||(l[29]=e=>m.feedback=e),type:"textarea",rows:3,placeholder:"请输入面试整体反馈",disabled:""},null,8,["modelValue"])]),_:1}),t(f,{label:"优势表现"},{default:a(()=>[t(T,{modelValue:m.strengths,"onUpdate:modelValue":l[30]||(l[30]=e=>m.strengths=e),type:"textarea",rows:3,placeholder:"请输入应聘者的优势表现",disabled:""},null,8,["modelValue"])]),_:1}),t(f,{label:"改进建议"},{default:a(()=>[t(T,{modelValue:m.improvements,"onUpdate:modelValue":l[31]||(l[31]=e=>m.improvements=e),type:"textarea",rows:3,placeholder:"请输入应聘者的改进建议",disabled:""},null,8,["modelValue"])]),_:1}),t(Ue,null,{default:a(()=>[p("预览")]),_:1}),t(Oe,{score:m.score,feedback:m.feedback,strengths:m.strengths,improvements:m.improvements,result:m.result},null,8,["score","feedback","strengths","improvements","result"])]),_:1})]),_:1},8,["modelValue"])])}}},Kt=ll(Bt,[["__scopeId","data-v-b9baeb86"]]);export{Kt as default};
