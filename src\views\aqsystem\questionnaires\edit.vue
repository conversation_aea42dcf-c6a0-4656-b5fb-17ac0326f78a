<template>
  <div class="app-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑问卷' : '创建问卷' }}</h1>
      <div class="header-actions">
        <el-button type="primary" icon="Check" @click="handleSave">保存</el-button>
        <el-button icon="Close" @click="handleCancel">取消</el-button>
      </div>
    </div>

    <!-- 问卷基本信息 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
        </div>
      </template>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问卷名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入问卷名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="问卷类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择问卷类型" class="full-width">
                <el-option
                  v-for="type in questionnaireTypes"
                  :key="type.labelId"
                  :label="type.labelName"
                  :value="type.labelId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                class="full-width"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                class="full-width"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="问卷说明" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入问卷说明"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 题目管理 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>题目管理</span>
        </div>
      </template>
      <el-row :gutter="20">
        <!-- 题库区域 -->
        <el-col :span="16">
          <div class="question-bank">
            <div class="question-bank-header">
              <h3>题库</h3>
              <div class="question-bank-actions">
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索题目..."
                  prefix-icon="Search"
                  clearable
                  class="search-input"
                />
              </div>
            </div>

            <div class="question-list">
              <el-empty v-if="!loading && questionBank.length === 0" description="暂无题目" />
              <el-skeleton :loading="loading" animated :count="3" v-else-if="loading">
                <template #template>
                  <div class="question-item">
                    <el-skeleton-item variant="text" style="width: 30%" />
                    <el-skeleton-item variant="text" style="width: 100%" />
                  </div>
                </template>
              </el-skeleton>
              <div
                v-else
                v-for="question in filteredQuestions"
                :key="question.questionsId"
                class="question-item"
                :class="{ 'selected': isQuestionSelected(question) }"
                :style="{ 
                  borderLeft: `4px solid ${getQuestionTypeStyle(question.type).color}`,
                  backgroundColor: isQuestionSelected(question) ? getQuestionTypeStyle(question.type).bgColor : 'white'
                }"
                @click.stop="toggleQuestion(question)"
              >
                <div class="question-item-header">
                  <div class="question-type">
                    <el-tag
                      :type="getQuestionTypeTag(question.type)"
                      size="small"
                      class="clickable-tag"
                      :style="{ 
                        backgroundColor: getQuestionTypeStyle(question.type).bgColor,
                        color: getQuestionTypeStyle(question.type).color,
                        borderColor: getQuestionTypeStyle(question.type).color
                      }"
                      @click.stop="filterByType(question.type)"
                    >{{ question.type }}</el-tag>
                    <el-tag size="small" type="info" class="topic-tag">{{ question.labelName }}</el-tag>
                    <span class="question-id">ID: {{ question.questionsId }}</span>
                  </div>
                </div>
                <div class="question-content">
                  {{ question.content }}
                  <el-tag
                    v-if="isQuestionSelected(question)"
                    type="success"
                    size="small"
                    class="selected-tag"
                  >已选择</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 已选题目区域 -->
        <el-col :span="8">
          <div class="selected-questions">
            <div class="selected-questions-header">
              <h3>已选试题</h3>
            </div>

            <div v-if="form.questions.length === 0" class="empty-state">
              <el-icon :size="48"><Document /></el-icon>
              <p>还没有选择任何试题</p>
              <p class="hint-text">从左侧题库中点击题目添加</p>
            </div>

            <draggable
              v-else
              v-model="form.questions"
              item-key="questionsId"
              class="selected-questions-list"
            >
              <template #item="{ element }">
                <div class="selected-question-item">
                  <div class="selected-question-content">
                    <el-tag size="small" class="question-type-tag">
                      {{ getQuestionTypeText(element.type) }}
                    </el-tag>
                    <el-button
                      type="danger"
                      link
                      icon="Delete"
                      class="delete-btn"
                      @click="handleDeleteQuestion(element)"
                    />
                  </div>
                  <div class="selected-question-title">{{ element.content }}</div>
                </div>
              </template>
            </draggable>

            <div class="selected-questions-actions">
              <el-button type="danger" plain @click="clearSelectedQuestions">
                清空
              </el-button>
              <el-button type="primary" plain @click="handleRandomSort">
                随机排序
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { addQuestionnaire, listQuestion,listLabel,getQuestionnaireDetailPage,updateQuestionnaire } from '@/api/aqsystem/questionnaire'
import { getCategoryList } from '@/api/aqsystem/questions'

const route = useRoute()
const router = useRouter()

// 添加formRef的定义
const formRef = ref(null)

// 是否编辑模式
const isEdit = ref(JSON.parse(JSON.stringify(false)))

// 表单数据
const form = reactive({
  id: undefined,
  title: '',
  type: undefined,
  lableId: undefined,
  classId: 1,
  startTime: '',
  endTime: '',
  duration: 5,
  description: '',
  questions: [],
  settings: {
    allowAnonymous: true,
    allowModify: false,
    showProgress: true,
    showStatistics: true
  }
})

// 表单校验规则
const rules = {
  title: [
    { required: true, message: '请输入问卷名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择问卷类型', trigger: 'change' }
  ],
  lableId: [
    { required: true, message: '请选择问卷类型', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 问卷类型选项
const questionnaireTypes = ref(JSON.parse(JSON.stringify([])))

// 获取问卷类型列表
const getQuestionnaireTypes = async () => {
  try {
    const response = await listLabel()
    if (response && response.code === 200 && response.rows) {
      questionnaireTypes.value = response.rows
    } else {
      ElMessage.error('获取问卷类型失败')
    }
  } catch (error) {
    ElMessage.error('获取问卷类型失败')
  }
}

// 学科选项
const subjectOptions = ref(JSON.parse(JSON.stringify([])))

// 获取学科分类列表
const getSubjectList = async () => {
  try {
    const response = await getCategoryList()
    subjectOptions.value = response.map(item => ({
      value: item.id,
      label: item.name
    }))
  } catch (error) {
    console.error('获取学科分类失败:', error)
    ElMessage.error('获取学科分类失败')
  }
}

// 选中的学科
const selectedSubject = ref(JSON.parse(JSON.stringify('')))

// 题目类型选项
const questionTypes = ref(JSON.parse(JSON.stringify([])))

// 添加日期格式化函数
const formatDateTime = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 根据类型名称返回对应的标签类型
function getTypeTag(typeName) {
  const typeMap = {
    '单选题': 'success',
    '多选题': 'primary',
    '填空题': 'warning',
    '简答题': 'info',
    '不定项': 'danger'
  }
  return typeMap[typeName] || 'info'
}

// 题目类型样式映射
const questionTypeStyles = {
  '单选题': { type: 'success', color: '#67C23A', bgColor: '#f0f9eb' },
  '多选题': { type: 'primary', color: '#409EFF', bgColor: '#ecf5ff' },
  '填空题': { type: 'warning', color: '#E6A23C', bgColor: '#fdf6ec' },
  '简答题': { type: 'info', color: '#909399', bgColor: '#f4f4f5' },
  '不定项': { type: 'danger', color: '#F56C6C', bgColor: '#fef0f0' }
}

// 获取题目类型标签样式
function getQuestionTypeTag(type) {
  return questionTypeStyles[type]?.type || 'info'
}

// 获取题目类型样式
function getQuestionTypeStyle(type) {
  return questionTypeStyles[type] || questionTypeStyles['简答题']
}

// 获取题目类型文本
function getQuestionTypeText(type) {
  const typeObj = questionTypes.value.find(t => t.value === type)
  return typeObj?.label || type
}

// 搜索查询
const searchQuery = ref('')

// 题库数据
const questionBank = ref(JSON.parse(JSON.stringify([])))
const loading = ref(false)
const selectedQuestionContents = ref(new Set())

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  topicClassification: '',
  content: ''
})

// 获取题库列表
const getQuestionList = async () => {
  try {
    loading.value = true
    const response = await listQuestion(queryParams.value)
    const currentSelectedContents = Array.from(selectedQuestionContents.value)
    
    questionBank.value = (response.rows || []).map(question => {
      const processedQuestion = {
        ...question,
        questionsId: String(question.questionsId)
      }
      
      const isAlreadySelected = currentSelectedContents.includes(processedQuestion.content)
      
      return processedQuestion
    })
  } catch (error) {
    ElMessage.error('获取题库列表失败')
  } finally {
    loading.value = false
  }
}

// 监听搜索条件变化
watch([selectedSubject, searchQuery], () => {
  queryParams.value.topicClassification = selectedSubject.value
  queryParams.value.content = searchQuery.value
  getQuestionList()
}, { immediate: true })

// 过滤后的题目列表
const filteredQuestions = computed(() => {
  return questionBank.value
})

// 切换题目选择状态
function toggleQuestion(question) {
  if (selectedQuestionContents.value.has(question.content)) {
    selectedQuestionContents.value.delete(question.content)
    const index = form.questions.findIndex(q => q.content === question.content)
    if (index !== -1) {
      form.questions.splice(index, 1)
    }
    ElMessage.success('移除题目成功')
  } else {
    selectedQuestionContents.value.add(question.content)
    const newQuestion = {
      questionsId: String(question.questionsId),
      title: question.content,
      type: question.type,
      category: question.labelName,
      content: question.content,
      options: question.options ? JSON.parse(question.options) : null,
      answer: question.answer,
      analysis: question.analysis,
      cueword: question.cueword,
      required: question.required === 1
    }
    form.questions.push(newQuestion)
    ElMessage.success('添加题目成功')
  }
}

// 判断题目是否已选
function isQuestionSelected(question) {
  return selectedQuestionContents.value.has(question.content)
}

// 删除题目
function handleDeleteQuestion(question) {
  const content = question.content
  const index = form.questions.findIndex(q => q.content === content)
  if (index !== -1) {
    form.questions.splice(index, 1)
    selectedQuestionContents.value.delete(content)
    ElMessage.success('删除题目成功')
  }
}

// 清空已选题目
function clearSelectedQuestions() {
  if (form.questions.length === 0) {
    ElMessage.info('暂无已选题目')
    return
  }
  ElMessageBox.confirm('确定要清空所有已选题目吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    form.questions = []
    selectedQuestionContents.value.clear()
    ElMessage.success('已清空所有题目')
  }).catch(() => {})
}

// 随机排序题目
function handleRandomSort() {
  if (form.questions.length === 0) {
    ElMessage.info('暂无已选题目')
    return
  }
  const questions = [...form.questions]
  for (let i = questions.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[questions[i], questions[j]] = [questions[j], questions[i]]
  }
  form.questions = questions
  ElMessage.success('题目顺序已随机打乱')
}

// 添加筛选方法
function filterByType(type) {
  queryParams.value.questionType = type
  getQuestionList()
}

// 获取问卷详情
const getQuestionnaireDetail = async () => {
  const questionnaireId = route.query.questionnaireId
  
  if (!questionnaireId) {
    return
  }
  
  isEdit.value = true
  try {
    const response = await getQuestionnaireDetailPage(questionnaireId)
    
    if (response.code === 200 && response.data) {
      const questionnaireData = response.data
      form.id = questionnaireData.questionnaireId
      form.title = questionnaireData.title
      form.type = questionnaireData.lableId
      form.description = questionnaireData.description
      form.startTime = questionnaireData.startTime
      form.endTime = questionnaireData.endTime
      form.duration = questionnaireData.duration || 5
      form.classId = questionnaireData.classId

      if (questionnaireData.questionsList && questionnaireData.questionsList.length > 0) {
        form.questions = questionnaireData.questionsList.map(question => {
          const processedQuestion = {
            questionsId: String(question.questionsId),
            title: question.content,
            type: question.type,
            category: question.labelName,
            content: question.content,
            options: question.options ? JSON.parse(question.options) : null,
            answer: question.answer,
            analysis: question.analysis,
            cueword: question.cueword,
            required: question.required === 1
          }
          return processedQuestion
        })
        
        selectedQuestionContents.value = new Set(form.questions.map(q => q.content))
        await getQuestionList()
      }
    }
  } catch (error) {
    ElMessage.error('获取问卷详情失败')
  }
}

// 修改保存问卷方法
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    const saveData = {
      questionnaireId: form.id,
      title: form.title,
      lableId: form.type,
      description: form.description,
      classId: form.classId,
      startTime: formatDateTime(form.startTime),
      endTime: formatDateTime(form.endTime),
      duration: form.duration,
      questions: form.questions.map(q => ({
        questionsId: q.questionsId,
        content: q.content,
        image: null,
        cueword: q.cueword || null,
        type: q.type,
        labelId: 2,
        points: 1,
        options: q.options ? JSON.stringify(q.options) : null,
        answer: q.answer,
        analysis: q.analysis,
        randomly: 0,
        required: q.required ? 1 : 0
      }))
    }
    
    if (isEdit.value) {
      const response = await updateQuestionnaire(saveData)
      if (response.code === 200) {
        ElMessage.success('修改成功')
        router.push('/aqsystem/questionnaires/questionnaires')
      } else {
        ElMessage.error(response.msg || '保存失败')
      }
    } else {
      const response = await addQuestionnaire(saveData)
      if (response.code === 200) {
        ElMessage.success('新增成功')
        router.push('/aqsystem/questionnaires/questionnaires')
      } else {
        ElMessage.error(response.msg || '保存失败')
      }
    }
  } catch (error) {
    ElMessage.error(error.message || '保存失败')
  }
}

// 取消编辑
const handleCancel = () => {
  router.push('/aqsystem/questionnaires')
}

onMounted(() => {
  getSubjectList()
  getQuestionnaireTypes()
  if (route.query.questionnaireId) {
    getQuestionnaireDetail()
  }
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.full-width {
  width: 100%;
}

.question-bank {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.question-bank-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-bank-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.question-bank-actions {
  display: flex;
  gap: 8px;
}

.subject-select {
  width: 120px;
}

.search-input {
  width: 200px;
}

.question-list {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  max-height: 600px;
  overflow-y: auto;
}

.question-item {
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-left-width: 4px;
  margin-bottom: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.question-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-item.selected {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.question-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.question-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-id {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  font-family: monospace;
}

.question-content {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  margin-top: 8px;
  padding-left: 4px;
}

.label-name {
  color: #909399;
  font-size: 13px;
  margin-left: 4px;
}

.selected-questions {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.selected-questions-header {
  margin-bottom: 16px;
}

.selected-questions-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.empty-state {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 32px;
  text-align: center;
  color: #909399;
}

.empty-state p {
  margin: 8px 0 0;
}

.hint-text {
  font-size: 12px;
  color: #909399;
  margin: 4px 0 0;
}

.selected-questions-list {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.selected-question-item {
  padding: 12px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-question-item:last-child {
  border-bottom: none;
}

.selected-question-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.selected-question-title {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.delete-btn {
  margin-left: auto;
  flex-shrink: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.question-type-tag {
  flex-shrink: 0;
}

.selected-questions-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.topic-tag {
  margin-left: 8px;
  background-color: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
  font-weight: 500;
}

.topic-tag:hover {
  background-color: #409EFF;
  color: white;
  transition: all 0.3s;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.clickable-tag:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.selected-tag {
  margin-left: 8px;
  font-size: 12px;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  background-color: #67C23A;
  color: white;
  border-radius: 4px;
}
</style> 