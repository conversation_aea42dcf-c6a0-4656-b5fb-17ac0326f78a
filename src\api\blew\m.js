import request from '@/utils/request'

// 分页查询面试列表 /interview/page
export function interviewPage(query) {
  return request({
    url: '/interview/page',
    method: 'post',
    data: query
  })
}

// 获取面试详情 /interview/detail/{id}
export function interviewDetail(id) {
  return request({
    url: '/interview/detail/' + id,
    method: 'get'
  })
}
// 就业老师评分反馈 /interview/feedback
export function interviewFeedback(data) {
  return request({
    url: '/interview/feedback',
    method: 'PUT',
    data: data
  })
}
// 获取面试问答记录 /interview/transcription/{interviewId}
export function interviewTranscription(interviewId) {
  return request({
    url: '/interview/transcription/' + interviewId,
    method: 'get'
  })
}



