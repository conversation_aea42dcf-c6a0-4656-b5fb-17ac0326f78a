<template>
  <div class="app-container">
    <div class="page-header">
      <h1 class="page-title">{{ form.questions_id ? '编辑题目' : '添加题目' }}</h1>
      <div class="header-actions">
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="form-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="custom-form">
        <el-form-item label="题目类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择题目类型" class="form-select">
            <el-option
              v-for="dict in questionTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="问卷类型" prop="label_name">
          <el-select v-model="form.label_name" placeholder="请选择问卷类型" class="form-select">
            <el-option
              v-for="dict in labelOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="3"
            placeholder="请输入题目内容"
            class="content-input"
          />
        </el-form-item>

        <el-form-item label="提示文字" prop="cueword">
          <el-input
            v-model="form.cueword"
            type="textarea"
            :rows="2"
            placeholder="请输入提示文字"
            class="cueword-input"
          />
        </el-form-item>

        <!-- 选项区域 -->
        <div v-if="['单选题', '多选题', '判断题', '排序题'].includes(form.type)" class="options-section">
          <div class="options-header">
            <span class="options-title">选项列表</span>
            <el-button type="primary" link @click="addOption" class="add-option-btn">
              <el-icon><Plus /></el-icon>添加选项
            </el-button>
          </div>
          
          <el-form-item
            v-for="(option, index) in form.options"
            :key="index"
            :prop="'options.' + index + '.content'"
            :rules="{
              required: true,
              message: '选项内容不能为空',
              trigger: 'blur'
            }"
            class="option-form-item"
          >
            <div class="option-item">
              <div class="option-label">{{ String.fromCharCode(65 + index) }}</div>
              <el-input
                v-model="option.content"
                :placeholder="'请输入选项内容'"
                class="option-input"
              />
              <el-button
                type="danger"
                link
                @click="removeOption(index)"
                class="delete-option-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </div>

        <el-form-item label="答案解析" prop="analysis">
          <el-input
            v-model="form.analysis"
            type="textarea"
            :rows="3"
            placeholder="请输入答案解析"
            class="analysis-input"
          />
        </el-form-item>

        <el-form-item class="form-footer">
          <el-button type="primary" @click="submitForm" class="submit-btn">保存题目</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { addQuestion,listLabel,getQuestionDetail,updateQuestion } from '@/api/aqsystem/questionnaire' 
const route = useRoute()
const router = useRouter()
const formRef = ref(null)

// 表单数据
const form = reactive({
  questions_id: undefined,
  type: '',
  label_name: '',
  content: '',
  options: [],
  analysis: '',
  cueword: ''
})

// 表单校验规则
const rules = {
  type: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
  label_name: [{ required: true, message: '请选择问卷类型', trigger: 'change' }],
  content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }]
}

// 题目类型选项
const questionTypeOptions = ref([
  { value: '单选题', label: '单选题' },
  { value: '多选题', label: '多选题' },
  { value: '判断题', label: '判断题' },
  { value: '填空题', label: '填空题' },
  { value: '简答题', label: '简答题' },
  { value: '排序题', label: '排序题' }
])

// 题目标签选项（问卷类型）
const labelOptions = ref([])

// 获取问卷类型列表
const getLabelList = async () => {
  try {
    const response = await listLabel()
    if (response.code === 200) {
      if (response.rows) {
        labelOptions.value = response.rows.map(item => ({
          value: item.labelId,
          label: item.labelName
        }))
      } else if (response.data) {
        labelOptions.value = response.data.map(item => ({
          value: item.labelId,
          label: item.labelName
        }))
      } else {
        ElMessage.warning('问卷类型列表数据格式不正确')
      }
    } else {
      ElMessage.error(response.msg || '获取问卷类型列表失败')
    }
  } catch (error) {
    console.error('获取问卷类型列表失败:', error)
    ElMessage.error('获取问卷类型列表失败')
  }
}

// 获取题目详情
const getQuestionDetailData = async (id) => {
  try {
    const response = await getQuestionDetail(id)
    if (response.code === 200 && response.data) {
      const data = response.data
      form.questions_id = data.questionsId
      form.type = data.type
      form.label_name = data.labelId
      form.content = data.content
      form.analysis = data.analysis
      form.cueword = data.cueword || ''
      
      // 处理选项数据
      if (data.options) {
        try {
          const optionsObj = JSON.parse(data.options)
          // 将选项对象转换为数组格式
          form.options = Object.entries(optionsObj).map(([key, value]) => ({
            content: value
          }))
        } catch (e) {
          console.error('解析选项数据失败:', e)
          form.options = []
        }
      } else {
        // 如果是判断题，添加默认选项
        if (data.type === '判断题') {
          form.options = [
            { content: '正确' },
            { content: '错误' }
          ]
        } else {
          form.options = []
        }
      }
    } else {
      ElMessage.error(response.msg || '获取题目详情失败')
    }
  } catch (error) {
    console.error('获取题目详情失败:', error)
    ElMessage.error('获取题目详情失败')
  }
}

// 监听题目类型变化
watch(() => form.type, (newType) => {
  // 如果是编辑模式且有选项数据，不重置选项
  if (form.questions_id && form.options.length > 0) {
    return
  }

  // 新增模式下的处理
  if (['单选题', '多选题', '排序题'].includes(newType)) {
    // 单选题、多选题或排序题，清空选项并添加两个空选项
    form.options = []
    addOption()
    addOption()
  } else if (newType === '判断题') {
    // 判断题，添加默认选项
    form.options = [
      { content: '正确' },
      { content: '错误' }
    ]
  } else {
    // 其他类型（填空题、简答题），清空选项
    form.options = []
  }
}, { immediate: true })

// 添加选项
const addOption = () => {
  form.options.push({
    content: ''
  })
}

// 删除选项
const removeOption = (index) => {
  form.options.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 将选项数组转换为所需的格式
        const optionsObj = {}
        form.options.forEach((option, index) => {
          const key = String.fromCharCode(65 + index) // A, B, C, D...
          optionsObj[key] = option.content
        })
        
        // 构造请求参数
        const params = {
          questionsId: form.questions_id,
          content: form.content,
          image: null,
          cueword: form.cueword || '',
          type: form.type,
          labelId: form.label_name,
          points: 1,
          options: form.options.length > 0 ? JSON.stringify(optionsObj) : null,
          answer: null,
          analysis: form.analysis || null,
          randomly: 0,
          required: 1,
          questionnaireId: 'Q002'
        }
        
        let response
        if (form.questions_id) {
          // 编辑模式，调用更新接口
          response = await updateQuestion(params)
        } else {
          // 新增模式，调用添加接口
          response = await addQuestion(params)
        }
        
        if (response.code === 200) {
          ElMessage.success('保存成功')
          goBack()
        } else {
          ElMessage.error(response.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存题目失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  // 获取问卷类型列表
  getLabelList()
  
  // 如果是编辑模式，获取题目详情
  if (route.query.id) {
    getQuestionDetailData(route.query.id)
  }
})
</script>

<style scoped>
.app-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.form-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.custom-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-select {
  width: 100%;
}

.content-input,
.analysis-input {
  width: 100%;
}

.options-section {
  margin: 24px 0;
  padding: 16px;
  border-radius: 6px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.options-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.add-option-btn {
  padding: 8px 16px;
}

.option-form-item {
  margin-bottom: 16px;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.drag-handle {
  cursor: move;
  color: #909399;
  margin-right: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.option-label {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background-color: #f0f2f5;
  border-radius: 50%;
  font-weight: 500;
  color: #606266;
}

.option-input {
  flex: 1;
}

.delete-option-btn {
  margin-left: 8px;
  color: #f56c6c;
}

.form-footer {
  margin-top: 32px;
  text-align: center;
}

.submit-btn {
  padding: 12px 24px;
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  border-color: #66b1ff;
}
</style> 